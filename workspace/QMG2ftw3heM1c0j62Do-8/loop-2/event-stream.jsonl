[{"id": "569c0451-90c7-48b3-bc23-0197b7e79efa", "type": "user_message", "timestamp": 1753892252891, "content": "Search for the latest GUI Agent papers"}, {"id": "ffe2949a-709d-495a-a487-18b21b034b78", "type": "plan_start", "timestamp": 1753892252893, "sessionId": "1753892252891-yll0yfy"}, {"id": "b46794e6-e501-43d0-86b5-da8cd03fbdbc", "type": "agent_run_start", "timestamp": 1753892252891, "sessionId": "1753892252891-yll0yfy", "runOptions": {"input": "Search for the latest GUI Agent papers", "stream": true}, "provider": "volcengine", "model": "doubao-1-5-thinking-vision-pro-250428"}, {"id": "287a2b13-2c71-432e-a04a-3655b928819e", "type": "plan_update", "timestamp": 1753892256003, "sessionId": "1753892252891-yll0yfy", "steps": [{"content": "Visit academic databases like ACM Digital Library, IEEE Xplore, or arXiv. Use keywords such as 'GUI agent', 'graphical user interface agent', and limit the search to the latest (e.g., past 1 - 2 years) publications.", "done": false}, {"content": "Check research platforms like Google Scholar. Use advanced search features to filter by publication date and relevant keywords to find the most recent GUI agent - related papers.", "done": false}, {"content": "Explore specialized AI and HCI (Human - Computer Interaction) conferences' websites, such as CHI, UIST, or AIES. Look for their latest proceedings or upcoming publications related to GUI agents.", "done": false}]}, {"id": "0c1800f4-ac70-45cb-8045-39bd2b3af4b7", "type": "assistant_streaming_message", "timestamp": 1753892257116, "content": "用户", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "743980b3-b526-406c-8e67-a6d8874dcd26", "type": "assistant_streaming_message", "timestamp": 1753892257116, "content": "现在", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "3f86a7bc-e35c-423d-84da-ed3abcda1160", "type": "assistant_streaming_message", "timestamp": 1753892257116, "content": "需要", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "4e14c409-fc40-46fe-9fcd-e9ca94ede49f", "type": "assistant_streaming_message", "timestamp": 1753892257234, "content": "执行", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "d20e7f02-5b36-47bc-bc3e-6df1ca399065", "type": "assistant_streaming_message", "timestamp": 1753892257234, "content": "第一步", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "d6a63797-055f-4b7d-989e-9f17f55b56ae", "type": "assistant_streaming_message", "timestamp": 1753892257235, "content": "计划", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "38ac52db-c4c1-452d-9bfd-318a3e71bd7e", "type": "assistant_streaming_message", "timestamp": 1753892257235, "content": "，", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "6910bda0-fd6c-4438-9d04-8d1b4ea58b4a", "type": "assistant_streaming_message", "timestamp": 1753892257315, "content": "搜索", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "ddb2f4cd-d1ca-46bc-8794-2f7d3b163707", "type": "assistant_streaming_message", "timestamp": 1753892257316, "content": "最新", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "481b70e6-5b09-4a75-861b-c96450781d14", "type": "assistant_streaming_message", "timestamp": 1753892257316, "content": "的", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "5774eda2-5100-4444-b717-a3810cf2c549", "type": "assistant_streaming_message", "timestamp": 1753892257316, "content": "GUI", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "dbdc3176-1d01-429b-8dbf-855ae286c799", "type": "assistant_streaming_message", "timestamp": 1753892257434, "content": " Agent", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "7e7b17e2-bd87-4d14-8761-cc96d9079855", "type": "assistant_streaming_message", "timestamp": 1753892257437, "content": "论文", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "78d0123b-9cd4-4b17-a668-57a7f278bfa2", "type": "assistant_streaming_message", "timestamp": 1753892257438, "content": "。", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "40385166-d5bd-4555-948b-9cbe1981bfe7", "type": "assistant_streaming_message", "timestamp": 1753892257442, "content": "首先", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "abf9917c-5f76-4c8b-98a6-06aa1146671f", "type": "assistant_streaming_message", "timestamp": 1753892257560, "content": "选择", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "d4ed36cf-7f36-4508-8fec-bf6cbcae5000", "type": "assistant_streaming_message", "timestamp": 1753892257561, "content": "web", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "49373ef7-a5ff-4c6e-9982-b98a83c3336b", "type": "assistant_streaming_message", "timestamp": 1753892257561, "content": "_search", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "c5ae60a7-ba06-4047-ac48-cbbb98be5631", "type": "assistant_streaming_message", "timestamp": 1753892257561, "content": "工具", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "3e6ffe4a-95d3-466c-b390-ff3e5bd6b27b", "type": "assistant_streaming_message", "timestamp": 1753892257638, "content": "，", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "f06f4f32-ae8b-4fc1-8c1b-fbc197aa945f", "type": "assistant_streaming_message", "timestamp": 1753892257639, "content": "因为", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "51e7a7e3-37f3-4d23-9b5e-2e32da4344e3", "type": "assistant_streaming_message", "timestamp": 1753892257640, "content": "要", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "60a409ff-995e-4810-a222-61850d5a0da6", "type": "assistant_streaming_message", "timestamp": 1753892257640, "content": "获取", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "acab4fc8-a975-4729-8995-144b20260aa2", "type": "assistant_streaming_message", "timestamp": 1753892257754, "content": "网络", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "a58eede2-e367-4014-b724-83ede7b7fc1e", "type": "assistant_streaming_message", "timestamp": 1753892257759, "content": "信息", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "ff796136-5395-4889-9b66-10a9fdca43ff", "type": "assistant_streaming_message", "timestamp": 1753892257759, "content": "。", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "3e136197-e3d7-4e50-9a78-ed37a1f14b38", "type": "assistant_streaming_message", "timestamp": 1753892257760, "content": "查询", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "ba16c297-f1ef-47c2-81d7-3d58ee9a48bd", "type": "assistant_streaming_message", "timestamp": 1753892257846, "content": "需要", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "4db5dbb3-2380-4514-8193-0a3f962949b0", "type": "assistant_streaming_message", "timestamp": 1753892257846, "content": "简洁", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "d7beef53-e838-4cac-b2ea-7a203f02b304", "type": "assistant_streaming_message", "timestamp": 1753892257847, "content": "，", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "679190a5-5400-463a-827d-0c46baa38ea6", "type": "assistant_streaming_message", "timestamp": 1753892257847, "content": "控制", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "df9b96e1-c0fc-4b31-9b58-5cda2c90c85b", "type": "assistant_streaming_message", "timestamp": 1753892257922, "content": "在", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "ef89e77f-51d7-478e-b3c8-09997864f85d", "type": "assistant_streaming_message", "timestamp": 1753892257923, "content": "7", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "0317d4db-52df-49f8-b844-6a07cb8294fb", "type": "assistant_streaming_message", "timestamp": 1753892257924, "content": "词", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "f30a90ac-d8ae-40d1-a293-a9c35c282d38", "type": "assistant_streaming_message", "timestamp": 1753892257924, "content": "内", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "0480effb-79d5-4e1d-9b25-5714db232d3e", "type": "assistant_streaming_message", "timestamp": 1753892258051, "content": "，", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "59dd44c5-e400-40c5-b34e-167747646953", "type": "assistant_streaming_message", "timestamp": 1753892258052, "content": "所以", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "5439e4c0-aa78-48be-a9b8-c2af8ec92917", "type": "assistant_streaming_message", "timestamp": 1753892258053, "content": "用", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "a158e6ca-aba6-499f-933d-be8da8cfc6ca", "type": "assistant_streaming_message", "timestamp": 1753892258053, "content": "“", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "bbc200c2-f2f9-4ba4-b595-a831e41d3f3a", "type": "assistant_streaming_message", "timestamp": 1753892258130, "content": "latest", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "c992ec4b-5363-4ce0-af1b-b7d7af5a44da", "type": "assistant_streaming_message", "timestamp": 1753892258133, "content": " GUI", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "7ffbf037-0860-4dc0-9de5-2a807fc34fc1", "type": "assistant_streaming_message", "timestamp": 1753892258133, "content": " Agent", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "d9355948-a7d3-4e32-818f-4f71753c51e7", "type": "assistant_streaming_message", "timestamp": 1753892258134, "content": " papers", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "74c25de9-5d03-4276-918f-87b3eff3b5a4", "type": "assistant_streaming_message", "timestamp": 1753892258260, "content": "”", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "85d3eadd-758e-4541-9568-b78e2f14a49d", "type": "assistant_streaming_message", "timestamp": 1753892258261, "content": "作为", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "d7701b6b-1af3-4a51-bbaf-e89b0a628b46", "type": "assistant_streaming_message", "timestamp": 1753892258262, "content": "查询", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "9993516a-10cb-4ce0-ae6c-15b8dcf841ea", "type": "assistant_streaming_message", "timestamp": 1753892258262, "content": "词", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "f9289275-7096-4a55-bc86-d47949b9de63", "type": "assistant_streaming_message", "timestamp": 1753892258347, "content": "，", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "dc72f1ee-baa3-445c-bd77-dd4fca1c9f7a", "type": "assistant_streaming_message", "timestamp": 1753892258349, "content": "count", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "1404f77b-c51e-4ca4-9914-16601aa73346", "type": "assistant_streaming_message", "timestamp": 1753892258349, "content": "设", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "ef9b991b-661d-4cc5-bc66-3d222e206062", "type": "assistant_streaming_message", "timestamp": 1753892258349, "content": "为", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "32dc56e8-6d7c-4642-b31a-6c102e225303", "type": "assistant_streaming_message", "timestamp": 1753892258427, "content": "1", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "64497f5a-20f1-4f39-a520-e0e8b564f96b", "type": "assistant_streaming_message", "timestamp": 1753892258428, "content": "0", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "6789b4a8-34d6-4e84-ba39-70c9814663cf", "type": "assistant_streaming_message", "timestamp": 1753892258428, "content": "获取", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "571a3685-bf1d-4555-9f40-77c0611d10cb", "type": "assistant_streaming_message", "timestamp": 1753892258429, "content": "足够", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "158fe87f-1825-4e32-9c4c-245c9cd8447e", "type": "assistant_streaming_message", "timestamp": 1753892258800, "content": "结果", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "8cb4286f-9333-497b-a2ad-4fc85826a9a5", "type": "assistant_streaming_message", "timestamp": 1753892258801, "content": "。", "isComplete": false, "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "fad5c967-efbc-4c7e-b2d6-4ab1f40f7301", "type": "assistant_message", "timestamp": 1753892258974, "content": "用户现在需要执行第一步计划，搜索最新的GUI Agent论文。首先选择web_search工具，因为要获取网络信息。查询需要简洁，控制在7词内，所以用“latest GUI Agent papers”作为查询词，count设为10获取足够结果。", "toolCalls": [{"id": "call_3to2wwqi2wy6k1vepj0n7ge1", "type": "function", "function": {"name": "web_search", "arguments": "{\"count\":10.0,\"query\":\"latest GUI Agent papers\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1753892256939_x1ev8a86"}, {"id": "cbbfff44-188b-4830-9ca3-bb3d6de0e74b", "type": "tool_call", "timestamp": 1753892258975, "toolCallId": "call_3to2wwqi2wy6k1vepj0n7ge1", "name": "web_search", "arguments": {"count": 10, "query": "latest GUI Agent papers"}, "startTime": 1753892258975, "tool": {"name": "web_search", "description": "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️\n\nSearch the web for information. For best results:\n1) Use CONCISE queries (3-5 words ideal)\n2) Include only ESSENTIAL keywords, not full questions\n3) For complex topics, use multiple simple searches instead of one long query\n4) Focus on specific terms that will appear on relevant pages", "schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query - MUST BE CONCISE (maximum 7 words)"}, "count": {"type": "number", "description": "Number of results to return (default: 10)"}}, "required": ["query"]}}}, {"id": "6d75f6a8-ad51-4ab8-a324-905dfefd861d", "type": "tool_result", "timestamp": 1753892261726, "toolCallId": "call_3to2wwqi2wy6k1vepj0n7ge1", "name": "web_search", "content": [{"title": "OSU-NLP-Group/GUI-Agents-Paper-List", "url": "https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List", "content": "GitHubGitHub · 翻译此页2024年12月27日 — This repo covers a variety of papers related to GUI Agents, such as: Datasets; Benchmarks; Models; Agent frameworks; Vision, language, ...<PERSON>'s PapersGrounding (8)Benchmark (90)Evaluation (9)"}, {"title": "OSU-NLP-Group/GUI-Agents-Paper-List", "url": "https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List/blob/main/paper_by_key/paper_evaluation.md", "content": "GitHubGitHub · 翻译此页TLDR: This paper introduces WebVoyager, an innovative web agent powered by Large Multimodal Models (LMMs) that can complete user instructions end-to-end by ..."}, {"title": "Search for GUI", "url": "https://paperswithcode.com/search?&q=GUI", "content": "Papers With CodePapers With Code · 翻译此页UI-TARS: Pioneering Automated GUI Interaction with Native Agents · 2 code implementations • 21 Jan 2025. This paper introduces UI-TARS, a native GUI agent ..."}, {"title": "[2504.20464] A Survey on GUI Agents with Foundation ...", "url": "https://arxiv.org/abs/2504.20464", "content": "arXivarXiv · 翻译此页作者：J Li · 2025 — This paper provides a structured survey of recent advances in GUI agents, focusing on architectures enhanced by Reinforcement Learning (RL)."}, {"title": "ProgRM: Build Better GUI Agents with Progress Rewards", "url": "https://arxiv.org/abs/2505.18121", "content": "arXivarXiv · 翻译此页作者：D <PERSON> · 2025 — We propose Progress Reward Model (ProgRM) to provide dense informative intermediate rewards by predicting a task completion progress for each step in online ..."}, {"title": "Empowering Generalizable GUI Agents with One Exploration", "url": "https://openaccess.thecvf.com/content/CVPR2025/papers/Sun_GUI-Xplore_Empowering_Generalizable_GUI_Agents_with_One_Exploration_CVPR_2025_paper.pdf", "content": "CVF Open AccessCVF Open AccessPDF作者：Y Sun · 2025 · 被引用次数：5 — Comparison between the current GUI agent paradigm and our exploration-based paradigm. (a) The current paradigm only learns generalized GUI knowledge during ..."}, {"title": "GUI-Agent - a xlbqc Collection", "url": "https://huggingface.co/collections/xlbqc/gui-agent-67bc272a55edf2ef92ae1076", "content": "Hugging FaceHugging FaceMobile-Agent-V: Learning Mobile Device Operation Through Video-Guided Multi-Agent Collaboration. Paper • 2502.17110 • Published Feb 24 • 13."}, {"title": "Large Language Model-Brained GUI Agents: A Survey", "url": "https://openreview.net/forum?id=xChvYjvXTp", "content": "OpenReviewOpenReview · 翻译此页作者：C Zhang · 被引用次数：58 — This paper presents a comprehensive survey of LLM-brained GUI agents, exploring their historical evolution, core components, and advanced techniques."}, {"title": "CogAgent: A Visual Language Model for GUI Agents", "url": "https://paperswithcode.com/paper/cogagent-a-visual-language-model-for-gui", "content": "Papers With CodePapers With Code · 翻译此页In this paper, we introduce CogAgent, an 18-billion-parameter visual language model (VLM) specializing in GUI understanding and navigation."}, {"title": "LLM-Brained GUI Agents Survey", "url": "https://vyokky.github.io/LLM-Brained-GUI-Agents-Survey/", "content": "GitHubGitHub · 翻译此页A Collection of Research Papers and Projects in: Large Language Model-Brained GUI Agents: A Survey. ; ScienceBoard: Evaluating Multimodal Autonomous Agents in ..."}], "elapsedMs": 2751}, {"id": "f2d55d88-757b-4ca7-abe9-aa0ec6cda7bd", "type": "plan_update", "timestamp": 1753892262722, "sessionId": "1753892252891-yll0yfy", "steps": [{"content": "Use web_search tool with query 'latest GUI Agent papers' and count=10 to retrieve recent academic results.", "done": false}]}]