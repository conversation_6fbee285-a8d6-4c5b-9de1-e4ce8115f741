#!/bin/bash
# Adobe_Creative_Backup.sh
# Adobe Creative Suite 专用备份脚本
# 支持：Premiere Pro, After Effects, Photoshop, Illustrator, Audition

set -e

# 配置
BACKUP_DIR="/Users/<USER>/Desktop/Adobe_Creative_Backup_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$BACKUP_DIR/backup.log"

# 颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "[$(date '+%H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

echo -e "${BLUE}🎨 Adobe Creative Suite 备份开始...${NC}"

# 创建备份目录结构
mkdir -p "$BACKUP_DIR"/{Premiere,AfterEffects,Photoshop,Illustrator,Audition,Common,Projects}

# 检查Adobe应用程序运行状态
check_adobe_apps() {
    local running_apps=()
    local adobe_apps=("Adobe Premiere Pro" "Adobe After Effects" "Adobe Photoshop" "Adobe Illustrator" "Adobe Audition")
    
    for app in "${adobe_apps[@]}"; do
        if pgrep -f "$app" > /dev/null; then
            running_apps+=("$app")
        fi
    done
    
    if [ ${#running_apps[@]} -gt 0 ]; then
        log "${YELLOW}⚠️ 检测到以下Adobe应用程序正在运行：${NC}"
        printf '%s\n' "${running_apps[@]}"
        log "${YELLOW}建议关闭这些应用程序以确保备份完整性${NC}"
        read -p "按Enter继续备份，或Ctrl+C取消..."
    fi
}

# 1. Premiere Pro 备份
backup_premiere() {
    log "${BLUE}🎬 备份 Premiere Pro...${NC}"
    
    # 项目文件
    log "搜索Premiere项目文件..."
    find "/Users/<USER>/Documents" -name "*.prproj" -exec cp {} "$BACKUP_DIR/Premiere/Projects/" \; 2>/dev/null || mkdir -p "$BACKUP_DIR/Premiere/Projects"
    
    # 用户偏好设置
    PREMIERE_PREFS="/Users/<USER>/Library/Preferences"
    if [ -d "$PREMIERE_PREFS" ]; then
        find "$PREMIERE_PREFS" -name "*PremierePro*" -exec cp -R {} "$BACKUP_DIR/Premiere/Preferences/" \; 2>/dev/null || true
        find "$PREMIERE_PREFS" -name "*Adobe Premiere Pro*" -exec cp -R {} "$BACKUP_DIR/Premiere/Preferences/" \; 2>/dev/null || true
    fi
    
    # 应用支持文件
    PREMIERE_APP_SUPPORT="/Users/<USER>/Library/Application Support/Adobe/Premiere Pro"
    if [ -d "$PREMIERE_APP_SUPPORT" ]; then
        cp -R "$PREMIERE_APP_SUPPORT" "$BACKUP_DIR/Premiere/AppSupport/" 2>/dev/null
    fi
    
    # 预设和模板
    PREMIERE_DOCS="/Users/<USER>/Documents/Adobe/Premiere Pro"
    if [ -d "$PREMIERE_DOCS" ]; then
        cp -R "$PREMIERE_DOCS" "$BACKUP_DIR/Premiere/UserPresets/" 2>/dev/null
    fi
    
    local prproj_count=$(find "$BACKUP_DIR/Premiere" -name "*.prproj" 2>/dev/null | wc -l)
    log "${GREEN}✅ Premiere Pro备份完成 ($prproj_count 个项目)${NC}"
}

# 2. After Effects 备份
backup_aftereffects() {
    log "${BLUE}🎭 备份 After Effects...${NC}"
    
    # 项目文件
    find "/Users/<USER>/Documents" -name "*.aep" -exec cp {} "$BACKUP_DIR/AfterEffects/Projects/" \; 2>/dev/null || mkdir -p "$BACKUP_DIR/AfterEffects/Projects"
    
    # 用户偏好设置
    AE_PREFS="/Users/<USER>/Library/Preferences"
    if [ -d "$AE_PREFS" ]; then
        find "$AE_PREFS" -name "*After Effects*" -exec cp -R {} "$BACKUP_DIR/AfterEffects/Preferences/" \; 2>/dev/null || true
    fi
    
    # 应用支持文件
    AE_APP_SUPPORT="/Users/<USER>/Library/Application Support/Adobe/After Effects"
    if [ -d "$AE_APP_SUPPORT" ]; then
        cp -R "$AE_APP_SUPPORT" "$BACKUP_DIR/AfterEffects/AppSupport/" 2>/dev/null
    fi
    
    # 预设和模板
    AE_DOCS="/Users/<USER>/Documents/Adobe/After Effects"
    if [ -d "$AE_DOCS" ]; then
        cp -R "$AE_DOCS" "$BACKUP_DIR/AfterEffects/UserPresets/" 2>/dev/null
    fi
    
    local aep_count=$(find "$BACKUP_DIR/AfterEffects" -name "*.aep" 2>/dev/null | wc -l)
    log "${GREEN}✅ After Effects备份完成 ($aep_count 个项目)${NC}"
}

# 3. Photoshop 备份
backup_photoshop() {
    log "${BLUE}🖼️ 备份 Photoshop...${NC}"
    
    # PSD文件
    find "/Users/<USER>/Documents" -name "*.psd" -exec cp {} "$BACKUP_DIR/Photoshop/Projects/" \; 2>/dev/null || mkdir -p "$BACKUP_DIR/Photoshop/Projects"
    
    # 用户偏好设置
    PS_PREFS="/Users/<USER>/Library/Preferences"
    if [ -d "$PS_PREFS" ]; then
        find "$PS_PREFS" -name "*Photoshop*" -exec cp -R {} "$BACKUP_DIR/Photoshop/Preferences/" \; 2>/dev/null || true
    fi
    
    # 应用支持文件
    PS_APP_SUPPORT="/Users/<USER>/Library/Application Support/Adobe/Adobe Photoshop"
    if [ -d "$PS_APP_SUPPORT" ]; then
        cp -R "$PS_APP_SUPPORT" "$BACKUP_DIR/Photoshop/AppSupport/" 2>/dev/null
    fi
    
    # 预设、动作、画笔
    PS_DOCS="/Users/<USER>/Documents/Adobe/Photoshop"
    if [ -d "$PS_DOCS" ]; then
        cp -R "$PS_DOCS" "$BACKUP_DIR/Photoshop/UserPresets/" 2>/dev/null
    fi
    
    local psd_count=$(find "$BACKUP_DIR/Photoshop" -name "*.psd" 2>/dev/null | wc -l)
    log "${GREEN}✅ Photoshop备份完成 ($psd_count 个PSD文件)${NC}"
}

# 4. Illustrator 备份
backup_illustrator() {
    log "${BLUE}✏️ 备份 Illustrator...${NC}"
    
    # AI文件
    find "/Users/<USER>/Documents" -name "*.ai" -exec cp {} "$BACKUP_DIR/Illustrator/Projects/" \; 2>/dev/null || mkdir -p "$BACKUP_DIR/Illustrator/Projects"
    
    # 用户偏好设置
    AI_PREFS="/Users/<USER>/Library/Preferences"
    if [ -d "$AI_PREFS" ]; then
        find "$AI_PREFS" -name "*Illustrator*" -exec cp -R {} "$BACKUP_DIR/Illustrator/Preferences/" \; 2>/dev/null || true
    fi
    
    # 应用支持文件
    AI_APP_SUPPORT="/Users/<USER>/Library/Application Support/Adobe/Adobe Illustrator"
    if [ -d "$AI_APP_SUPPORT" ]; then
        cp -R "$AI_APP_SUPPORT" "$BACKUP_DIR/Illustrator/AppSupport/" 2>/dev/null
    fi
    
    local ai_count=$(find "$BACKUP_DIR/Illustrator" -name "*.ai" 2>/dev/null | wc -l)
    log "${GREEN}✅ Illustrator备份完成 ($ai_count 个AI文件)${NC}"
}

# 5. Audition 备份
backup_audition() {
    log "${BLUE}🎵 备份 Audition...${NC}"
    
    # 音频项目文件
    find "/Users/<USER>/Documents" -name "*.sesx" -exec cp {} "$BACKUP_DIR/Audition/Projects/" \; 2>/dev/null || mkdir -p "$BACKUP_DIR/Audition/Projects"
    
    # 用户偏好设置
    AU_PREFS="/Users/<USER>/Library/Preferences"
    if [ -d "$AU_PREFS" ]; then
        find "$AU_PREFS" -name "*Audition*" -exec cp -R {} "$BACKUP_DIR/Audition/Preferences/" \; 2>/dev/null || true
    fi
    
    # 应用支持文件
    AU_APP_SUPPORT="/Users/<USER>/Library/Application Support/Adobe/Audition"
    if [ -d "$AU_APP_SUPPORT" ]; then
        cp -R "$AU_APP_SUPPORT" "$BACKUP_DIR/Audition/AppSupport/" 2>/dev/null
    fi
    
    local sesx_count=$(find "$BACKUP_DIR/Audition" -name "*.sesx" 2>/dev/null | wc -l)
    log "${GREEN}✅ Audition备份完成 ($sesx_count 个项目)${NC}"
}

# 6. Adobe通用设置备份
backup_adobe_common() {
    log "${BLUE}🔧 备份Adobe通用设置...${NC}"
    
    # Creative Cloud设置
    CC_PREFS="/Users/<USER>/Library/Preferences"
    if [ -d "$CC_PREFS" ]; then
        find "$CC_PREFS" -name "*Adobe*" -name "*Creative*" -exec cp -R {} "$BACKUP_DIR/Common/Preferences/" \; 2>/dev/null || true
        find "$CC_PREFS" -name "com.adobe.*.plist" -exec cp {} "$BACKUP_DIR/Common/Preferences/" \; 2>/dev/null || true
    fi
    
    # 通用应用支持文件
    ADOBE_COMMON="/Users/<USER>/Library/Application Support/Adobe/Common"
    if [ -d "$ADOBE_COMMON" ]; then
        cp -R "$ADOBE_COMMON" "$BACKUP_DIR/Common/AppSupport/" 2>/dev/null
    fi
    
    # Creative Cloud文件
    CC_FILES="/Users/<USER>/Creative Cloud Files"
    if [ -d "$CC_FILES" ]; then
        # 只备份小文件，跳过大型媒体文件
        find "$CC_FILES" -type f -size -10M -exec cp --parents {} "$BACKUP_DIR/Common/CC_Files/" \; 2>/dev/null || true
    fi
    
    log "${GREEN}✅ Adobe通用设置备份完成${NC}"
}

# 7. 创建恢复脚本
create_restore_script() {
    cat > "$BACKUP_DIR/restore_adobe.sh" << 'EOF'
#!/bin/bash
echo "🎨 Adobe Creative Suite 恢复..."
BACKUP_DIR="$(dirname "$0")"

# 恢复Premiere Pro
if [ -d "$BACKUP_DIR/Premiere" ]; then
    echo "恢复Premiere Pro..."
    cp -R "$BACKUP_DIR/Premiere/Preferences/"* "/Users/<USER>/Library/Preferences/" 2>/dev/null || true
    cp -R "$BACKUP_DIR/Premiere/AppSupport/"* "/Users/<USER>/Library/Application Support/Adobe/" 2>/dev/null || true
    cp -R "$BACKUP_DIR/Premiere/UserPresets/"* "/Users/<USER>/Documents/Adobe/" 2>/dev/null || true
    echo "✅ Premiere Pro恢复完成"
fi

# 恢复After Effects
if [ -d "$BACKUP_DIR/AfterEffects" ]; then
    echo "恢复After Effects..."
    cp -R "$BACKUP_DIR/AfterEffects/Preferences/"* "/Users/<USER>/Library/Preferences/" 2>/dev/null || true
    cp -R "$BACKUP_DIR/AfterEffects/AppSupport/"* "/Users/<USER>/Library/Application Support/Adobe/" 2>/dev/null || true
    echo "✅ After Effects恢复完成"
fi

# 恢复Photoshop
if [ -d "$BACKUP_DIR/Photoshop" ]; then
    echo "恢复Photoshop..."
    cp -R "$BACKUP_DIR/Photoshop/Preferences/"* "/Users/<USER>/Library/Preferences/" 2>/dev/null || true
    cp -R "$BACKUP_DIR/Photoshop/AppSupport/"* "/Users/<USER>/Library/Application Support/Adobe/" 2>/dev/null || true
    echo "✅ Photoshop恢复完成"
fi

# 恢复Illustrator
if [ -d "$BACKUP_DIR/Illustrator" ]; then
    echo "恢复Illustrator..."
    cp -R "$BACKUP_DIR/Illustrator/Preferences/"* "/Users/<USER>/Library/Preferences/" 2>/dev/null || true
    cp -R "$BACKUP_DIR/Illustrator/AppSupport/"* "/Users/<USER>/Library/Application Support/Adobe/" 2>/dev/null || true
    echo "✅ Illustrator恢复完成"
fi

# 恢复Audition
if [ -d "$BACKUP_DIR/Audition" ]; then
    echo "恢复Audition..."
    cp -R "$BACKUP_DIR/Audition/Preferences/"* "/Users/<USER>/Library/Preferences/" 2>/dev/null || true
    cp -R "$BACKUP_DIR/Audition/AppSupport/"* "/Users/<USER>/Library/Application Support/Adobe/" 2>/dev/null || true
    echo "✅ Audition恢复完成"
fi

# 恢复通用设置
if [ -d "$BACKUP_DIR/Common" ]; then
    echo "恢复Adobe通用设置..."
    cp -R "$BACKUP_DIR/Common/Preferences/"* "/Users/<USER>/Library/Preferences/" 2>/dev/null || true
    cp -R "$BACKUP_DIR/Common/AppSupport/"* "/Users/<USER>/Library/Application Support/Adobe/" 2>/dev/null || true
    echo "✅ Adobe通用设置恢复完成"
fi

echo "🎉 Adobe Creative Suite 恢复完成！"
echo "建议重启Adobe应用程序以应用设置"
EOF
    
    chmod +x "$BACKUP_DIR/restore_adobe.sh"
}

# 主执行流程
main() {
    check_adobe_apps
    
    backup_premiere
    backup_aftereffects
    backup_photoshop
    backup_illustrator
    backup_audition
    backup_adobe_common
    
    create_restore_script
    
    # 统计信息
    local backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    local total_projects=$(find "$BACKUP_DIR" -name "*.prproj" -o -name "*.aep" -o -name "*.psd" -o -name "*.ai" -o -name "*.sesx" | wc -l)
    
    # 生成备份报告
    cat > "$BACKUP_DIR/backup_report.txt" << EOF
Adobe Creative Suite 备份报告
===========================
备份时间: $(date)
备份大小: $backup_size
项目文件总数: $total_projects

详细统计:
- Premiere Pro项目: $(find "$BACKUP_DIR/Premiere" -name "*.prproj" 2>/dev/null | wc -l | xargs)
- After Effects项目: $(find "$BACKUP_DIR/AfterEffects" -name "*.aep" 2>/dev/null | wc -l | xargs)
- Photoshop文件: $(find "$BACKUP_DIR/Photoshop" -name "*.psd" 2>/dev/null | wc -l | xargs)
- Illustrator文件: $(find "$BACKUP_DIR/Illustrator" -name "*.ai" 2>/dev/null | wc -l | xargs)
- Audition项目: $(find "$BACKUP_DIR/Audition" -name "*.sesx" 2>/dev/null | wc -l | xargs)

恢复方法:
运行 restore_adobe.sh 脚本自动恢复所有设置
EOF
    
    echo -e "${GREEN}🎉 Adobe Creative Suite 备份完成！${NC}"
    echo -e "${YELLOW}📍 备份位置: $BACKUP_DIR${NC}"
    echo -e "${YELLOW}📊 项目文件: $total_projects 个${NC}"
    echo -e "${YELLOW}💾 备份大小: $backup_size${NC}"
    
    # 打开备份目录
    open "$BACKUP_DIR"
}

# 执行主函数
main "$@"
