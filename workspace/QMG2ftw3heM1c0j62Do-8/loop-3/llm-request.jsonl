{"provider": "volcengine", "request": {"model": "doubao-1-5-thinking-vision-pro-250428", "messages": [{"role": "system", "content": "\nYou are Agent TARS, a multimodal AI agent created by the ByteDance.\n\n<intro>\nYou excel at the following tasks:\n1. Information gathering, fact-checking, and documentation\n2. Data processing, analysis, and visualization\n3. Writing multi-chapter articles and in-depth research reports\n4. Creating websites, applications, and tools\n5. Using programming to solve various problems beyond development\n6. Various tasks that can be accomplished using computers and the internet\n</intro>\n\n<language_settings>\nUse the language specified by user in messages as the working language when explicitly provided\nAll thinking and responses must be in the working language\nNatural language arguments in tool calls must be in the working language\nAvoid using pure lists and bullet points format in any language\n</language_settings>\n\n<multimodal_understanding>\nWhen processing images, it's crucial to understand the difference between image types:\n1. Browser Screenshots: These are images showing the browser interface that you can interact with using browser tools\n   - Appear as part of the browser_vision_control tool output or environment input labeled as \"Browser Screenshot\"\n   - ONLY these screenshots represent interfaces you can operate on with browser tools\n   - Use these for navigation, clicking elements, scrolling, and other browser interactions\n\n2. User-Uploaded Images: These are regular images the user has shared but are NOT browser interfaces\n   - May include photos, diagrams, charts, documents, or any other visual content\n   - Cannot be operated on with browser tools - don't try to click elements in these images\n   - Should be analyzed for information only (objects, text, context, meaning)\n   - Respond to user questions about these images with observations and analysis\n\nDistinguish between these types by context and environment input descriptions to avoid confusion.\nWhen you see a new image, first determine which type it is before deciding how to interact with it.\n</multimodal_understanding>\n\n<system_capability>\nSystem capabilities:\n- Communicate with users through message tools\n- Access a Linux sandbox environment with internet connection\n- Use shell, text editor, browser, and other software\n- Write and run code in Python and various programming languages\n- Independently install required software packages and dependencies via shell\n- Deploy websites or applications and provide public access\n- Suggest users to temporarily take control of the browser for sensitive operations when necessary\n- Utilize various tools to complete user-assigned tasks step by step\n</system_capability>\n\n<agent_loop>\nYou operate in an agent loop, iteratively completing tasks through these steps:\n1. Analyze Events: Understand user needs and current state through event stream, focusing on latest user messages and execution results\n2. Select Tools: Choose next tool call based on current state, task planning, relevant knowledge and available data APIs\n3. Wait for Execution: Selected tool action will be executed by sandbox environment with new observations added to event stream\n4. Iterate: Choose only one tool call per iteration, patiently repeat above steps until task completion\n5. Submit Results: Send results to user via message tools, providing deliverables and related files as message attachments\n6. Enter Standby: Enter idle state when all tasks are completed or user explicitly requests to stop, and wait for new tasks\n</agent_loop>\n\n<file_rules>\n- Use file tools for reading, writing, appending, and editing to avoid string escape issues in shell commands\n- Actively save intermediate results and store different types of reference information in separate files\n- When merging text files, must use append mode of file writing tool to concatenate content to target file\n- Strictly follow requirements in <writing_rules>, and avoid using list formats in any files except todo.md\n</file_rules>\n\n<shell_rules>\n- Avoid commands requiring confirmation; actively use -y or -f flags for automatic confirmation\n- Avoid commands with excessive output; save to files when necessary\n- Chain multiple commands with && operator to minimize interruptions\n- Use pipe operator to pass command outputs, simplifying operations\n- Use non-interactive `bc` for simple calculations, Python for complex math; never calculate mentally\n- Use `uptime` command when users explicitly request sandbox status check or wake-up\n</shell_rules>\n\n\n\n\n<planning_approach>\nYou are a methodical agent that follows a plan-and-solve approach for complex tasks. When handling tasks:\n\n1. Analyze if the task requires a multi-step plan:\n   - For complex research, analysis, or multi-part tasks → Create a plan\n   - For simple questions or tasks → Skip planning and answer directly\n\n2. If a plan is needed:\n   - Create a clear, step-by-step plan with specific goals\n   - Execute each step in order, using appropriate tools\n   - Update the plan as you learn new information\n   - Mark steps as completed when done\n   - Once ALL steps are complete, call the \"final_answer\" tool\n\n3. During execution:\n   - Adapt your plan as needed based on new findings\n   - Be willing to simplify the plan if the task turns out simpler than expected\n   - Always complete your plan before providing final answers\n</planning_approach>\n\n<planning_constraints>\nIMPORTANT CONSTRAINTS:\n- Create AT MOST 3 key steps in your plan\n- Focus on information gathering and research steps\n- Call the \"final_answer\" tool once ALL plan steps are complete\n- For simple questions, you can skip planning entirely\n</planning_constraints>\n \n\n \n<browser_rules>\nYou have access to various browser tools to interact with web pages and extract information.\n\n- Must use browser tools to access and comprehend all URLs provided by users in messages\n- Must use browser tools to access URLs from search tool results\n- Actively explore valuable links for deeper information, either by clicking elements or accessing URLs directly\n- Browser tools only return elements in visible viewport by default\n- Due to technical limitations, not all interactive elements may be identified; use coordinates to interact with unlisted elements\n- Browser tools automatically attempt to extract page content, providing it in Markdown format if successful\n- Extracted Markdown includes text beyond viewport but omits links and images; completeness not guaranteed\n- If extracted Markdown is complete and sufficient for the task, no scrolling is needed; otherwise, must actively scroll to view the entire page\n- Use message tools to suggest user to take over the browser for sensitive operations or actions with side effects when necessary\n</browser_rules>\n\n<envirnoment>\nCurrent Working Directory: /Users/<USER>/TARS-Agent/workspace\n</envirnoment>\n\n    \n\nCurrent time: 5/20/2025, 10:00:00 AM"}, {"role": "user", "content": "Search for the latest GUI Agent papers"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Visit academic databases like ACM Digital Library, IEEE Xplore, or arXiv. Use keywords such as 'GUI agent', 'graphical user interface agent', and limit the search to the latest (e.g., past 1 - 2 years) publications.\n2. [TODO] Check research platforms like Google Scholar. Use advanced search features to filter by publication date and relevant keywords to find the most recent GUI agent - related papers.\n3. [TODO] Explore specialized AI and HCI (Human - Computer Interaction) conferences' websites, such as CHI, UIST, or AIES. Look for their latest proceedings or upcoming publications related to GUI agents.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "用户现在需要执行第一步计划，搜索最新的GUI Agent论文。首先选择web_search工具，因为要获取网络信息。查询需要简洁，控制在7词内，所以用“latest GUI Agent papers”作为查询词，count设为10获取足够结果。", "tool_calls": [{"id": "call_3to2wwqi2wy6k1vepj0n7ge1", "type": "function", "function": {"name": "web_search", "arguments": "{\"count\":10.0,\"query\":\"latest GUI Agent papers\"}"}}]}, {"role": "tool", "tool_call_id": "call_3to2wwqi2wy6k1vepj0n7ge1", "content": "[\n  {\n    \"title\": \"OSU-NLP-Group/GUI-Agents-Paper-List\",\n    \"url\": \"https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List\",\n    \"content\": \"GitHubGitHub · 翻译此页2024年12月27日 — This repo covers a variety of papers related to GUI Agents, such as: Datasets; Benchmarks; Models; Agent frameworks; Vision, language, ...<PERSON>'s PapersGrounding (8)Benchmark (90)Evaluation (9)\"\n  },\n  {\n    \"title\": \"OSU-NLP-Group/GUI-Agents-Paper-List\",\n    \"url\": \"https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List/blob/main/paper_by_key/paper_evaluation.md\",\n    \"content\": \"GitHubGitHub · 翻译此页TLDR: This paper introduces WebVoyager, an innovative web agent powered by Large Multimodal Models (LMMs) that can complete user instructions end-to-end by ...\"\n  },\n  {\n    \"title\": \"Search for GUI\",\n    \"url\": \"https://paperswithcode.com/search?&q=GUI\",\n    \"content\": \"Papers With CodePapers With Code · 翻译此页UI-TARS: Pioneering Automated GUI Interaction with Native Agents · 2 code implementations • 21 Jan 2025. This paper introduces UI-TARS, a native GUI agent ...\"\n  },\n  {\n    \"title\": \"[2504.20464] A Survey on GUI Agents with Foundation ...\",\n    \"url\": \"https://arxiv.org/abs/2504.20464\",\n    \"content\": \"arXivarXiv · 翻译此页作者：J Li · 2025 — This paper provides a structured survey of recent advances in GUI agents, focusing on architectures enhanced by Reinforcement Learning (RL).\"\n  },\n  {\n    \"title\": \"ProgRM: Build Better GUI Agents with Progress Rewards\",\n    \"url\": \"https://arxiv.org/abs/2505.18121\",\n    \"content\": \"arXivarXiv · 翻译此页作者：D Zhang · 2025 — We propose Progress Reward Model (ProgRM) to provide dense informative intermediate rewards by predicting a task completion progress for each step in online ...\"\n  },\n  {\n    \"title\": \"Empowering Generalizable GUI Agents with One Exploration\",\n    \"url\": \"https://openaccess.thecvf.com/content/CVPR2025/papers/Sun_GUI-Xplore_Empowering_Generalizable_GUI_Agents_with_One_Exploration_CVPR_2025_paper.pdf\",\n    \"content\": \"CVF Open AccessCVF Open AccessPDF作者：Y Sun · 2025 · 被引用次数：5 — Comparison between the current GUI agent paradigm and our exploration-based paradigm. (a) The current paradigm only learns generalized GUI knowledge during ...\"\n  },\n  {\n    \"title\": \"GUI-Agent - a xlbqc Collection\",\n    \"url\": \"https://huggingface.co/collections/xlbqc/gui-agent-67bc272a55edf2ef92ae1076\",\n    \"content\": \"Hugging FaceHugging FaceMobile-Agent-V: Learning Mobile Device Operation Through Video-Guided Multi-Agent Collaboration. Paper • 2502.17110 • Published Feb 24 • 13.\"\n  },\n  {\n    \"title\": \"Large Language Model-Brained GUI Agents: A Survey\",\n    \"url\": \"https://openreview.net/forum?id=xChvYjvXTp\",\n    \"content\": \"OpenReviewOpenReview · 翻译此页作者：C Zhang · 被引用次数：58 — This paper presents a comprehensive survey of LLM-brained GUI agents, exploring their historical evolution, core components, and advanced techniques.\"\n  },\n  {\n    \"title\": \"CogAgent: A Visual Language Model for GUI Agents\",\n    \"url\": \"https://paperswithcode.com/paper/cogagent-a-visual-language-model-for-gui\",\n    \"content\": \"Papers With CodePapers With Code · 翻译此页In this paper, we introduce CogAgent, an 18-billion-parameter visual language model (VLM) specializing in GUI understanding and navigation.\"\n  },\n  {\n    \"title\": \"LLM-Brained GUI Agents Survey\",\n    \"url\": \"https://vyokky.github.io/LLM-Brained-GUI-Agents-Survey/\",\n    \"content\": \"GitHubGitHub · 翻译此页A Collection of Research Papers and Projects in: Large Language Model-Brained GUI Agents: A Survey. ; ScienceBoard: Evaluating Multimodal Autonomous Agents in ...\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Use web_search tool with query 'latest GUI Agent papers' and count=10 to retrieve recent academic results.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "用户之前用web_search获取了关于GUI Agent的搜索结果，现在需要继续执行计划中的后续步骤。首先分析搜索结果里的链接，第一个结果是GitHub仓库OSU - NLP - Group/GUI - Agents - Paper - List，这个仓库专门收集GUI Agents相关论文，很有价值，所以下一步应该用browser_navigate工具导航到这个URL，去查看仓库里的最新论文资源。", "tool_calls": [{"id": "call_qvdrqyj1b1xrllhcaoz31pms", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List\"}"}}]}, {"role": "tool", "tool_call_id": "call_qvdrqyj1b1xrllhcaoz31pms", "content": "{\n  \"status\": \"success\",\n  \"url\": \"https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List\",\n  \"message\": \"Navigated success\"\n}"}, {"role": "user", "content": [{"type": "text", "text": "[Environment: Browser Screenshot]"}, {"type": "image_url", "image_url": {"url": "data:image/webp;base64,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******************************************/cptwDifPJZKKv2dGf6C6furygzKw8yhPhssiP4ifbdz/8TafCllDFdutk6g/kmHkYDgmFLmmTsEAv6u26mtiqo0ub5sTSzXii7ae80I4CpwJi5GnnNVGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSOvNGWRRwemPZd9+sbkozsNRU92yXM9rG+PFBzPnbmxq+ZpnZ7vrdNoYLWVDTKMdy5BxulxgRNJGh1dSTwDzvAIUJLCiakZUlb5Nj7/cHYx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY6v/nQZVZ/evLbr2Zy6n+H/F97pxr5zR1Xw6IMZDscUgq3h5msSRiAM4Qft5DMm4nqICOruYVFPYhHzvfqAQ78nWdAyRMHfBsgpOK0WOHp9uxZDbMWFbqhOKhYxNLg3Go4n8MlRFE5eKleENd3RwutsdVAqaYrurvzxa4Zy0w45dVWokf8wyDatCsDa+qvjv5jEOCBBeimVWYCTPr0EL6amUkJ2+Gx++OT9caVDo+er+QBhiQ27kPkUrFQGXfoZPCbWWgz2peIooUT0nCYNGbMiUpKyU8R4uv2PnW9QmpcGYmkjHiNJGQNZDzdgomkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jLgXGe7uduBzLvNMxDe5rlaCav29gSnZuOjqC7DncTjJyQIffzdBWE/8zZCPiE+CnAF0yu6hQQjwG8adwOagGCaVsRpH0O2Y6yGXDLnnKohXaOPwOECoBYCwF6RPGhJOPvMZnrhRgV6O5efgnC8kUBtfvSDPRnYs5815Bl00SHT5hyZd63kqzdjHiNJGPEaSR40r15kaxNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPKPCWyNKDrzdKFiDzuLdSt8eV5ukjOhK8nifMZeKEaq5EOWdCHLOhDlnQhyzoQ5Z0Ics6EOWdCHLOhDlnQhyzoQ5Z0Ics6EOWdCHLOhDlnQhyzoQ5Z0Ics6EOWdCHLOhDlnQhyzoQ5Z0Ics6EOWdCHLOZzHiNJGPEaSMeI0kY8RpIx4jSRkDWQ81nJD7E0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kZpocpEHpXb0ucOc0e92bnKlb48rzdJGPEaSMeI0kZ0JXnS4Cuyd+kXfgTj8uYA7O4GIcGaeOtSJHrGy0oIP2s4bqm1cjShvGMr6rIocrVAm+3fEhDWLr0bR9QEAQmNgGsMzVGojiOr2FqbP+KyCIBLhE0kY8RpIx4jSRjXP5Ry3yoOEXUkhxze7Gu5IVJIcc6hqd9UHCLqSQ43py5LC4Wj/a9OW89EKjaA+/3jBwUe6v0g5F7GUclGOlfnm92BFrBmuOsIEMILhWirJAHZt1cQfUQqxJ72vBYJ9qAW9pPGYo3f1zM1SpQlzf+9UKJpIx4jSRjxGkjHiOZPje5FT+x45v6sQxIQg56ra0f2tGmAQGZheTjCnfhmm4kZAW3KzmK07RM67ZXnEQgb0ykZ1QwDTLWBmYNIp9ooQATI/e6O6049jLwZanE0wNsfhAA9jt4fZZ5H9C1FfyY3lebpIx4jSRjxGfftz2iqDhF1JIcc6hpd06/1cD1mn1QcIupJDjnUNTvqg3pDb+Clez/EXfqgo4wuYTvdNc2pHI/WviRg3/40O1/hHCHRK89igtwcLTkzJKM7xhkZbwhEpwy8TRukjHiNJGPEaSMeI0kjxpXruA6WCzzFSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjx0RD7E0kY8RpIx4jSRjxGkjHiNJIb484zy1jHiNJGPEaSMeI0kY8RpM9lymARpO2fve+csug9rSHbtj95dWA9SN/f9iXgHtaReGN4u9vZTWhSy/16Esug9rSLwxvF3t7Ka0KWX+vQll0HtaReGN4u9vZTWhSy/16Esug9rSLwxvF3t7Ka0KWX+vQll0HtaReGN4u9vZTWhSy/16Esug9rSLwxvF3t7Ka0KWX+vQll0HtaReGN4u9vZTWhSy/16Esug9rSLwxvF3t7Ka0KWX+vQll0HtaReGN4u9vZTWhSy/16Esug9rSLwxvF3t7Ka0LbHJDGr53lNg6zfIpt4tn0uSzpScdS5uCRWBUOSH2JpIx4jSRjxGkjHlKD5cPJIcc6hqd9UHCLqSQ451DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoanfVBwi6kkOOdQ1O+qDhF07OoyMMtLePOM2KgCwfq9qE4eo9b/W1xDuGy0xdt8dGuXRQPVqat+jq68qz+CMb4QG5ekjEY8RpIx4jSRjxGkjHiOZPje5DP/+vz2xL7YlhPa1l+wGJfYNMde/DkuC71oJgYoeT3rEglBoWYjFlLg+yqQDou4KUJsUXs7BBZUEcKCcRjs4FSlCupW+PK83SRjxGkivY2fi5Stm08pJmCWm3DOWdCHLOf+wo3/6lx0F3xKS17RPnyoRqlUPWKOiWrXJApqPC0F3xKTL8tNav1BMt5zHmK4TlJhqJdJTZ/pW+PK83SRjxGkjHlKD5cEUTWQFeiK0GRTpfQpCETzS5gT6Wz+G9qf+r5F0l8GaGZXh/JeOXb+wANScleDfI/TQeYciUS/HMpXwzTsGeCc+j1Ft74iHWQRwzsdCaI/Iobj4ygC1s1EWEKSFuHTkKJpIx4jSRjxGkjIKyC/LTbhnLOhDlnQhyziV7oRoMtn5RQVEtNt92FFXJyzTwfbD/+C1cBioLXAhyzUkWdaDTnz4DeyxNJGPEaSMeI0kY8RpIx4jSRjxORp5yiUA6iKABJq8fcKzhsu+Zq2pzSRjxGkjHiNJGPEaSMeYqSMeI0kY8RpiQmszWMeI1JPAXsanfSpGpzqGuU7LDEvSawVbjfleZDNzqZwYH2Da2RIg2T5k5vCcksMJEfaQWVn7HI8hD6kc9hzLtztgA0yoHv9kS9hRc6nOKCFH2+5lgMU+hYxG4AorT7QoV2f6VvjyvN0kY8pQfKhrlWP2ffUZKi6kj4XdQ1O+qDhF1JIcc6hqd9UHCLqSQ451DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoailct/IwUHIHhAnKz5CKWxwgo8NjlJOf0m8p+jLGp9QQ7bl5Ckowuu8ewUonV1AlTg8+c2yfwWqCjv87rZ8PYdzr/ZGzemNmqqmng0rR1CsMkbvowacpxMJWeWsY8RpIx4jSRkDWQ8kNW7pIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSSG+PK83SRkJpKEMLXSRjxGkjHiNJI8aV66DH3Bzb+Abg0vLqKUJNN4gJCTTeI3BhV1LTEtDXbWB6NaGRIjboeWsY8RpIx4jSRjxGdbQics9HQXfEpMvy024ZyzoQ5Zz/1LjoLviUWA0wqyT9Wejnp4R8So6twj5BVncUPSoc5+Wm3B5yFADoDlYVN63smvTPpEUCJDk6OjXYp9okPOQT1OGI0kY8RpIx4jSRjxGkjOhK86XKIKhUXdszqTISrouq/JNi0Ek3rf2Shzh4XzoLCUaafBzOThUvmiUnIQraAobG4slC629aMuE03BWL7WdDD9UJSjtSuBv1W6dTrF4gFkiUlxmlrGPEaSMeI0kY8RpNWP43x5Xm6SMmqC1oMsG2nweUXQ/jD/g2kdoj5QMiVMt8+UPKBtIPuH7I7Gh6WLEwYha3qb0Lztj+geUga5aMu9D+KYZDqj/hBw3QscAmZ+hA2H7gekWfCUov7Mb048tHL7MuzyL1fZH6nQOKjv7B0QSk0Y8RpIx4jSRjxGkjHlKD5cEWHTwjyAIL1Ha4CgZE5gAwlBGrEwp+4q/YTwn2JpIx4jSRjxGkjHijIkzBLTbhnLOhDlnQhyckmX5Y/jCeAvQUwS7DrsL0FKSH95WEAQqrkQrnZSVcbqYPXyzm1NIB7bD5UeXG4qeOUvYeSVw2H5yp8zAZeHZPJKgB+xNJGPEaSMeI0kY8RzJ8bccVSAYWKfN1HL13051DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoanfVBwi6kkOOdQ1O+qDhF1JIcc6hqKXK88dQdebpIx4jSRjxGkjHiNJGdCV5grKAW5/3wFbFXdbup9PLr/fAVsVhFd1Pp5df74DEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFYRXdT6iuwH4TEFcHJvfo52EuBlbMopwllixo/pXQh2KXM69zJnQm0FFamCjYCcF7vwh08bVxCpzWisLyvlnsrzdJGPEaSMeI0kZ0JXnS2Gz6Sk3yO6kY4pp5aOX9J/jSAKjVQo10kY8RpIx4jSRjxGkjFkNflE46C74lJl+Wm3DOWdCFYeKK17M09d53gq1bsSsPFFl+Uq5b/AK4tyQBrkI98pJl5TYJ8YlCSd9CFSfFNBaQ0N+lIbHfK+FfGQgNopSHLdn79K+RmU34ROWLro6KA1wAfqWcdjfKk/CAYmAmFuANP4RqOWh/0/RWiWKLTLER60IY3HjwQyNJahRNJGPEaSMeI0kZ0JXnS3awDUwiXsGwVv0D7CCzuHBmHBofGZ+cQxvZKjVGxq5WFGzJlVmooDy1zsDqbz3QNCyHKtOAnF4z4jGRibV/YHg0ewWAh9Cj8qoHz5AKUnKKQv+p8D7E0kY8RpIx4jSRcD7M/pW+PK83SUGDe1NFXILKtFipNMw7wY9eAfou8ecHRDTmX/Pps/0rfHldDijyvN0kY8RpIx4jSRkDWQ81dhi9eWHpWJR0Dh5bG1lW0XseqoQXYe6/pN0wtjwEWYgZX3DUxq4JckJ8pnrQPsTSRjxGkjHiNJGOb5gmgno6C74lJl+Wm3DOWc+pJiwnbBTptvs6tFpfcDM2JyS3TDD5lrWmx31TvBPWJbJ6OgpQt5CRvOay5eWoHXvYsUDoI6WMPNwYH2JpIx4jSRjxGkjHiNJGPEcyfG3HFqnXOMODGGoZwTFZKi6kkOOdQ1O+qDhF1JIcc6hqd9UHCLqSQ451DU76oOEXUkhxzqGp31QcIupHBjnUNTvqg4Q8pWOzw2W/JdttxVHbF4VlmoF0pV7zXw512LVHH/grSYeWXQ9zKgGzxb2Zv7J8S6zIFYu/e2I+hojB3x5Xm6SMeI0kY8RpIzoSvJ0VjBhdSSHHOoanfVBwi6kkOOdQ1O+qDhF1JIcc6hqd9UHCLqSQ451DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg3SBAIhz8+oZyI0Av5YL8IXbH4c2fVv1SviWYQ7JIeOBIvCSzBC8e6BYg/QDVgmJpIx4jSRjxGkjHiNPl3eY35HpZSELgNPUq6ONuO2U2K8hHxhFnz3gBKGR+PDg+MxS0b48rzdJGPEaR5VIoOEXUkhxzqGp31QcIsdhWrqSQ451DU2oLtepJDfdZKZYMVQ5Ec2+37uSHG5ARcBCQFc3Bcvi6lb++6lb48rzdJGPEaSMeI5k+N7kUpX9DLpLdKiVNEp0WAsSANFroHpqRVh2rs0lLiraFREj99r4OTwA3TSg3m04+xZ5CcEaXlYwo71RPNrDmkAgQsSTcVAEREMQPx31OI2WRNEUdYRBJj2d83tCUaZQLmh/7nyuPMDdKVqywRznK/cFBBWEYAS2JxF5Y/cd4H8uo64N2oNMBwC+KiI0bbNuGeb+rk5IfYmkjHiNJhcM8wvb36L6gJQKe0x6MMr4l4EzhDmeCz0F3w7tmQm2EncyGkDI7uGBR+oIoiMepn60s3hUfS3lApYFutFCJYezrWB4Vy8KMeI0kY8RpIx4jSRnQledLejUciPkVrWCdNCkF8arAU9DoOJ+gtvP9mr6Pt7MDvFaefmY3lwKztdPHUGaypYLyV1mLwHS+KX92BNT4gjuYMV5pupW9tB7xdxTYDzwvwFmtCffofH26lb48rzdJGFwSVhQTD8tNuGcs6EOWdCHHWrHahBIbJS3PvXkV23yFffKbBPJcYOaLsiPIxvrsZBdj6wjlHALxlsSnn0sCuFboqi4y5w6NXdg/kjjoZ2w/7l9cw5DW9IIYJT6m355YTp4DxK4Nd/bAFbyGyQrj6THW9dz9JEz4ivFrGJiNaDXjdrMGpVKc3EscFXW5TWNBDUGMEcDebNwCjTZBh1Lni6lb48rzdJGPEaSR40r13QdLWMeI0kY8RpI1FrpIx4jSRjxGmJChZXeaUqsrvNKVTbhQ6g8tayxNJGPEaSMeOrvNKVTbfQhmWTjCouFaeoqiulfrCDGMM0ri28SVCiaSMeI0kY8RpIyBrIeCjXmIl7Mkc6hqd9UHCLqSQ451DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoanfVBwi6kkOOdQ1O+qDhFv0PLWMeI0kY8RpIx4jSRjxGkjHicjTzlDlcBtkdZVukr7TpM30fDEkzu+rhlfl0XXSnRQPLWMeI0kY8RpIx4jRz96xAlTJHOoanfVBwi6kkOOdQ1O+qDhF1JIcc6hR586U/tyKDekLSakkOOdMIg37LofxKD197Mkc6hqd9UHCLqSQ451DU76oODyokxQEkBpMHZ652ebu0wTmoYA64KYqCYsvVaNLmN9TxVoZQkgGCkXoY3jiiaMiSL3tJaI8ncdYRCGEoq7oNBW9Bdp0ZD7dWLBz089NvK83SRjxGkjHiNJGPFVByMvy024ZyzoQ5Zy3mxVVniv8pSoytiVZFwEeWoxX8KF6ILRa/ozfsTSRjxGkjHiNJGPEaSMeI0kZA1kPNXVH39hyigWQUoYQUjGxsHWnQUlgGk5gTJQfyHQ9DXBXvFO3DqgyjBFoADjDTiWQlwlgH2JpIx4jSRjxGkjHggxnQ4Mvy024ZyzoQ5Z0IcrCnkGpNqiq00exG4Czb/et5Pa6vEmtP46EIXvV555wu5ZHwiOCqVidpisZJajgQ4uERAUNKw7LOjSRjxGkjHiNJGPEaSMeI0kY8RzJ8bccXYpjt4jSRkSkwMCvN0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjx0RD7E0wzYmZi+uk5nZX7N99vk+EbqNmNpunfS4cP4anFg11utnAtpY5rRm2Kz53jj0w4mryCQz9EBVYy5l0QbIwiMeppeB9iaSMeI0kY8RzJ8bdNDzQW63X9UHCLqSQ451DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoanfVBwi6kkOOdQ1O+qDhF1JIcc6ho1/aWK0dKqoBGkBeFBP1JyPMHZbBq5m7IucEOWkwtyucSPYfgLSUz80BcS6pupLvIL7kNT7ZvwTBnijBwxQE0tQ6fdx5XwpkWFQ6ZGAoIdZzTc5/WKobF7REO0HZna5Bqb7rFo2ZAIAYeRl5Sjy1jHiNJGPEaSM6ErzdJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeIgPha859IibwVnGJ6PuJVK8XIPNHfPHcfaZy8Ti04Y2pm79AN9K3x5Xm6SMeI0kjxpXPTzgwMWKOhhj3yoOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoanfVBwi6kkOOdQ1O+qDhF1JIcc6hqd9UHCLqSQ45pR1VilCxwNFG8rzdJGPEaSMeI0kY8RpIx5Sg+V9criNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEZ141aTVEZteS2KaUyIza8lsU0pkRm15LYppTIjNsY8RzJ8b3Ihlt12Gr19X7QK9g1gY6N9gwDBe+F2BgcMAb48rzdJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRVbjKft094fueh2/49fipdxDqoPsmhndbQjGEThVhNyIu+22EThVhNyIu+22EThVhNyIu+22EThVhNyIu+22EThVhNyIu+22EtiaTPZcsQodlqrDqYMipvr3psiiGzPQcwn+90TKAPkFFJeJKSsu5qx5l8EMk4jYFsvmShP0QKCaXhr3mQfzKR3uuI2uilsoh4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RCCPqZZ/ZcltWB/TPlFw/H97tNMxo2mDaE2ek+DWERechi8BcL49Fxvpub48y39SxIC0Q1w23/XjADZu4xvMBHGJD7E0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGOsKmllCLm35O4VJJ/8NnDcNmD4KlsbElOoTAW3ZsmLZu7QBfSFDGs86OACobV4RzwM+1dKUKGNZ50cAFQ2rwk1jHlKD5cF4H2JpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx/9hkrjocL4ZzwTBEnFbsn+mKa/6iHd235SZJUqPkNqTyc4luPRUifyWhtPhnPBMEScVuyf6Ypr/qId3bflJklSo+Q2pPJziW49FSJ/JaG0+Gc8EwRJxZqFE5k+NuN9axPhXUt+HJ9glbR6x5bbk+wStos+jXwBb2ZI51DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoanfVBwi6kkOOdQ1O+qDhF1JIYmz5qKsjR4IGFFzWdle956vLD6hOP7JpIx4jSRjxORp5zASlCyu80pVZW/HlebpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeJmozH37uFx/jKFCFUR/+P+G17KXSi70xSKl3EOrGw2DQFVtCMYRvMGEY0a+P2EE+lb48rzdJI8aV67dSt8eV5ukjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxM1aeIXnIYvAXC+PRcb6b7K5Hh+P73aaZAomkjHiNJI8aV67dSt8eV5ukjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxM1AMv4t35KFDGs86OACobV4RzwM+1dKUKGNZ50cAFQ2rwjngZ9q6UoUMck8tYx4jSRnQledNUrfHlebpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RxRbZU/YGWlmfpFwK5bOpK39wdZc34zvbAyRoWRHpCPft/rMGk6BDc8ZEuMScA+GLgVy2dSVv7g6y5vxne2Bkjn+lb48rzdJnsuWImJALuaLkbCJpIx4jSRVW39f04xd/e4/Cr8t00DAyuTTpc8V+7+L9mTlIPVH2JlzfOu5d2o5pg54S20IfYmXEtdXYbhaxLvKB648r/66nvjyvN0kY8RpIx4jSRjxGkjHiNJGPEcUfA+xNJGPEaSMeI0kY8RpIx4jSZ7LliJgfUP57OI2GNJBQTM5Grnmau82pFpXhp6XK6cDOhIf/XWbOB05q/1RgMjSYtB53Ng0i/fuTDfky8v9AIMIad6X7+YP2Fw+yRl8EeIVx+JiCLizMdo5Bzo1IfAnb2hIZn6K62hhswj3TN9RPD2mZKeKmBKC/MvLTXrxRhO9B9n3aM3lrTxA/K6/OmIkbqJDOSwuS2L18P0ajf4cizlSPJ1DgMOGB4co526NnO0tYbHBtkG5CemLJMcKrth6K8FuATO0ySaFpexxSqfDe7Wn85pC/gGBTwbuI5WfLcKzYEWhMX5cz4/MzS4ak0Yv8ce+Z9qEz5/bDlrmcub3FoTqzbx+WbZTv/XwAaiGFDW5rnAG17uCSAln2PWJpIx4jSRjxGkjHiNJGPEaSMeI0kZpp1aezJHOoanfVBwi6kkOOdQ1O+qDhFdAJF1cPnJIOLlRcZoZfmeV69f2tPmyy77QAJSRv0YEM3hGIDSbdXCUbAvBsTMyEv9thjr1qnfxVOZYBmjN5n6ENIkHWotb67Pb0c1ucPglccCPd0Y5d7qq88kfw4rIsOViTIBFsVA4BXIshQweyDjYHYc9kXqUK5vZ2JcsaypBQ6r8BpV5HJ5TkYQs7h/zWeyxvMlpCAMYxDEfSnaIddl/7Cj6DvBfOGE9mCh3uFH7Y8YBEVPHMVxeuUAb3dBJjB+JFcHMNPMfIkAmrJUDy8/bCe/gUIwYBJZguApck5ETZQKEt5YTSgq9PFeTfrqUh5j8/LckcKx4fNEhclVNHdbYRhaIQw4FyTQdF5Fqh7jrL80SKIvlu9TXjmukY8RpIx4jSRjxGkjHiNJGPEaSMeI0mFvjyvN0kY8RpIx4jSRjxGkjHiNPl3eYxUu4IUQcRy+N+3mHeK2ipIr+Fooj4wVoKHopkKqO7edTrnwWTFtpbZ4AAjBX6Ijg/Fmovnnwhl97m4uCR3/5cAZZGTGRagNpT4dUFF79a/LEkqmTpsDLGwZnBmKzA+xIA4en+FtEogNUccfvc+mVoDNdHhRB2Am+Vvd2Kv3l1MchzMXn21leNlew3322kIHBtZ4PgzgUq74BtJOcbNxz+6LaoHmSc99nLB642MPL9M+gk/pLbSUXEYTQOE+C6MhX0fwwDFpITrM5fIl1kKNcRd5Xm6SMeI0kY8RpIx4jSRjxGkjHiOKPgfYmkjHiNJGPEaSMeI0kY8RpM9lyxExh0jRd05eEaqBZPUNTke0/IxSaOp8ZpIx59I6MzPX9qSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY/+oLwRWkY8RpIx4jSRjxGkjHiNJGPEcyfG9zfQ5EGUDJ97Mkc6hqd9UHCLqSQ451DU76oOEXUkhxzqGp31QcIupJDjnUNTvqg4RdSSHHOoanfVBwi6kkOJZizSxsrdoHTEXE8BrOmtlvzdtRsHvzUso1WTs+XhOGisWG6pFpm+grsJC7hCVAJwuORjtQkCQQW5k3eUA8EW5V+tsaBkKxoykASjdbKV5ukjHiNJGPEaSMeI5k+N7m/YmkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiZpWpKjSXoG207vJ+EBapmNFT+5Xpiwdtz7wl8BhDtXC1N+YWTqBAfWWsY8RpIx4jSRjxGkjOhK86apW+PK83SRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jij4H2JpJZcRpIx4jSRjxGkjHiNJnsuWImBuw+pQ1h0wDjRRdjR0x21ZbjD1idAvRWl/lvrBsF5sljLPe7AaUScLs2GuemUlXZe2hpFR6PKSpx2VoyeFEGCURqHLtxBfBF/xo0DzzVr4UFcA110+UpfctNPn9EpnvS4HYvEw6bTa+UlgATBUlc0NZ+rDZ6SQionAAnJ6a1DsIY/l4V8k1RdMijfSwm3ovas32IitgBNXkm9E0zi+p9SaugNTReoWKPuza9MNl7FHaU4J3EA7SCyAvmCi6lb48rzdJGPEaSMeI0kY8RpIx5JqzmZ0ILseW25PsEraPWPLbcn2CVtHrHltuT7BK2j1jy23J9glbR6x5bbk+wSoe/SPLFyNPOUwRqRFSdMMZOS+8AnsCjgGOrl65Fj+oBG6/D4qj/WiWvelcxnZ81PpZDJ0IyQ6nSQcNCcjeHqTs+hdDOUM/1iAXmLFCvmOkoWST1zea+bOamvC423pHpcToT7aTNFke6NnBNeX+5UXaP0qUJJKgFtstIkxRQxgUMgiRGybcKAZuH6SnUcEQE8ckNCnkt3k1lmLHsNZoBVJE9w8kYfHwV3BSDgy7ArIboiic4VCCaqodMAP4I4puv6HQoiAbYMlvRQfXivTg126hUdKATHltU20Rv5yLFYI83DdqqLAuoTq27hw7A1XGRIfJAK6szTZ1J2AYDPrR/9wTqlb5+U28C/QkFvFh87L2EBBvOSQUBqxrNbcmDo83mX914DhsBiwQODFc8KpPE9X/IVsFYavEztKrIk2pYTPITJwvx6oSyGSHUyx0Uzu1Z6AWOpdkluG+vl1LLULB47zRseqGKuw65pFm5ZUoYOXDVDX4ON86ZCSkA9m8EoJf8i8HJY/LBbruJSyoQfs4vcMjSyhmZ8TnmZSiSNs43NxEns2f6VvjyvN0kY8RpIx4jSRjxGkjNNFZEkUNyjZANuT7BK2j1jy23J9glbR6x5bbk+wSto9Y8ttyfYJW0eseW25PsEraPVwN/c105dpkHIPMt/UsS9J001B5Nxu78nrzgG0bq0cptsKlfDi62+3IsjT0xHho6ErYTgoyZSXGN+BVbO+YU1VjcA2kFDBEvbAFYMHgac7mN2Nv/PQydAI4wqNm8Gt8eV5ukjHiNJGPEaSMeI0kY8RpMLhnsWg4SD2LQcJB7FoOEg9i0HCQexaDhIPYWm/L4Ii7yvN0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY/+oHXcALnEPImjwjnHV1I1yuVi1reY4bMq2qoujUj87zLBwXnNwZoLxVFW+YmrM9MMu8n7J71r950kY8RpIx4jSRjxGny7vMYrE0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RxRKgLXULYmXjpA8u0obZCgAUBsAiyixy7yuxpoD0FpaEvADmf5sJU2TKGIk+ysHH/RG4GBh5OABLFPZRGqFfZX7znckYbvK83SRjxGkjHiNJnsuWImJpHYPaWXyFmm6nNCoz46djvHSRtA8cJ6RsRN/skh9iaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjNNoUd1Omw58vi6lb48rzdJGPEaSM6ErzpqlZTW4/bAAqN/vkRToVb79/upgyNGmMxk8q6rAs6QtV9tIvplZbBk9y4K4B/eGXUxZzkQNxqoWgSyPJAGBw3TZbHlNqy1jHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRmmz/St8eV5ukjHiNJGPEaSMeI0kjxpXrt1K3yhjis4xWN30KJpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI4o+B9iaSMeI0kY8RpIx4jSRjxGkz2XLETE0kY8Jj7mCAQ6OtCVvjyvN0kY8RpIx4jSRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiOKPgfYmkjHiNJGPEaSMeI0kY8RpM9lyxExMuPRphxllZ3QjH8huQJZtZ5uJdKdLnnLH9CWhZxj1zf8wds9kwyFqnT9Mc00pfLRDBD77SPOwxLgEbFc671hZzgHOjZkt/9oLexvK83SRjxGkjHiNJGPEaSMeI0kY8RpIx4jSRjxGkjHiNJGPEaSMeI05eB9iaSMeI0kY8RpIx4jSRjxGkkd6AAA/v4Fm8oJ+QxW0gAAAAAEpPONjOCT7S4DkfQ+dDjCeu3q5GNa5VAUMMEOOgtRIQm0H2cxICC4iRvAtjNnQBJ3pYu4oGkPsWaTVNfqAQAAAAAAIQ0znodFYtzSzgNKgFOWBrmHEDwtgEgtS4L9SLewuPm0QCHstktf9UID041r2fjpP8R8euByQDnr0nwficBSm52NfkvCYEwLSi5GYGUca4KMRE8drC83IqehOQW7ANoBD2gJP5NgNZ3oMooAAAAF0xYPLz+SkI45mPrjCghhd6kb5hrc8eKhRYjDwbXvhjAJLE0fq29+RupFLq3iOUJf+p9NfMzqKpypTxA8U6FRoPdB61ObkMTNxf+NIvjc0F05LJY6SwV9YRMFS03Ih2GaJd4tb4y+C9b3KAj/5ffORFx2RMAQD5t3DCpSomfKIoIfwo0vhwD2vF8+EBh7t0mSLbj4jwkSaVtcDo83m9Vf5Fe2gFh98JCygk4ot/c06OVD1IQJY/QUxD/uiTgHOyXfQXlubPhM/maAjhhUpUTPk9dBi7FnIgEX6hRsE95CxyBT6BmE9nuqLfIB9I1ovcUqu6Gb3P0vN7tvYpXpLBKH10+t6OkA48COF1ePUy8wC6YTCO7Zs/iLFyzzdT5/tRJK/lVf5iiXUd0PbOnfPop+lQR138NFQ+VHpsUvO4Frlw1hK9FKCSBW8SgNnIE4GsS8OWVDz12UDKWhVN1Q+FEHLcX8K0QJFcYlGOj+hVhhQigB4NCkYnaLoq9FKQCzagqiPOYqbFxSKCwD14E4aLR/pNglqmG2gi42IaefPRCjkIiC0shetk3rAoL5PhGkyFGNZzMmJXu6sH/nxGMElpNOO+cqD9vWIY63aoSvKAMTDDRHugXKfjdwmjqWJb+gl1m2kDK+jyG+2dvfPpZKHY77cRpo/WWZEhDpX+3ijUd073rxD3AYSf61cBhkdug0ThSKQH5BTjpgxKjwdi26rrX4bWvL8xtsCkNGszCfPEJ9QW2UxYOslglbGPseSb2mUqjSwJ0UmQ3HYfpzOkTlw6Fdhm4JffdyAdm5tgl7NPZcKSA21AkhQyVb73RX1gEJA1cWJG9wlTEkrXmgOk6VWLuyyi3vFK/iXZ4W6Xr/bhQ/Ujru9EAu+2JjM0MZQOhkF5f5Wrhk56XUBDT6CDhRgB1L9kJzJnkRE31FLUBlSeTKZQUvkkbdjIYsn4mHoIsyMnuICkEP/1qM7TNWD4SmMuorfFWL1OiZ/Ma9PyHCPDzjTe833bjn/X+hp8zSDZEYB5v1Bdk0UWsQNLnQo6nnKsI7dJM5PRCU+ayRrnqCa5AsNcvDOFhPaI90lrfkuoIxQshI4J6V7A4qrr+Anc3WvBCB429nY8GhJObeUdCArAw8Bs/8ORq1Y0EvMEzqyz2qzLJ+6SRdnND+wBfG663UyjWvUwYoEC5I3EpUJ8UL8u7zpsGdoO/ydQQIbADIhzzquB9TNQaHONHtWTRhvpKLZeaMULpCGl01uKrLDm9h809ZbnDG5bLhuDuqRYLHSAMF1e8BnpcT6i4JASJPqdfkipxEraRrqW1uTyhkb+F1t9H+YXfAbn59t8FbycrO4qBJujyV9rJH6wNcvxLdcv2aZwjJ+G7lxZ6t2gEN9ljdQcpWM0bhI6hVxFCXZs7vp4a4NXFwcxlDTGt9XG2waz67+h+spmD1FyYKaL0z+TYeav9zfnZaSCPv9IliiwFmTTVylM2AFKul9+Bz3lZ6JqOe0Mxs+oJuWxIYjFAxOkH3bMg9cJdrE9PZ2iVl2rrcJeLi/2RF2ljCamS77w+v6pul50QgoTWDTIEFCGYHwJSIb94jtjh9vhRe1DaCGQkeZrlJhgDU4K2GWcANhXDT+ZOJ9LDBUL+g5ELF+nuaCLUDN9b25F+5eodqCO/wmjLmBVgRhKdFpgrP5RWSpd5TfCvVY15Gw0IGJUdLY3bAUBxrAORoSJENSgZhu0AanQ55Cr42UUVEkMggA3QS0y7uGrWTp4K9L5ValDRVnGmYF5kOl5op3DzG42JFcnEznST7+oeEIFebZn1JMRPsupRXVkstj7dpqFvs4WZOf+ShHWqHvwkMl+TXVuboRrMGrdLExjMyjcvLiXDyiGgUS+AEePtAk0vhBVBoELDCQJyH9C0ACFl5D5EVK3wPRTw4HdbtDRnTa1WukK1klsMcVH4YAes4PzjsGxNQ4vhssslqJS0YNjVOjaeP1kLy/Qbi8ZSijArVEDy5PwdMIpkT0afhlB0Hpsmf8msu9JKmc+BWcm0oHfDj7b1YulKqI9QLCl2Rtxr9lox5gpUL5IsrkS+WrvTy488/EV2gcvqyZYLhaMDtRNZ5IKEC4uLFR/jszXJd7+ueE1uHbiU5x+d86s90BBbnCCH61u6GhCuo6i7k2R1elqgUIHEgudQaS0ZCUotPxKb7LVJi6/A99Mqr5Ne2oinq9Zs5WZ0sb8UxNRGJ3O0w50nPwjF2eDOFk7nVBRUiC0edUKzLSMq8HMEdM1o0Q5hPsL//azr3CqcmeqeNgi+ZSnhGeZZF4OVHqXOhmFuv5GKCjndKA+s/b35luQ8/DXOKD6Qeu63YMpFfe9UcXW+Esr04gxbdRpCCFlRih7a7gUp/aUZO2BuLGbNA/L8jAi0HNSHz+wbP0MrgWlH8ScdjN11kF9trCGwel9XKjBi0KcUC5Shqj4x7eoDxc3B+jI2Q5iRG18/jHgKXkUid0+oxj9SchhOHvL8RDcfN4pq+Jpv9rPlIz2va7Pp/ZrB+f7+MUlvMZqiPKWLE2La7hJhKTeoY6zLFmCzusAUpC88QvQTtJIocpOAXUnJ4DT0cx9Ku+W4BtZW+NXs/DuQMK6BWL8NTRLTWSjX9GxnQGyPlnj6IMnwGGqM34C+CgHgdfmxnknEqAphtMpJl+RRyAgFB3xux9R9ckmjc6rP24d+tcp1gSV5cXoWZLVRDEntZS2tz6nTXD0S3x48GwLXtCBqhXT5R5HC17TJ78UA3Siyh38TbKSAfW3Z3F+QoPRi2bQMspmulbuoKmqKL8NpEHNTVYzdRPPyruTCWXXjLkjFQoyUJxkRbYvzxXQB+tKI79xsMAcJJ2ammQ+AOylYqiG/z7zxBAv7TpV7/cx8P0SVJ5oGnN7EjmbZUM1W4Q05qvANGgdQ4UZz+NwX9/bijCrOuhn2kHW/Cs0uRdMkCQU4kWk/D/hdQsYIne8Z/RWxQb1ctfhBdJMaL2I1rBOkvprBdQ7lO6suI4OHpgGcHw10OYoD0aXrCJgxEwOklKtPmGMOvIL8RE2GPiR0OZj4T+QyiacZO2unSV771elxxTi+df7SWztWECoxeP7dtyIbrpwJQQbgc7nb5FZFBUOHoTG+R9ilJZDwTz5F/zvbCpw1HTzRFgwMtePIiDfZTTM2ymc8AlBU0gE5PnqJzz4tKkPkWF9WUnZ57R0ondDpg9NGzMlQqSSWMVRl83NAwRzP4nkTYE+wfVHSCvaU0G4l+1wByO+WZOmPHHjT5h1O8rVOFDqEjALVFdIBb3GAxL9lqJRVmyuOvHPfHswVL3+qQsWTUUKctc+yS8hx//8FXj5aukQGxHUVpCiJhI59+p41Dpsnpko7s7EHUftFj55nHrvHmc9TuEKcNJ8wMKdrLhI3BhDeVP81lfD0t7bN8lXjFA5p06kCESp3DNcXdUNlM5d0c3LbduKedZMwnXly8IBajzoXHwgS7/FIOOGbmx/GcpaFqYa4t52naM2SO059plkm+ouv7WmUNxHs3nFIQApEbJPTSWnDITAZzisk0TNrkeLGIVRtvG/dwPjYo1INwP7mnw6hTfs8MEVxxk13ZGvCm/AA5y+DhLIbjFG/Vu4MnXix0lMUplQl4vcWaT1oKcaIPsQ88hxnHexi56QDKmN6nTLJKhgOhrMc3CfhAK+KChudw7VSJonzmarwcc/c1Q+ZCn3doYOvTbdm1hrPQ2eTQzWGDlXWUM76r9J8ftERj+8V1ML/fxsJrMDWsjHdhToJfr5oYRy5fpKDICT8F7PnEyf3xt4C9iwyx/RKDPyE1pXiE2NVzFQg7KGtYeB+KuQlNObq9m8BJ1UJdpJlx8a9ty/LGst/sY+2l1bJTJq/IF/gD6J3Ag6Hw7fU/qlWMg9r5mKO1PyND+nMb6V+CTEqpFFIIvF88RfCKgLKc4KLgIWCf2f7WsmqVEDCW9YGZmlfW1k/U3woSnm2zRuer9W0lE/kZPNXQwtcykGRceAfBjqM7gGcd3/ng/aMsH/J/jsFiCeHXjOlYNYXQoYHXq/z1ceyGhwepYagPCfgO6pWDdDPSKxeOXv7Sdpdzm+JOw5TJu9RKocqZ5+PO6ToB/kn2y5dBnJXXbuLF+qioy4hj6CWcPU5VhJ3adt/9xHsV3PfhcfyxNkbFalXjNnBXdPgHO9G77u5+uQiRIs1oLGanPUNUAhvVpBtnmWi8guANXVtsJ2qyG0SEdAfIUptGxPOKQFb5IJbZDQO4gw6LLaBrHq7uU4owGpPmVPGKrLvIpwtsl8DEHgKDCu4hbuSEZWAviUti15QNvOrqKYTPnisxZDRBDZDxBk/VjER0ITV24B8r08MhoXnLXv1KZ4xcn5AMK6NMkTCLEFetXLj56lzjQijk/utiqzrW2ws3HuQv6RdMbqYj61JBQTaIQ3ECsqF1czLov9a0FotlCxn3Co6LPo2vYqKgiqrJWpVj1A7nvnoiVzDt1NBwFxDT9647mHiHcYY/KTiBlsSIcoRPdeCwcwtEyCo3b9roXsPdj5LoczhdTWOo03xvsZXZWrh9BtQsGrbV91gfb3nvp9IxJ37x/5GRY66GjeMrJGSGI9/sXFOLeW2kaHDM06D12HsAT0CMwf/5pjjkj3OKeHJPpLpFowWOJb9zAt0R21lLL+TY+aHEf73KOz4nSy9WGPObHZbHqzB6PmJni5AvVbqOv3vVWwxgC8qwJG+9K469vqC0zCNmbB9yV25BbQ88Snp9B73jmW3myuUW29MBnv/WrYBs6yrJGuWBiocGgO00g0Ea3atsK8PKwnSy2h66zIaTfPpjnbW8OPlKHsyrQkC3eIwK5kBb0HdPoEj7Q4cj89eR4djTE1/MF6z/IgHlH9DT0j9PJV/8smak77J1EhhX3bbpX6KzVCSqcCXkfJ9frA6eeuhC9lMwuRGFnGZZeEO+vkBG4AUZqzevdc4MKlMfBgBe4wUn6O048Avy068OlzRPmJv7UsD7ox7qxypE22ttw5K5cu2euloPZbZ4ZPgv+50YeRgRZrgq733jnzGkWFithK0cIIxsb+o4sdJaZz1If/Z5qjH9MYzBwXaGV+3gTZ2pRWJtphkR7+wP+7rpjz8+bRbQezZcgS4kjQeNvHlD23ia+8oDiHYhx9LKghDobcf+ykQdvLZMQAnQ+Hb6n9U1yAF87GS/cA0MuKxurZrSTMJ3a0zjtrfxIcXFeA7Cu/YKNOiICUPzg9MygbiMe+mqeg8RkkDZJ0Sm0gUcwbzYuIOg11Ay9pyMnmroYWuZSDIuPAPgx1GdwCsPmp59siFfp30zxKwA389vKNONKMqz4gBMA80UN8P/BQjnfOFSNjZ+5t3s08492o/FJHja9MyzVfi8XKwhYy8QP2v64DiOTG353b6Xix9XRLo8fZpmwykfHBna1B36kWhWJKrFCCwMWd7lYj7OCVU488JssuIgMffv8kZhxFdu15PnbL8DVC3OocSB9FBpDff3uc9cA9AADZsG8BELORQfdW1GWAjjXB9CX4skX3GyB3WLv2r3qSv6OWf3vunnPvibpCY+XXYjjOGK4X/OALq6kZ+HPyuVAYQy+zqBaqKd7qB0gRI7GQjBAPPAq6ejspaxYqTPemRe48ZJJJh93vaYln6deWxYoICMbipCQVZbYH/POVKeazEtVa6Zof2wvwZbfslnMGxH17WwppUux3WaRcvfvIw4ERCsJoMaUTtfm2sQrTUSDTyjJdfDffnNN/htsVb0emDySg0VJzG7S8K5bPGoK2F88ZEChqssl3F6GbTYFsuLxgqDUStLY1Kc/o4HxX+l0h0bP12p9L14iQexkltXAgfZC1DQ00OXNfeHkj8KwOP6FcRChjf1PieYIMxOUqll6mINqGTM3+Vjh09qgFe9QQfmoM2F9h+ixtvy9jxKjAdquYTErrIZX7KqjRymS1bpRSjnPZyFAKVkI5P5y5rL3CfVaGpVzS8vjkxVvIKmSVe65yXKgZq3lE7gnHQo6EROuh6Ew9YKyME3sVTmVbYBdRRwGWILNYkXACF7He2ztCK7Kc03s0z0Di+3NIgKz8KjAwy54oY+T2dISTr/UHlu8VWhHXhE8MqmJxJ5QPUZCLl65t9LNRPtOOACcJQbv3W4EkFkH4zsmq7XJoOQXrhs3rQHZZLUHhUT0EjmTgf+1uen+SkUviCzhTbgFlunbNEUsn004bJBAAbLFoCOIENuDRo3931XBWDx8wuuPOCaZBvREQp9dnevOrgEY0dQQPZKV0dLiRfpkZgUuP9Cw/BYlzUgfmpH13RwyDq/s5lM/xWslVH6/mFlaWOpufdK0i2g1sRitdzWW5fCGmyq5cGvjKq98PVVMTrySJHNzO1QNC+ALjvVK9cImuYxowrivstIj3L9n4Ioy8QC5BN6WMPVWkN2HVWhcxI6QiouwsRtAt9g0hXzAl51LHEyTWbJMG+xoW2QGbe7p4tHBI9ebDISdEOr1DLsxfCoKnKzFkXN+LmEusi4k3FAELeCeNDhrBBes76s9Wz897RrYbeO7RgjwOZ/Bk3rK2iulprQQr/WGnamX1gf4AEXqb3RQYAK3dfSGXQ147sLwFTYwstG5jFX1KlNbjfmNNI3A+grmUGMgWBOmmxqlUdVlOzRZers7c28Zg8wAKYSu0hy2Jh5I1OWfHRcUskqXfilqHJQeZ3gyol+AdPWTan8tdeKFSLPuEMMzQehvDf2GyGyM3fi0sZMI9wDSQApcZ/TCARb3iIjAzbrsdkX9q3PYKn7S8eqkUS4hETobFv3T4xELayJLL9elcpFmGYYyIDNrwj3gkHn+1Kfm87eRAbUDU7itPDoNLLHDzeIqcb1ijcmyvtnzSgheA/n7xsHzaUyuap+yzY6Q+dqOAKCXJ3jrhxTDkf/2PPRmQ/Ai1GtPM3PBP50rxnUbr2iJ5AtRUpdtm2N0uEieqlCm+JxKgkOPvN6GbguXpdDcRsLx9jCDEv50A+J9FxOFW/nQ9Txa0HBFwFrMaKPk1bQ0j6K+jNOr/00qkphojJkYxetJopV25G9VJYODkQMdKVoIGrLFwcdlgZNMwy3zZsfX4B7YUUBLlPb+3zKkRt3PxvUuBcstdIBx+T5rZNN/OogpohKe2p9EWXkQtkFPIoZ0U+dhzVVf2sB6CToHfXvLBibcImkkR1e0lVd6o3fgPfAK0GkY0xjNOeMYusz/0jFbnLwWoCQkYmwbfSbWbkYVF/cc8OhdUzzsqzcRDR/uGnR7AR5wtowbwVH1H6ltKVeaA1BMsps6cXUhxpH1y9KA9+dX5z0M+lWgjrLRYVXqxX50a9pRQY9j3EHSSh+Y4yh4cLZym/1aloHYCQWM7Hf2ltrTHTcyaYcHuH7iSRHQEGiz3h6/J+1S/sEZ6JzatZdgPqqU97DyG+Ldyjto7/TG/Igc0PjIFYMlih/pJLsqdcda/M1mbNjtnBes7dzSHOPYHe02CB6AcjSqyVeqDSgq6cnbpf7QuS2vorfNWR/TinhJd3IMUNqMG7VOgdkXXf/EJ3PoL6fHNyp61G/YLag/l4TDAT2Nmx+ulUwfYP9FuRQ6bvSl5UPyVkgldPa2vRxr2cdHOzqN4MERkwse4zmQPX7lMiZgfbTv8kNqqi/9J7ulc+jrU4vz8GiPgey5IILb8GP94lavpPm9SmSuvh0sGi1panXdEvC3rBKkQ/yPOivc73NZ2uqf7VGpSNV/kN3rJriG3/2EHSEhZgcpB4ba6/00TuboDjMfkpqEOwMrNQs0L1OfEjU09RH/pKI7zNuGM9mGrbbE8VBbJCPKQqNCbEjiUHVMtyQBcne60wAPKzJfe+oTSJbW+Wm6hwozn8bgv7+3FGFWddDPtIOt+FZpci6ZIEgVfVIxIx7tgvY6/JjoHeNJps0DgA/DiAQ/9LJRoiv9D9mz6GqwTnV3TDBHORTdKh1JlqIKj8zdtU/XTd5WOKGNjcUskM/kTEMkWpuyScZltT980JMCEaF2q+DpBpACEh/ifW1Uej8NsV4A30bpITjHg95VXaZC+lEP1n38iD5EXFD/OG0/0kViFHfVtKXLRX8JxqhJHNZId7D7zohEFpmX11xeIGSC/VutMt5m8rtMhtgRgqGrQ6ozpJRNl0fWGFtyP3a5hlMQPDGgIKX1Dv+incViasnW8fhCRU0O5+B0/FpI1pMX608pGJ8JQQhZR5pXixZFClj6Ur2Qvd6Z9EqoVRHMz7/aBoq0+JWzKsdtuQfYRDlxXkLZh00Y058QtDjrPiYPkgyrrddYfxDKmony19C8WF0odLx+ab1+ajCNatmeXIVnHfW/U15xe3PorUokkbgdQPw316/WztIUvzC0YjOi51bg/EGRbBZkechh5RjM7evp+7HJ0Lu84PbGqySnBKFMFA1XyHvGC3wXJHRFk485KFg1kbvg3H5pOFIvm2cM+gmyUplYCeijPRUilRUqRHtc0BTbH/4P7aABKJ+MqseCTtrSxqtrMsenu9NSs9yNDztP7dY2Z8HUVAtuGFkNkSSSyw2S0dRUJvgBfZuBHbaFiZrAspLTgESpqoh2dJiBBF4RDnvwdWW9/nRKr+GtHX5WTPpfCSbzarz2pY6qtnoHSOZRmUx+dkVU8YPN8DHHlYiA0/VcWqFK/pQ/J9IupqrZnVkU1TEgOsHYM03JjIFHnj+fCTIpgW5am/a1OSbXpq9a+mMdIykpt/6DiuhIeyAMgUFKqvIZMynFfIrFIKqlaOyfTA4qGprjTizkXkHMPgId4VY3Zenrnghal4qhtUJLzx16kaKPw5oGXXTd1Jt4cXX1mek9JJPm/INKN0o7ZEceUAUrTz3j3pimfKWqbvHv1oyfYYHjnKdyJwUiSas5V/vwe2RvF0792JTHC/RSt5vXp67kQB8Py75S+gkXY2VWji9FTwO+d5TL7k0UMMcT+PDL/yY7i3uQ+T5CRq+AzZG3nXoPZm42VaRXq3MPtDM+hHbsQGOjU+nacrycrZE0+sUB3qnghe4MTAZe88w2EbOjZpVsSqq1q0n9aA00W0i9Am75uKiarHLQ1D8ZQ4yoaUMktZvtcbghbRun/cGLenJ5HiOgeZK3dtiJWwBnP/YZahkAgUdxoTEOrc6eGvZLzoxAfc+xEUXL9VH477WgpjzA7Yfuoacn8eMrZ6blC/aFS2UBdOtu0lSooYcUevIzXazTvsYv9x+6h1GM/u+jQGZkOHOkc2f2CPrbTyfdhtKWzRanfQwMc6PafjimVaXNeHc5fFfOlkPA5SpSdgt2Cpk5+7iYfhAQU8HnmbBY+l1CJL4vzq8IZb4FVjVPlUGkCggpi382i5HWQoOD717w+qCKqoTTzAeQCsmv8xiivmO+hIOO5PQhWTadjF15DgXw7lIf9ggli8CG4mr1HYBdCoMaL5CYEBxqaxscJQmgVTTyJ8qjY7WOnid0wq3yFlhkX1fmiMxnqsPgXSY46UjtQ8+Ty3hGojIIXWXblvpAdXreQyNW/R50/CWjPoooibk9XQQQHB2YuHg+eXA20iPSE7jGTPfa735DxbI0G01l3jt7fvUthDyi3NY9/2sF/88s/tFw2nGEq4EGATmuyFAZXDM6dxsahzXy+CVmjplyFhHgtsUw9frRJD7sSnk92e0F2UZAnzzO9dGWlUmyZKPzQFPbxkM3ZCg5mch0ZkOWbhbii027sOMWM132/BaSeHz/rySO/6hJivKIIjQLWt7sfNXKmyvpHB74i0pBKFCJCKOd6cZKhlu5RPlVAuMqD86AlKXekEk0kRTgiJi4Yazx7olKWxdlGh0J7EvNYBujNqn9DUKbx1MW5xlIN1O7XolYRwZkFncOcEnbXB8S7ur6d2EjkSoH6Pq9oWmfdd9aZT0mLeV5wBmFvx0rrLInH5d/mvWnUO9oDSbPahXm668NASpov9WrxfI3xfDzHdZ9VQ0ERocz5K7vDD6ZlLs76beDkd50bnSbUncKY436szFWJzum2itc9ngFaObPFlOe+OXU+mtMnnns3P+aL6o4x5SNhac2IJjFhZHnUiSHowjwy9M4TSofsz6yNkM5ndbYjO95FS7nhFgGpZGTJ5sHwYyhaka+sFMuiwoHifMrf8uSuv4hcAnhziwlNUnK5r48aQlU1jnF0udnJXJs7GDtFcdMZrwiUaIFK+BeT0W3NKYy6HaFu/ts8+n1WVu9WP7lK80iz1gqMQ2PFF0+MOHl7GR51Ff5GnFAg+58Jd8MocugaLEcgfpot3BYUvF9G0LOJRyWkB0BvfozMouke4nPwLY//euaKt8W3X5FGWLGIasqC+h0+5VHUDDnzs1L6dopq9ja8Ic8llKgB1OvBRrPPPxWD053ZT8IhIHsoFECj0KNco5ZdqVldvR+x+8N2coOz8QpkES58DbBz2upmFxiz3buTtvG4CmTk9Dn4jECECANB8fg8o3t1Heg6t8wty7Dd9pEIPrFVGYKW3lP/D4RR8I9fRMJO+U6gqL4oc9lZ2Kpd2rHhHzM2MafWioPOJzIBBNgTAL0iyagWPYySwlMaObTpXA/IoLLje68TQKJIBlKX8dQ7VI9W5vuMOrtHZNfeH2ykBEdn1D5UOsLhj/H0eFpNkBsnQdvyqD+qPnL9PahmcR7ec8+eL+NZU85tboTtiRbC0sa9YPwqZSjuBf7z6290f7niOQHP7BnYcFT++5Bcp9QLl3D+MEnQ2xRmRuH8WUN0ZP3Wqv29E+oiamV9K2mn7/7FBPz6YHI34wtjbTor2mzhyte75R96XNmUPiCkgj1GXF66u9n7WV/f0M0K8xJyVCRJT68qRtHKdhCzNeVgCfAAmHIHj4SBUr1iyaQbRulTxGb3UiIE58UFWwOmvyywPQqOvGQNZ7By6GV9sFxIRnKAvyQNygN7XSIbNj462vhTQ3YFLnTvFadSdl3l+z0+WctXRrGMflBNlF7r99prA7+yCLa0+lEWhEhicmZ6iPqtUtbCJuip8EiNDYlmhCqNTM21/UkMAueSlOo/PVbHkIhIoFSRn85QKKdkuLPoBjogTRkdw6gbnVMhFY6REa2DOr8xRAtdiahcXYc/h1fGYHYhXj4ucNVhrwDhvZQh1MxCisEbnPAJmxNLVkCt8872V9YRjBUxeVh6tnWqn6QTOY+cxVwcbkoSV1hU7OkK0R59Vb3GUpARNq4BPyQh/8wKCMz6dvZjJVG1AFYyqDQmnVaTpPtBosC8Hdm44qV6Ea4WVZRr0ToQmeIrIvFdmjGLT4zNrhduz1NPr2QJD1huU1QFqDTX+Qid/Ta/h8UcAE45LH8SBi1C2RBX5OTTrySZXC0vQNPXbKVV/RDJekqZLOEFV1JvDYsdWgzN7UBLZjfQ/yEg3/dHNg+W04/U0B9uEuGImVOshnXmjW1xz0fwVSt3FONkOTya/i8X4/Nl9DNV2A0UC/zsKzEQB1uZ96dMNQbnDsbAF6gfrVmZR6ZmA4b1lCfbwJ0Ez56xYqZBmb4rN0ZsVGYQ85fEcaBcRo3aK2zxTpnoA6/QNWdiL3Idk5rH4Xpg8lglcF67VuTv0zTAdvk6+tKmeUeM4L8VklEZANDd42JSYWG2Vh2bKh0y56diKiULv84u9dtD5JZsxsVuASEstIHkYcoXBjj0R6NorJ9PKF/rx0+0EINzuAG5eHJJYhURmTerWpBtWR9JUNGeO8Sdq9W/iiDTLsMWskLdJybDLxCTi2TqChq+wPPllXT6pKtRT+3Nwsxi+nbr5lfKyYQXqrnKXYWQEmBzHqy5Bau4nQMMKvcuC2jpi5e749dPzV+KIrfjqVpP/1/OCvf11sAcNArajT0RT1tN222VQbp1Cq3owu/MkIKe/hHZDM6QT22jzudgVbrC8Lx+F6vhhSMBSevr/tLpdZdZgFYMspmlDNz9FVOHhPFt5KmYI0+yHrExozZSEE39yplSKeMH1vKaEL+MnbrQdFPPnJb24TG+UnIcYQOvkgDrjFmQEddMYfmg7nMtnhopOCGCoRDhzbCa7XQ1ZT5eYusHGpkmrxk2hlvBuk/Bv0wuHgpXNFj0bx7VQlaU7jrMbyVa/NAab5zCizPzqe/KlHd06K0IoAtXWO2uErpN/wuFMH6UMmSY2RkOOv40Iuiba+AfDbixMMKd/kEGZmhPo9LVP2MqtNfaURLwIZkKVvXZOmLa2LOIq3S863aVU25lmrVUpMAwW1tqFZbLNgxqaTq4YNNIQ0g+WzrYuG0mwHUw1ode3zJ3H8OlwpfaPlOwTEuPT0W+ocZqx+1sCZPj6f9ghJctGjhkEOxqyqVDCM/rhmKuutX1gaW5cDs9WKpF2ZYA9uD0IfJpe7sGxzdvqPmrhBUjHIKDLKY8eor9TuGFLkd74ExoHPMV0LAT92VArFpSs9Ns6oEI0kABT6hDL8bBhcYBxSPSuxbvXI0u1xwqMCJ84wypQElwQpkf7MtnDd8uwAsc7a1Zzg+Mz0VkyC+GJf4v0vZ+CSahqpBsa3ILOhH+uk+HQDdeRVg8b7xfL7h2jA+YIQjqH1vcorow4YL0db+4kHlXdO2vR/uyAeMSPJAn3Rn7hSfdflMVPsIdaMVYEqu9us2wjEGKZVvSbM3qogfHP6GN6GjcFeRfum2kedUsgbWaVI/w1AmPTSjlV/oHvoA5LRoJZdh2H1a7OFLckTc9cgMIjxdB8bamqiuTmXQ0qhTxNhA6h9FvVOeR7D6G5rMV7rVeu+bLcJ3DbpfQlXn2QvpJAKh1ANtHP79qO1p4/15GQeS142l/oz66gdBgWJBIE3IVIDLbwXtAiyVJBvg8l9iAD+1d5mKBIyyR6rg8aiT7Z+HAPtHEIVLE6wFwFPg027zhDpDorY45xQIoBzgx8xUCZs9G6lV+Os+kktYp2jD9XxA3bDyTXolOFkZKDLqBUDDu7p+TeQg3/ESO/GINmHIh5gC886U6z+lc7Z7ooxWOKHYzaNgYHAS81uR0n+IGpxYPIOGeELe6RM1E3V4dWjrQ4/iPZSKUGopybSU7dHp7P3p9s0hJcJ9GJKM0wUfQdh4HMGlUS4eKZx+eT/3i15p04vLL8Iw5ze5ko/jNYTQgI6p3ZBjXYWYLG7Uay2CyxsYoRo3CQXJjSDnx8Lp0qcnr37ZgTGh/U3+qS9JWFu66IuFqTSOI44lI/LKX+MyMKwojTwMHbNJk0m+yz6pi6kOV2YIJzwmTJ3JsDSeu+B2c2fZYD2BnBRPxyPs/RkLTqsZ1wg6y/4Xua5htppC7ysJB3T8lCz2CeqPlr5iiMhHR0va5Tj1PWzVrok9rgMpgR1POKJVWBk2Md+wWXBNFUWKVuVPsuC/aK7KYQOutAPtNVzDPhU0W8NjK0i2xZdXbhBAZq5/muAGKU9qAXq2aNRxqiVxgME9jt6DUSdw34OREUn4MJk984bUfEDHYxHUuvkabTr067FpgeOfWJvQuszY7HJNTOq6mBSTXNmfsYtgf4h5yDERzqVlPZq5A4DTaIMst795S7yiPVm/Xg62iFvCBQjZv+0/14+o820Go85iZME+cZ0Xun52TDV8d8XYmDuuYu+68d5/hEgB1gysMMseXFUunhjlurIZhiElUzaqE2CVblL6wAxIe0DAv19Vpl2pzJ1W6wr8el7/CImOyIRjYmGEZ3gJ/YGUZT3M6OtMT+gS6EY1HEAQndspm2zEY7mIIkWdpAJuYyDX025ttLq10xsHhc+XeQV0W5evncZMmeNT3e3UIMMdifqShaMb0zlDd5FP8x3M7Jb/ZdkTlP8bcdY2tikjmxYwG9TOXSbupwo0ORTMTt9tEnErtqdCWXNMw5opksNpauxQmXr8dP8H7Q4SUmJnDrBMaI3bLULUZOM8sERpAwgAu9cqs5YzZPeac4XHevVETpg9z4e4mew6hpD3+7olOXmCI8amoInNlw/tk6w0mRoClN6LLvoo0YiDLeqODVsBvLRWv+ZT9OqE21r10BtuZI3ewoeqMwcVduXKUUUxNzAcAS/ABxCfxNEj5399Nco64pXqrfTEQei+Gp5hrlMVRURzQX3P0kO+42YU70rmsXHQQSqvyGyRTjdAK1+ezwFFafTYpdjVORqcfJq/xJfkOmyqugBY2VrrfY1oRkzZSM52V432gwlaS3vJ7HIDMNVSLZj9y+AqMocJFwAXG1eguenkJFcvROjT7jTbVwWNzzj+poEJyaaqbcv74hmhEi8lrrxpMWiftuMw/1dT69Czfsr9lLgup3jfAvXO5yx3aXa0P3WiviiVc+w2+OOatzozzTN5eHE7BQ4FUVGJ5gMb920UvQGx1gXIKNe4CltsctadeDvVFNanEXwoo/z3BSKqGgcNhEDw9GC4wFKwNh7MC9+34X9kQkp3ep5BlrOF40ES/KpC0eobyf6XuPr5LE5wTgosxno7Czr0fRhyG8RFl0Z5ItHNbS2jAsbFTjM9n64Tiug5+bAbAMQFhMTjGRqmLjgzKQsjxF2942hfEg5V8sKDk+iMNuTqthJYnYg6gLWUzYtmgT80fWeZwdLFBaMjM4DxAW6RR5h2+AAAAAAAJ2JeTs5MvWeWTh4/87fQXPODAvY7WiLg+f+P/VEBZrV4rIN85kuo2tPkv/1G2YEh5dXBBCeg/FQ1CaAA1DtgVXPGCsYG+E1UFyUl+5moYgOVQLvFUDbN+FSbSa7HNa8Jg/+DVgxHouVn0mXB0nTDNKABb2PVAuwdJEaYvMm5zYT6zS17F/FYYmXrfpvMBedwVo4IQQwMWQ7zvf2lquwspFusAXr+Fxw3LNewy5hyFNRrdaRTZjeMDn9uDoMBHy2BB1js4fpIjTHW7jKLyxA/25wvcayIIil7BYgQNkwQkUG9XjqhHTF8AAAAAAJGL4AAAAABNwP6DFOUUAAAAAAM7xNKG3s34FAAAAAAAtjEq/W/mHfTP8snIl6EjP6z1XXMtomfCfD/Khxeaqp20z2ZMnzBYD7YsOecI7aa1ILwjO2me05tjbJY3NIXTO2me00NhphLSXkBGYKYs7ffHzt7thusqgkOGy/QQKdlsQQKdlY3tpntNJIPuZF+FPPAdIqeAemvOhOujqDPeO4CiqYZlmIFxIBmrxQCZNQD21TYy95toUiMZXpYVyeBzjb7EmnF3V2yrbYakcL6KdYbEkgwMHCKcfOjHSPlPPAFJLbqzcgvPlvZdTM8h0zhOioDRy8D0TGsJKhE/ERLrk1CUiLd3yziL4Z22xJO5TmgVRpyTixw01QdV5gcxN+GUp7yLtgB0XdMIfgruCkwV3BSYK7gqRC+vChBZcdh4fFvmy9yuTIRrrgtJTPjc2qe0lSZp0H3Bvcmi3miN4gRGjzyMiKDTdwoQcuHmRpmpFfU2AHtyn4izI5F02ivAHWsYPfhagYzrAAiqilX8YvsxMi80fHzgjKhh6lNInC28ZVof/dFW6kw2iMJS8OhMgMCcSqkd77w6f1aAGtjlpcmQw4wbvBeSAIqopWAKTzxl+MxTDGdzWl7aaTC58j15ANjea77Lr0cYaI9glAtcjInKuN3JEOMr62seDUzEyFiYI37C/bkHt8AAOf88K91X44hE/AC+GDIMRARt9ri3DDim4yYEI+kb4UAZl+sJ9D1vDREIGYNR04evyflFii5ctOS5hBtyv9Yy/Dj85CCRifTv0skGVxzt8qWZfhcW4DB7BJkDb8NuVq5EWpox1qeaw7/qgK/wk55d0XdQO/oHu/kWVdzdZB4AYpuZG5VfFxlbrAmPQxp4HW0QtcNQvBtNJX04FI1tyBoQy9amZrEjUTXRaf+hUojFZbRKX/fB8BZLTo2KfzKvLe0YST1FuBdMlZd4Y3Wyxxvbpops23l4VlThnIG+7XEMcthDG4SBsbuWVIB/fwlFVNC+1UnQLFhCOYOaF1VGvNtK0W5mjXHJ3mC8vox/VaMWy5eqHZjXHGbxxbXj8tERaKffqUjZctDQVpTqitZb/ylrCpQ+doTvHXULXeRMarUa+/iLyaZbdxQoacaAjGu8iRZCY0eHQ6DQb6el9GxPpo0DyGr3ubgXrT3VZXa8oP7xGRhFzXdm5iHViEDqx5tOIFrzUcc6hFaQq4O9/DRhudjBr3t9tuUs2nk06Kadq9CymqRIWmgcJpUM2JJTToo9NriaPYTZqgeR51p5FJB8d31MuE1YJxszXYLsAlrXWxdPyaFXcyKtmx9/BTy/zu71J/tqKt1stwCuEH1s+vbj1sTVQhuIrMyaoutyDnWNYeybS+wP0yUoey2JsayAFIQojM6/mkNVvjB46/TpeNZlzbeUTJLIc4jc7YexALf3yv7AqofmbPqOMPHJhBKceN/207/tj7w6CZ5MfSLixQf4ikjLA+foMZYLt6YxGIaS7TOURUBZqCMopXYDFgxMthFle1/3OyS/Pn81vxISnDH/NvgWgwMsEBTzNE9gN45hPHyF6YFsKLGTug9yMOhfGJb5IGxQn58VzSa2zklCQ21djm+Hu1Xh+0XmF081od+JmTax5iaoZroFgarlbyiWFzGV+Bm+lrJHLjFTW0jrVZsCEBFqAXHX5HvUDIZSirPmI14jYJvIFRyzIYmmtkNHERWICY8+1m/dqbQsBtE4jYRUAGICI4ZN2B2/Sg4M//+nBNML3ngBqvqN0zm/4/mfvyBtZ6HIQYV1uIP253d/aHX7hh5jcf2O6hiANQBHQp2yWkt8rP14cKLuxaaS/5OcCD9GAH0QHvolh2wx1OPgWPfUcTcAR3elTi4AcQnXw7sJqh4ti25VdG/LwXeOchIM4XyElD2xV7mEVjVRZ8RONVCLN9HbhbvkqC93aFHkeZ6cdJcnEx+IzyKmbJUrlis8Jyd3GM3dENwTIqVTNG3NQab4onItEaWzP6fSrnzax5XDf+Db+2e3Ve36io+Ukw4CXTCer272O5vvgI8PjxFJLyE11dO8BqTUBhv65igo8z/Svxji/F3vcULxaQQM2qtiOSoUzaqDGKJlGBYX/Wi7n28OJWZZBgtfkvZmMEypB8BsOwMm31bEHgMo61/XC0FFDui1WzN8Fr7ZE1hO2rFjgO82U3yJcZiFhBvx2/PzfgYk4BeufPXPaLTKfItyXTqQXzYwJVUhk1O0GJWbIXijdygrWEroC69eR77fi8UdIIOxj7ygGWpbwoAs2aHKqwClKnvTlTOVoOhmg57HJJvGhv58VMaKsdUqvrycmkBxlIzIhMAIkcOx5ZzwKlK+Q6RLqEuzMEaWYYPJwsXi1j6ZnBr7IBeMGyZ7ZdBrMHfGRjA8bVNxVdR1xeZh/c/tGKiNdsojj8OFHx+XpKNWd4QRcxc4NDf8ai7oTANSmczT5AEzvdndq8f4hZcdUOqjlIwvMeAQ6bwWj8MiRx9nEwAuI5wmVqjleaCS1UHtueOxkO8RTr2x3DhF/gGRyXAKXwHO8ei4/sXceDbXUy41lDaY9gqLUWLaJRVbf7CFU0595x4bemuu3Q14cLQPiUwH4qpojJHzDQ4RKQ8bQ2cj1/YdqspKgP2CyE6h/+h2UaqgiJ9mCQT/mwpmcg2qCr77rIgby1xvf7tlz+Q+D8O9tmOMaQSfuLhR3uLjQ3ZWCS7KwWC9iznbrtV5eF8FKMNRc1+SdUvQIJwxKd5OOHHJenzFFSCapqaQYvZN2Gxami9oCf0ZwK1PLqmWpW7+ccN/mpcGf1x2WdAKqtC6OU5bvqClNycEx25x/m08gh2itjXGfVWnEA0Aq798FSPjiqA8GlKPWdO9JYu+VsYVcoGbUU2cHIhhb22cuoKx7vALEVlqgHs8t7cPqgE4AwQlRF7Tqfkb0ylze3SSDvYh0IvkDXCGbIHIzWH/+ISL9Kj0rXI0Nsud77dcJgBDkHfOnAPTiKPkspzK7p9CHzGc5mMLEvCRd/0rvg11uxo5gQhigfgp3lKHw83CwmYPVl/7AljT3Zr4vjlvBu7bkcCK/zS/fJR15jJCdNAs1LzX0O+85wGIoEfPVAD92p0JOakxbkSOUOtHpKty0L0KmzKFpBhur5MjAGy9Reyo9SDy4XRDSF2N2bhjoFl2jYREP2Y7EcVyTqN//aMxjEzgf+6qzda8Jul9fuA1z6oD4TJuUdvLWzflGoYYlKEOh+aTe29YBjNXb10Y2dgTdeBw2iDQTQN21su171FZgWWRzPcBWhRNrXKbSIUxGXopoAZIITj7TysJawpk8yKAqPCPoyKUSXZJhHb7srNJGzflMjU5XzOFUVVpKYQNIyyeBkDmIFVgbs7S7bFy8dwMtsdjT3o/NnQWt2ujsr67J1uNZcNUKVm9xZ5VFKzA3/3rg6bXbk+isKiqH9fApJ0cLDFcSj83A3QRgfRbz/17kIJit2GGAiDji6tKLmPZNYpazuH9XqbkWo0UdUzFb3PvKGiAgMYKVPwFk5eIrI5PGz+mMHdD3w5kcYIuod3vW679GtUAUUvK42mZWFsIAMqL8DVJ9U6rpuzlOeydwYXLv5LX7o0IiiM1NI7DfIxl2jPMQbvwRapM6jJpXQx2cO7LqGVYnN3gniM8Zaefg8VLgKIu+ZsHXoJJ+zWGxF8zWhzPE7YoF1PdfSe92H1aVFeqNzksjqy2qn+7umcgs+BlNAkZz9uXpg5D3Z5/6rhpwxvCrhguhvENV7eMHfb1dG0s2l+sS//dg+9qTnkKJWOOeZ7U/Q8y+IeBmXmAubmlynQUgCYL5uhn+rdXrEjTGB42Ss8bVmlUupgsCAwDfv1sqBzERx3TWdSpw8k+nOc7B9uBo93uB27VZ6mV7WpOOmcpYWqdONo7cmOp1VQwc+FDXCPYiVcelQULswRpx3HkKbJPpLSulvPbzpKqC0zWmTB2YUCGOKWD/+nn872FNn+0e8kCKiC+2VOXYzwRr4I5iilIPkDIRPHFND1PIZLqWxWc5tq7OE+pbhDu/wUAnwudoDCuFuOsFt62c7XRAUh4yLukMObYwK0k+RKHojk0UPnYSThvspssY2ayLEUgUmfSJlgva1Q+H8DsdM6CAxKSydBF3eNoQjBbAdNBCNg0/DS5VRoY1578Ns1dvHPy/jBFw5rjMD3Eyd/3YIKK/2AAl8z9/snqOgqan3yVqJPaE5VPAZkftHrlZMDz/1Meez2yjVterxLHkspnVYOjLLbX9A2QAXXQK24hFejNSfYKUpfeqFNlCvDAk4TAp+5u3Vx/U0NRRgznSqHGV6CK44ug3bXy0J7iGZQj5AG8OnuZ3Pc55/uELGQwnRmE6Db5fz18Y9qJ0gAvQec+RBOKIIl/BJOPq7SQoq+AkNsMrwKODKV/a+EvaoyHc+ZVMU/T5tZ2l9rRCl6go0CMrN4IhDZIynKsatR98P+nPktC6/xuDm2DABYvmScSkXwrh559UkmmgbBY+Fh206owhaa2eUpB4W+JXLsv6zIwYiGHfn/RQ3+xj4n25W8pJWeiAWOKTd4rTSSJckRE9UGWU+Yh9/f2881nrzxPuovQmh0SStNtsoXPhBc338lBIuzjYPk72McbZ6eWC8yd+gUzHnEHI+jBdW9kcSPD9zsA0XyMitFqjQ88Xk8Eq8JQGx+EshFxlUktAgRcOfViXdt/K/kGs/1LO7u/2U1rumzmiQNVjVMLIJIed4ymawmZ0R/yfyt/gkBlCMdPGwcSlml8yp/ZwAes4zzUdKYCSwQw6pOA38MVVKpJ2niLuvHPsS9awSchIIHvgb+wPIAHpLo7ymzv/s8RWtOGVw86jv7wUW3yTo4Pp6zqkeBpPLx9ig8DyIZDXBHEX4XwprqdW4pvQ2m5m7ySuZbKQkKKsgsCQif5vY6zpsw8QCpBIrpLUOKpJ7AgYUemvmDdkso95budbAgTm5itH57NxgcvUvhUgGhOEEtCJnRhs4eVRss0P5vwB7ZmBJp5Mn6AyEAFOrpKazlQWcvgAJCtKja9hlOsQyRe72bEwzDG0dkMuOSwfjFbESDz1r6k+Q1S8JcU2R/2Xpvt2nUY4KN3qqksf9pT3Mm/irevFSjrER6NYhYX6frIx+Q1Bdrn81MtVNh3gHKo9YbbjECUz/H52mRuTozJ1v6diAAIfwvKtTqGiY+rrfXABaCgTK2/BgiO1iiXPRSVNGMqsLThZtCbW9Wzjfi8EK3XzZKONwxi+YxMW6K7+OSPKFTvXAFVfoZwmji+URWJCIPbWvFsVrjNd5YS7PMTs3Qa96+7GiThim+JTEHQYY0foAP4BgZZ9P+CWMDWLVFIMAU3FBINQEpCrFENX8IdkEmNmdkEUxbw5PfwukI0bambp/dN6dx+KoKvgukyme5OtF8ojXgG/48rM2qrT+MFtZHB+gpVw6lBEs0Tz4SRiJynLx8ktipK27QV3e/REu1GNZHbIpqrtCK0hnw2SVhieEX53M81sEiCAEmTno6/x2b+xSFsrADRSedsrQie80nIU7i3wHH6vJ4d6sN8P+cz9AeTcZXRRzbQKRr7JXnN6prreF9LRympjRMuMuZiHS1GyG8nN9jEe9+2w+Rl6e1J6+Zg3C+faLM6U4D3APYRgmdOnLz9j01wwAYQtONX525bJdabYfExITny1+Esi7yRA5AddUau9ppneIN+tGmJ3K2mlSwjzQp324vbcs1Yq37nIMuJ06w2f1tKvtGI+JYdkfn35xJ0UrNtQ82NtCQgTtsD+02BxAZb/f8fdbJmH7g5NaFKHX7M2DRO7oInxYqleBRk1X7aJSNmlkGBo8lUGQVmGopARNC634AhAEH9ppoAzA6igQ4IlmVeS3z1ZL2eybU+7sg/u+RG8YPluMJvQy0Fy+A3oH0MZGtQOCXTyqbSkwoG31BAivOzftTLDGHQzPBtO/QkyLCQOqOgDla6BfUV4J25u5S+i0gMFXy90q5kGu6eSlKG3T/A7Topg20ZhfUo/NZsuDjyq73F1H9CofjDG/ibMeWNVNpoxRyKmpJcyelc6hwdEdAAlb4DpzQyyIl4OO0ws/LCD9NNHpCvYxNGdCCDTt1ivGJoz7+/H27ha1YqfzzJASiR4TMsTaFtAVAR6l445joGHJ61kpiesABYjHI/SkHBSnCl5MEix5olwmS87/houMgc+lNmvhoxMHJAungbNllJ0ShPbzw3wsrrYK5FAZup0aItncHx0kZQkx0yPzoqZFaWLaLe5xmiIOFrkYRV7JzjT1b2WtHtAmgcJPB7ToNVjrfhuN7wZWkZGnmtHHQKAnrf023MYW8KENPSDkC1R51H5TCknBi10gmnZbpJ/h0d0lzRp+2aFyfWmlXGuRn3jC4p6GVkiGNFVDP5p6A0Uey20SWvYu0Q7WyL6OQFlBgr9thwJxaflbrZ4hQis/gVmY6AvkNzsTXBnOBEnWKPMMWaLoy2M4JV6HGrNO7IZ04wfx8hChFuMI16Goe6dZcDCQwt233Bb8DCw9qLGv6NLldgEfUnXErGQA1MHxvcRl3j3A2dDDrJRykVL+fG5fp2M58P+VSwrWV+5HnGBuFiaFDkWTHwIgOQxEcqMsuAsWMRlwAc1E0SfmS/zXWHbYtL9TPXiXeYTy3ObRidZCZ1aBVn5xMRW3fCsZAzI8WJm3aVI+zCZVpd3QKz0ex1URZVxXaGTU7gk+V9OJiO5Bibyv8VWTWeeu3u4ShJS310uiMuwELu8+Nb+KPVuzqG0nugL8MGcvweN3J4eoxJtet3Hz3hkgslgauZyCdQUrXFxqF+UbgJnP6ksWUG6DP8IB1DUec0wrg+M2Rd3js4u9hKHp91GL4HldCSWIIAYJRzYrDlVbtO+zPX+2xDrkG2mGVqlw/jd4UzXEm/AlNANox3ky+JhoTgFbb9b6D6XLUGnNYkdlrtx1p38KfIxuG2fKF+UGxqZ02tOpnjRr/zmNQYv9q+BQAjgKEd3qQEB7lxn2o1Wvi1oJgvhyw4o1JPBHdzam2wXv6XZboR2Hg0FYUmoSCghOtaA+iFLj5ZzFLZUPX6c2tYhmh3hQ/5QZj47ShALcsH+vnRnXH6cBrIKyjF4nK8bsCq3zp9Lqmgu0grymUDyD/oTfz4krDLb9DwZsDu5qEiJrUefyej+YNioLYtZBafmrdH0kru/YRAcovAksfCat+mus7AntM+cj/eEl0115Q4+7bZRlNkQMecsphYe2nzK8zu4NPPtAML2ga4qN/6xcGXvodEolkI6ojzbiJNrT+pGbwkR83Qi0luV1IYA/9ZdQz0AsfjTKpfXsUvDVE0a4cg7sEntuesiwKRQ+tvm77OeRmWXzQlMDfdylrDENZ9beY1iMBCmoE+lYJ9S5ZJiobi3sA6sbyg3F5WUN6a3J3QtDuq4n66u9EyhU+f/h2xCV1V7I+st68yOzOi9dQYKp1tz7sFFPFbut63Dvoirnd+5PZ8ibGS5hb/jYvqPvXlb2G0VyeVQqO5TwOtJLm4pIVPSehleyLt+1993HpSvDn9FKoRSaJ80i8olhVwa7C542iil/B3iSiA5cAFvmZQ5Na69elQolZCIN3Vl7jAEKhc/FqzWm37MVnaEXB9iTa0WRO+ss9rp2MIqxhOSGSMZ5cfZi0l3Ia/vFKFI8j8KnGpiqPV2XrPa3BrbjvxqES8os4qesCgBQ3eQqWKXj5FyAS0UShrPy2ljLnTaBvDWNCaQ9kGuqh0d+lxYA8HQYPLauqGUib/hkxc4ejsDI2vkNkkyMun0piAEFPXD5h0Khj0dca76v2mfwHgKDrUx0/H6/SOYxyqNDuuALLKlH1bciCRGmqeAPLeT566yn8nsxk3hDHEAmrhkpzXvC2rl8nDszvG7lBbwhhENkUsE9ayHeW937D3XvFvO5Xq5WubVh0ISl5ZBaHTgGagmybFrZww/LAWi3d5Xds7JeSOKX6uke9dfwE9xK2eN93blJMqpZWJjJnWtfqyfgXWgYO29uIojjr9z/2jt+lP0Pd+nyw0Wk0DSpMxS4FaYpcFbu9LKmTolA+E4YU8k8BHpjtFIczBVlLhR9JD/Dyz6oFFOd81q1sUJKjYEXUwk9F/khcsbMSFDMS8o/DkyOns9I/r+rcTdh2VuPr5GgziyfSn/nYkoCqjhJ3zQjYsOEJ5H6Ef9/TIU9RKWv3fU99zm/RX23Ms9bDYwMevmB1Vj1IBnx7lY6V2pUgt4o6CBY3K5YtDEKWLbhA1IglcyZ+5csS6P/VnleevLxVerdD8w2LPk4J/LkfwlBxI5CpilKfIyk65UbPdMlJY9W6ZH+C4AtvdcM/laKAwTHdC4v+Kj8gopBMxcQZT0kKrCY/dEaqvs2UVqb/vcqH6YWMBfg9DoGGoV9m7DT5G5TLgKZ92swepLTkM4hXw5FtJPPXEc7MyuEaW3nTIL9BLgMwo6FU9Qdr0XV83/4rNQppO5cDXTi1mDPNSEISdZwxXM75SsRQi/9Z2xbnfp2WGca5xCGzqK45gVWa/2TCcche9AvN8i1c0QmSfNzHTx8zTLCHGDRwoSUUq1A2nM/ryZyNqpKkLf1EnmE36UDD/uLwYqi6VkHrAwqMCiup+IglLQAF4cIcQ1PYz00gvs8Hbv3I6E5iqfAOP1KXFBr0uju99Bk2JngR6kxNt7BamcyNYZY5U/VV90kQHVvvD5NM/gmRsciQDXqEfdkQjiLWIxwI5T314y6F1Z8Zdn/a8m/jRlWgZI8KkXQspiuwX2gCZTsBiA6tnrfXGtBgdW7KfSezVF9PeHPc6zbpmBVylj+t3njcHZsnZN+1CTIvOb0+x0mgU+lPdungVqraK42MdxOn3jD0WLJWbMgTrE0GMGnLbBay2rFI3GS8dHSJaQOdVyvYTqgiEEZp0a86EPrQLlqlVf6AHPx/MZf4My3vitEiCPutCCC9PEQGc2DiwiuzIXCvyG49rpMF+wXB5aTKkZOPF+ZIFvJbEQ7hGia6IPTLySLXbFY9/CTbyxxG1TKeYf9boAqol/M20gSAVdOHQJ2su3EeK8H3jgSZckwItstbKQ53o1omJ3i6XOsGeyac0khwSa/trDWDCxEaUWoykBO0TERgo79HgWQBAKGTiSGmvWkdTNeO4BkDIPCP0uIyA9GA8qBn7/UHvlSfbbpaoQMk428+YzxKpsoxAaqzvsrLZVnB8+xiqGtLd0Lclpu5HKMIjOPSrOI3DaBerf5GyOhIAqqIdeW3SoktnGvc0Q3goCtvFaj6xCyabAsNzjK8j0awGFoY2vH00zPjrcwpa8LYQUSa7N09IMDzG96LUUc5o/0FOAO90ZV+pGCFP0YkX12pP/YkWMuudf/jGxS9JgrLlcka0ZUJJ7owR2SAwZKn4TqXrM925EeQN7YNM/i4pwdcx7L2xO18agry2oiBfJfdHHNUJLo140NlXg2sUp6QoYh5Dd4/ogOKmxgsP3OL73XdRhVzeW6UB3+nw+CuivpDhLHQ4WO+h8E1hMh0n0vYQ16o+nIJnTu67fh9hPVDJP1Tw78Jv2OYcIGsT5B4AQADXmIZsSARAOaO0Gw4UpZfu0vdq/sGFI62bzpHrGAHsAuFXAC6igz8Edp6Fc//KabAMgu4qlNj0DXIOLJBa5T9g5Za1LsGEu37e0jUqFYwxIiEJx3wLQTNVa7LmRMRiZk8VcQAPcRqp2DhLA7+1rEJ7VIYFn0dDOZs4xbZib6R621kBnJRzgoe0aT1eQBZO5douwfLRKGzfKX6rbQwOhqGDC8BSXUA4mc6nzhs9zUUSiw9lZqpcow3pY7laPr2LSgGZRqNOX0MNl7cerI9wDbIhPfc9nS1MqVlO/Aeu9VsyiDhrJ5jWpy8Cs4wFWzQZsnYlmfCxn1rut6WvJ6sfe11YeuVtua9AFmWpBn7+eqRln2FCXK7cep6KDkxd9AYuu11w9Kn6OFpxiAFsDXfmC67sL4ScEEQICXk+8/rQTcN8dL7Iue2ntRtNqn+qpCagYDACEv3H6fjQe2JAbJsm3vBqqJpsigIKfi0w1x8c69nYNmxTc7/d2HGNhKAVPjylOWFXu9N4HyQspTcX9FqS4obU8yubYOiaZ3G94l3t255I0vLzJDN9oojFA8pC1tgppgI0qgFTzIAuhH38G1Fa9NAFd4jF37lnfBJYLexo48qKqvz/7Dawtq5WZwaASd7wWFpfrFscRIDs3RM+8XCV16Vl3fCV+qkmCJFXVTb8WbiiSaIJBxhIX7XqlAjm9Sy6en0eT1uN0Lm5YmmsH74BkiyF6rLyGlIf94JPELRs+lCesh+g74FTRV5hEFaHXflemedTu7xd5o6zfrqz3n1AfJFhBTdL3SO4OpV3QsCgTFvz4UAi6I98e7b+chqgl6QiKltiu+OXL/EE1Q3/7P5+VFb8EFc/maK7AOgCbtk5H7X3L9IRhSyB2nhnGgzWBjSN1cerLXq3rmRl+Sppm0+k6xI/GSrjNX4EAZ20tCLn2s0aZ+yvadnq4G6IpTx9w/iRDFHny7BGXpP8HalzavhnT2PCqElOEFIywugxH5T3AHJquupprDbHplqOlOOak91WLG4704Ql3+Uwh/4wdF1RkFhqWb0ZKetdHCYdP34eehi2Jt5LiE6vX1Gh289OpcVZjp0XfBUBO0J7GeQ5uc1i/6MFnTwcU7iD+5DSw98P5V2dSz6Mi8s6EyZQ8p75WRC8JwRjeux7wSwuEozdo7cgdatzdAg4TT4JLoE06FKFMVhHi1NdyyeQYzDPgdXEbvB6iXIi+Bq6z5EtBIYV6fuL5QyTdU1gKfmbKNOWJEcGPaNoJ50vQI0LcRzrTWcdMBtDHd5pHWWlt7bbok8SVC1GQAAXgThhsSFjzGswfRCU1XcBaEtJp48aVuTFFzdqcUqlAkmFzMSdG7necqNftld8vMCaoZjebAUYvvk85gK1cXQ4VE7q5ZjZItmcj1FYXaGEykAm61+llz7FKwEi2OUKqNuvYzvnY2dldiQ6E98DBneqBTNsRCK2io/VWJlUdo1WR76BzsGJivsIc+GDIg/n7+SsDnJbfhJSg7o7QX/CpQ5WWQfBZcWAN0LP5gdoiCZ2x86BRntfQuKJUaAZ7gk1iEtpF5pNh9oZHTPcDnstoLHLM5n6xz0uKZhFbEOQ3xZIsHChi8C4SeoUP3LOTs2D4CH+0Cjihlh1D/ISuHR3eOBQklUrHIzXQ+kT9bQCZPKN5bNaVN5QMn6rCj97zQgUQ/Kd/FraM32pdtelqF6MdOj4+2XGgr2DubYnPzAIGsb/Co+Skqz12O6xequXnsEU82MR/HT3STnBwIz+YQrzWDk5UCqnvZJBUukY6UoEXGxcSybG3uMURnibJbeIX14EChqwfmzQ+2ybTWsd593fmqUdNE/TqCE+vfRhk86zBpUcNcxugiFf93+PBXZEe9G9WB+kqou8jk5dMl34HEvPbP6sL9cOtKlzfPPlvdLnYqGMbVzrQErhE+aUZj3HZzo636MC9WrBGquVpc4Jy6hmcJjRqk5Zb5xoO2O3y/XnC8Xq11RvziO0WBzwHiXIBysak99622r86IojnP/nJzxUu+HdAbx+p33snEy4LRoWNisNqQAQlf2P20elXjwcAA5jws8/T1JZD9EKiVuOBPuoENFa4Km81EQtJY/OuAS4p6koePrwzSwN10VCGgtpSPxnEaZMS2oW0f01ERorgFlV6ioeOaGzbl5MwFVA/pDBnxHpX8w0XT+r7Tgd4aLN5IquukipZCX1QHQ8RzurlYTfr8ATkppg9J9HY8Ekv2/5DsZnY7VylNP/SlTFXpcnbMnJYmmNV0EbC65cI/5fDFmOdotVefNOxKoAHXibsax8fxDnGvBq/khIeyspxpgK+DkNoPuuShXu1zxj+PbVuCuOjEtcNhLrAh5v2T61puDPyIXd2deaKNfmoi4ssC/bZNOSB6AyEnDhi3cHlw+rHoZ0wrksHLaPwdwbIujk87dt8qKLaInYBdbNX4p5+KVmeDGcVwoGruLGEMR8KAjIF98xcCYacWA16YZGRhpt0AHWa3szQFR6BqXR+lfQmWj5VRQHyidEl84+du0t36MBOyGQcTeG5zLTiVnYPvWsj+ljk6JPpEWxZptKtzLdSUXilTamaQa01wH4xUF3L//qMR6JnDvW0cOnA4toogtCzerIt9HOZwgJqChAXRu6keuJVgcrqVOeTJXMF9u9dAvSTQGNPpVf/T5ueR0bgEqZCGNTPCZ5mcZY+R4X4mVGTkzCrR4wqe8bKpF13DXOp0XAn5iH9e/uP2spC/I5rdfjx+SnzuJpzceAEUeKtu5qNnfMg1XilXQRcad7eVjECSQ15ih18ghlpjhndq3lDsRJDC0JMExT1QVBJSEHwZ6hTY2TPG/yZtb2fccpDCeG7R4HNwApKFd92gcb7Gu9E2Qfl/BFa9hh8Lqc7KicBjgZSz/4pa3j50mlK2Mm2hPgztXzlCJ4nxIqpH3E3e2kTPgpKwuqva1CwcS0ER5DSWzoGtVTcHPvjYdApiPSRghYlE55iqtn2/HqziPvg+TAXskPYf9Xb5DHvs45I/bYZTD6kmB8lcgmLTLFoAKn3HvBMDoT7fS43ag2s345E4WwF3EIHxPVn+9MqaBcv7RcAw5pX18+6zb2LTVOzsuFTtmVT0rmJzl1fleLmj8Znso+e/+TCyykx6EzQsoYZk8WQoAALDgxdpy7C+6t+bbvU+nX9IpgbU+EqpKI61KIqg3933yEb5n8YvrG7koYiy+78JlHFQIwnaFuoRdBpOkax0EUeCYKgmDK+zq+GVzU+7PT3aIorgq80SrX5wA8J9/1dJJfKZQOC+n9TSYDlCrKFiknoYkWwP4gnUbWJ8cxWDqPH9QDCXVmpynCxTGSBLwbAtK8VjCEYIWRiUvxdyJZ8Ra2xpOelebVysQaDgTxGWZmKSpkobpFSJpO1CD3nHoI3q7XQlpMbcaZDeVedfi4dOaGTk5FrU6CFRl8WHvicQ3VL7MB+r2HiJB5QToBfnNSbgxAHdwo29ypbuFuS2kflEWhANoVelVAclRjaT5a5R+7jJIXRYzqdpx/0XdAOkX7HT9Eo8XPFCHhMKOjLcgQSx4NylfifFRlaqhVBgwrl3w79qv2Bi0X52dKWJNPtllTYjZ5omO7oKyw3aiAIWnVxYTsQHS9DnGoU5JTTzqlwaX2D2H2r5RxmrnwzsjNam7Vh4wZ5k5KAb4A3G9QQTPnqTRILFqyDOgNQD67Hr41/jXYhPay4/q7wG7PZ1irl8sMIWSqTnTYR6xU4+cBDxkZoqp5NvOQGen2wHf344hFjDz2OKg7LWoLZxSvLRGcbSDGHeValDz6jAYiYaBb+yYpl63EvrV2Bpe0o78OWKeX8AuVU/59saIP+LOtIbzclph20jj3eaj90tP3PYC6Yl7XXN7lTvkHhoK11txDVT7uAhWHsNXTQKCHK3eJNtFWhpPTLlNcNVjhsew6Yb308f8vHDpWDffq1+L7igQrJ8IGNupETjZwulz6xQ51yGrKA8Owxs0R+rGhp4+EgkVzoSthtOmurTUX0jdhadjs2+TMV7FF0rmxxNvgc2lGnLt05oj1Je1crG+bJOTz5q+vY4QmSTN/mPRbXyD/hF/UxdPAplMtbPt81H2Zf9lAqLsyucdwPCTKKg25DHth9snzhloPRf9HbwellUvPi52OT8gZ56AT1f+Sdp44YwnTtMwlAKPRc3lrWYUTPsRi5L3hsXE1L2/wvzLFrD3pnIGJ//Qc6RTzhRbZWdrzNzqBKaKTPYAmSEbTSuE+E9wuUf5JASydkmYNEiMmZL0dlDedfFqj2X7f2luFU0xt4KMU4JWQ37whdMT3STRNgik5kin5dwp5lXs2Qr3n+MkpBlcs1C9TWsyNv77v2clu1Jd/8lwYzRqMjkeHv3BfBLEp8JVhhTekff6ZEPq8ao5FmiZRTIe5gPwlOQ79Jm2EbEkkyAQeWRYEJ0Lf5Lred7Xcrif5sUqOaZRajGD+USZmr6Ma0aK2xVG62kZlVosL+odl/qcDFyyaEaxIn7f25lLAfbIlpqAOYGxTq1bx/TZ0yQ03WQF0qHhbumDClvmtm6NESDtMoI9NGAXsl/svoAM6cOqw+ga8UWhkT9GUdpGiMx/kjYOBjXSDnfewQQKpFIGt57X7rycozfWwmHJ6pn70NBezbMeyISGO0yAVU2kEy90ixx9c2B+HRT1fuRStvCrMO8TUFVLqkQfFZvw1JJchfAnZ6VEQiNG4nZegAKf9S6h9VYLST+VjwdQu1H1NKLOrylFSM72miueWjSWE9wfyntS5Qf361NGl1qTpDlYH5rnEVPXDeyj6hWedo9vZVdHFSVt9BI1l4VlXcybx6ABOVApGrzB8YecPreEWWRqW35htI6G8Bp3Ncn6Gq9eP6FAnzGrjsyYiKgTCCdCV5Np8aDEDZUijzPZGgg9Y+Loy8sKQUAAAADoLDdb1WTMUOz6Ncjl9QmR6qKU1ZA5U7HVe1WmL2hnGfmi9AgwRfTJ2LslOSIaB+MqCCTseej/Cw8ILgCrpL3+Z16iIobXTnbhRQIyBC6gwncnAQlDx2x85A39EdghBGfgNAloR6o6GYZ2k5aKzvT2DckxZYrUrR8+Gw2QKorMrUOtoLHJQoxx+EqFuoUtrVJdqjhO6qmNhLCmuVEMfM2u0fcV3eNUkEAzk4dI4mdgaS40zr9Z7DiDpDCd6AAAS6UI+4n01TL0uOhbDK2UYDh23oEwlVD/lqzajFH/0739ihStjmwqDLb0gK2/VHiAZGi/XfEm3d+FxIMw7WztxZK+Ot+fNZ5PpHbsMQtrcTZQrZl0noYPQ1A5CyzsV5kh0i+M3ktqo2VKvznyvDeqUa5yYdrUGvFvprzZnB+EkPpJWRGUsOo+Ei3iIPGQCPTDbPlGo1YQ3YgCzFAAAAAAAAACbFAAAAAACsFAAAAAACuF2Qu8XhPmdzbeEVLIgXKpD0ROZtLl7qk+dSp4Ny+83NtK3q8TNTcn3OT/UJZZhkKbYzD49ATRSAZrSpFUEl4hSwee/b+LjdeVnKQwyIZcobqJfv2qDrUZ7e00HLf+pKIGkw7UMqD6EEw/H6bLSV1gP84K39dnoNToizRpsPAi2Mnoji6ITRHoWe+CDzMgPEmpsYCCuKE9I5UKOrsZaCaKzav2y+sY+TxKGVe+wRz69GkbTmoZfI7oD/ND5DBL8qPSA5oidNs6WVpIDFKCZpU9QXCJFZQyKG7DNhpOASkR6e1mE9MMRQCyqI9HPyDrNv5vMu4fzVulJ1jYAQCBm401VkH3+HH2oDuftxze5ZH4XU3Gf5FKOYIAAAE0cgCViW5rTG6s9ySXriR9HeDkKtDKjeeQDUEyqDZolAmyC1XV4cpOw/mQxriH2PflYcbjNIJRJ2j3ZFkhc533A8Aa8SiWqMOrwin/spqUhaj20dX5TtmVyaoj0bAhqn0Svjs2wwNpy2uASC0PfMkvKxsKB1ZwC7ewkIJUp0qM231OvifOdUnpSZbX+60UZwz4vnqhWbWUpUsvept9Ydf0m20QOpMGm4gEAUpEVaP1SYK5lqQSoVzLV0nGN7WPhRkDx1KL7fg5nJ6gIvdZlzrjqT2qvG7ksnvbMehrsEyhVYvtfgYs4erxtVTgOcul+gSxIOeMQPtix88Dd21Z8zqg5AQL7VkJ6DdA0//1/K710R1J7MfjxoBiONorPU9w7WwiCJ28aNmm/3g2bFWC5ZBHIYvhae1CNhNBdyV3pSwJKhga/t3+5OWij8sh5PnqNGUqMXsySGAL8neYo96byMZclj7bvrLeVETMr88W5QlyEjShOZg9/AQtJ15piGv//VU8R4sp693MGNF83OqW2LnyEKq8M6qu+MF4PU4PAwitwK8K9kJsggyLu64h/S40tlre1HYvEbIAX33OMq4LlrIX5xhDNAxyjaDWJlbzK2yriMTfYRFovUaND8S3Dhgz0Uq152EcrvJlNIvf76Rx7JM+bBwX7fIoYm7AKv8PI1DsOvHtBxWSFIfuUegVRKm+2QJFj+CEyJ5qUwXm4ZgRaAo2OqM17WIuc/t/eaYfDA5PU5Qi0diK3smcJCJSojQc2HpiiBajkoerahNyD8kEUAphP/PJ+OS+05qy43DDkV9uRhBWS92IAtgWc+IYKBZ31qZpwsW4aQzfgjbL6+Sw+P+06oG5MbYHGLDZkBzt+qEF6ZdH3v4mGstAboBop2nxG3fiXolRQ3GbvLh88Rh3dRbGNg+rD/3x1/I141Pbi2LnkfTabu1j6K70ewqufhU8FqCoZN6kuJmNGe4zKv9ojn1S1o6L69QTvbdneWCxAYh59VgZMb/1JgC5NH3g4bTOk5VydYEeUAWipznHFQXN0h8j7UFsGXkukKopYGslCMG3n4jx23omAn0q6y+4nK2aLeFuN8TrLUsFYQUPFkcX5HWJAKMhEFEsDkdR52eRtLc1wp4UGSlkQns1X0CxbeHECOu8ZWEDvZqDJA1UkELX3P1jvZ8KCGnCabp9Wk++P2e/G0542PL+xnPkW0a3SyyS8ZcFevrWQKiT5ajpXekUr/nVrjwzvJ6vNQQnExxYmEaD8G15HizQnrh79U0SflCRfM9BYLrod20fl2qs68Ri3PyyNe03U3SAlOELyNW+c8ePtuVYEIXqtTYhJQUK0HhqJY+iujVXueum1VKnfwpRb52rkIDm7qDodmVWIKBQZOl2FTbeMWZ+kqRPvvDSnD4mmwoPMN9tKmNWbLldFyWpYGRe2jfAyXOsZ0tgm9AWm430Nuo65LMT4zZF3hoj0UNdLB1DXdMjLpLdjR6BjR1XdR6RH1jYSvGB41EcFhN9bJtyfV/xXJ7Zp3A7TndhewZFS1ryh/xi9HP249gBxBkI2SQKMI92OxCH/qbf/hG3R4GVpU/7E1rHu0LqvnMpB/e3YDpVwCpQ1lTfonFMn8FwfNbrKfDCdr9x1L5vLGXUKfXIHMyjTFAGSsGeftcL7ygiDuSKpH18VvM2yZUVXeStCSSGcyuxMtcBkLDQ+MerKqc2e0ebUzsvS7qtvCCgzW0hAmaGV+pE7aDXiitUKRGxUi8b7PmSLfHwRn9YHv+7ezpIZzQNG7qZ1ZDHE3wbMgDDkPRAregLYCbJNQP2qWOlQyfsUsfJN1P6l/MCQ2h6rQ80uotSyaFJw9SGHulp8RV/uvmwh+g3lTzC0AIRkvdruHjA9LDuzLhZIGZsCB5qNOyR99X17kCqgHp+Q102BS+Wex7Pxw0wOhyW0Qeod2ceCG7s80oE9nV6SNQiDs4x+YhVm764zb5CpqxEt7azYWHXptkwwPF0q62W4g1Nl+jHKJJtDCjHn/XDhjld4HpF9s7IEim7o2HU2D1GfrquVxHQuMKw1pdPWSIVldasYC43i/Qw/bliBQxDGgF1ZTG8ip4QhMUQM401GwrI08Nhb5cVjKvkwd+ryP6Fsw+tscL9JC25j+agphqvFqfkygLRMybB1KIu95xrBLD2fuioHstZbFXzrhR+EGfaWEVJY7Y4gyhXC0u6B0JKu4KDh5HVhNzwyFvRwyzUpiOOFQRn36UP+16EGk+jFlfj9PXHGcy/7oml2j9K/tmHJQsp9aEzkZUnNeb7uUwXkZ0EU50g3ApIgI1mD+8ZGfPKXSQfNDXqTxeHt0iCtNhQ5KbzL7/YtAVFp16+EyUvTup/r8E1tVQsDiVrmqOG+WceZPJni1UlXPrTyqF1ZMQUETwn9wTZmxTOYiWpgXM3iCIR2SJ1KADWseYtvLLs+zI8LTExP7Ykw/RfJBkEAabuvXeMuQDjVAJ/qyaHb4wL7vRp1sr923aEIgcFG096RaL78ONDi4oV3T4OjX9bD9dVtoH6aaQ9IXJd7OwMboGB7ad2XTxzLz8aST/6doGUFdgbB/RMOKbfCk2tsWkMWf4p32Aa3lljXWgCKyxnFUhba/GttpxH/vy9gWBfMxXwfWushAhj0Z71iYmGckeUzrLf4kknBtA9D/48fMuy6trKKgQC2AmCOaQ++pvY59DiFIXhnngv8YcSFrzmg/PCXU40XHxY65HJsoUELmsGEuACT189+lH7+tp2yICwB7PzM0u84o7c6JWBv8O8Oay7vKfrur65aQ0fi6pQXs+gwaxaPaeKCaZaIEV6+hR4MDMyTTjVjX0lbQPt2YnV42gCdNttt7oaMaOmvo3jrQd8nUDXIuSllmZJ0us1mQ2SncJ8LA4d+z88OMiC8PUTHKvEckfJph/NI5pq4EphjkLjwYq/tdlZonx1qOJEUWccuSqh7mY4qBkWCoKsjv4chBy9wz1nX1qc2QfftV/RFcYMD5UIStAzdomqJH98HF/aRmq94C3QEwJyotzVMfPaNmo90yt5UVK0hp4tdG3/kvF82gZwlD/v9LfrPxq8iSxdgQkKUVNeBGMRJjDyADXdy/fO0ovZ4Jfxj852TJcYmDqrKyHqEk7bcep9p6Al85zI8hPGtsIpyxZIH0L7LBbZNefWZLIfnTL0EJ+zmYohihFeEmpHI8OG4FDiPpikEWBG+AiZMoiTdIac9qtm8fUz52GcNHHP5RCwZvxExdYLJhK1Y0WZ3fiFi1je0FlPgXsWzQoepIC6XVBe55DX1HIKbY2kBDX+fH5HT+iNDUA+YOnz8vSfUN5y6iBrmhpqZXiyGq2g2ey7Z4XH51xAsP65esJwnzSGhKTkyqKner1lmc5DnLqVSe6sgf4o5WscmxxWTqam0LgjrzQdkAEhm26nOwyu+KkEInhhhmo3xLCZQEjp0T7tqv44sUFD5bphQ9UsdjQe2uxO4/l2kBeHcgWnlUNg8Ohcen/Kql8upWRD99Jc3nGOsKDWtAwUrdPUXZ8jmG5uqfBtkcGY69xac5pujZ+VmhoYLlhzitKWECCSB/tEu4cdV/gKgy9sYGFf9gQzZO+PetGlmCS4F2WAMUSMiW63Eu5Hz1N921poCrXL5dxRz43xLG4Vfe3lcS5bZ827Aqn+8bM8F4rzZfe811HDz59T//tV+9BgQmhrXodobuSloRGrAGKNoeErL7RZsYw4H5QvQkMql7ZOGQWm5xx9kNlaeqjT09nfZE3t8RDunEu+xL8MRPmviDBOZvjQAHT1P3DFp/KFZaqKJxRwF22j9/USUxlV8Uh6V4mTIAHDdfcYhYwNHhNHynw/0SPSlYjhkmZfDrM6luY7IkXgX81NZfgmr2RCeBRFKvSO6yWTzyCVoYewgpwD1NZX4zz5nShcPf7Ik7fyCajJPoAaaMBpjM7dv262yxoaUf5Lwl2YaMFMLcN2jYjbOU3N7yZjsjbDzJjJPERKjKswrpJbqFNUJ/Slh//x4wNyu5j8VDs35Z4ZcyL2UEPNfg198uSQ3ALNzvawOcDhRyPOfqYmlCn00iraI3DrMO1f1ngaJVJ9jVaw8v+hFdcQNSbDltarb9kHef6qDJYJF/W4MGYIk/Ioeq5YLajJ6QWKRy6Kn6hn0C87sneTe9bx4xro2msq4+yTjORMJuyODKW8SSesk12PG+4wdojGU0aErFCg/VmVsA59qaxaLzPlvLn7SbUJgBUkzOYpP7TTRBjpw1GPUF0ZeZFENZB8k0Eaa9TP9K+V923Lj1ackKBu1ACi7OIhQBCLEQ/wwAQi0UFfW1MSTGWS5aJvaVsotnUVccznx5fnZ0uWP6szc4MdKYp6yJkv9VRNFkxTLmmhWFKDspr3p4QoAo9aQA1qoVfPfRtLVWFOy3B8KxlS0LMqIj5wrm035erqCmujyqLThGqWA1WMBlBcHWf7FXUSqVCTJQYTEjrZSyskGVv3949Ij1pMfgD8pb9JtVR7yw2lZ9PZvXawm5s7M+xy3pZvmzui9eP9FxpLxJA4gZyYtsmZFqmzR3U89sjdz6NatrEeUAStZ4F1ZTV9knWc5JeMSFST4gla3jEh/wzl/wvJdeemCedk2Meo9BsKmcefOLJjHOvZrNTAETDF+8ENH750G9C4r+OjEMM87LTXQEbZfXEcjwQLuXeEBqgZ9eICPEw9Mcd06j0qzpiSx0rm9rkO0B1I4eGeaeXfcs2t+0wSMN7FjeMH4ARNTSFdbCtpvoOmSgP7jP/VciVjAmgvIZFN/QLV3m4jaY6ZIYVUc3YmMKCxFD0C66Fs2XLmFW+oa15a+BhCoTLH4E1USckBgI3pGprvkdDMOYiC/bavKeFfMxuXreGftVjZW3vbyERYtZJq0DHV3O68ddNZ79jR/pqFBdkBIkrxyNoYvvORsDk4VpPp1VSrP1boUILXHYmDbm2FZMKRF0tJQ5bhD/SpHMjjOQTC9tRsznqf9Yo4zaECZvSokrlp6q0vLluSjbuxl6aZiyX6JB3PXiLLWj3W41+7wjkOXxvZPJYBW+V560XFf3E71D11f9hZFDe6IMz/aJxig7tZQaSZ5+cyhKSh0oRniR1thtCs/GlIRPUu3vOhnf0W/m/Gxfr6mDYz5ER6XbpchXwijYITjB7FENrlolLNYR6VTvVTmLKiUruClIMFSwb90fbPHCRQT0Imkvt9JsQpIzm26LIPll4qaH0wHi2w+m0CvNflwei/rL7f4tTXX7qd66mbgmZnGWZ/KwcbpnTdGJbZa4sDeWp5vKOID8hHowR0/MMJpSrWWxc+Dw9Dzz/3oSfGTTbhF1IazQsnAlTGAgvE/uWuERnQ6Yctkif3UFUoHdhrtZrJKf87/wTJTFKS+kwz+g8UE2XrqokG64zkIuY6prGHLRY/zlyBjq+eykSVoHxHy04dJ9GPowt2yNDLJqxzc9oJpF2qdyJEEfc2yR4ocNY7dm7hr4FPz3ojmLIZe3x/0sZBS1lWXngU/ZhnJArXKPHhrA6W5Xk7O2c3ONzNmIAFFJYWMKVJt/H11XNQffV9hryGjPg16kIOb/pp7ZDLf7XFtGCUboqi5EfR1XzNsze2YgYlBfPrSBMNbguqHoyDT/Uv1AeeZc/B7aHU7YPpBcRUMTCMM/FfOUAeOp9DgwJe3//ascQwraYRuhjfT2m2P01U4Ecj+LqLv8NBDVR3m/s0OxVxYD0kdNYXXmBs14oSO/xJT3tqyL4XKguapbbHXc+DRQ0PT3ToNpl6cxl8r9bjkZy3MuD13D7RQJ9IJH/G8zjj5p+tUff78AIj3gPN8GVPAWRdNxgS3EvylkQVOkcw5VHB2poft7kFZW6bRRBWRFqv737XPg76aHkbwu2uAvp6MRH+qNZ8AFI+GQ4pqoTDhVVoP9DTih7DuuSshGfL/3oEZRBjMrA1q6MnBa/wi6cg5UDnbBXo07UDai1FreuwKTfew+rOe+aL2Ah2+qFkKNy7Toj4861D4XQIStVZ/ye1k42FwEd7TjbOCrTIKdODGkHqFaE3Ycukcn6mCIdg4S+AUWoJ5o8J5xoLGirplt3L6Tt1wD9BoCuphfHJ1JcAB8N8Vz4K39VSktBnlSEtk41IXOuDZfKNJX3FdvuH5A9v5FsQd4Xqc8fx696wQnGq/qJTI+O0lCsIUh98lSRpm1ZPH3+WP4pDcFoAxko7/ScDpS66K++6Pmqg60mzDuDqG5PymrB/LEmnywx+fXBfZP/bjnhrghqSUKrg+H8MvCNQfFsA7HfH2JzJWmuc/xIQPy3JuweldO2rRsLjLAwQrGuYbvbRbjHe9MmQzAXmZIoUWPe8BBzDtrdathzXogaiig7oBsq8p1KDqT/iV2op8zOEWYstDjS4AJO2LXv0ARmmBPwP/rCZy4g01gXe6esOigxF0WKgsXgSlKxwfznBycz3i7pt6aKKCE9bHqgCgAAfhKLKZ9Cvd/0joYsVOm8gLzL8DbGJ5OesqLTkCsjNj/VvLPdPtxQ8BWbXB2skMyvuoCAA5DrIIZuMu5Ta4opx5QZtOn3QjM8kC6ldb2IqjlfwucAdkVBomAAXrjlsOssL2SxuutukrvuF4RkQZ0EDlAXDVa/6JTZivMKGdjkDt73ViO2O5Qp1SFebihzqYzh/Ljq9phn+lY1LFDBrR8CNT1l3VY+TISdvbuPYeajNCOD7q3KD/QXrC+duL5Rx+UOftOvc5gcaWzvAhDlqnS/rTH31AcxHldewUlfj5B1O1kUz1pzgyfILIgBIJxBrPdcwAr/VrGGChDpbAYq9mTxF815DpwO9tC9LBKn+Dd1OL2fF5cnuvwGKHDcmv09nJHeD4AJ63E4pDPQLBzWkdxuW7ACu8m9pEMhyWXPZU+b8KkKchx076zw7ZLJH8tyaxbQP9LBL7te1fI2IRV0j2tqTkIrUC/XhyTBSwGlxALEBDgWStEbP6sWpbCzK5ILrBCV8BzJYGa+0Z8gOK26LLPVTZSv6jHWQZAglzUk9dEALO2DKd0+j0MzYb1OnFaxSb8AuqCF6EU8TV46I1lsz2JGQr2hqNvQp2JIy/W0HXdYhb/QXIPUW/2MPCmNNka+MUAXDVwun0yB249TTphNBsEe1EE105Z8aFayO9XuRcMgyw3v7IFXfk8eGUSLs0S3YAWRY/9KAQC+lT0tMa+mNAQ/8J9zF733VvRurxoeblGuYuFSEaThYbset7izYv+2eUXSWTbii2GTMeMuItK/uKSyLjHxdsL4jStafv50x1/ifP9BcrDc5Htf4ZtJSSg8mZZytOGQmeFnTlZB5JIvhkyMRsdCDybrWOoouU0hu1uJTmnWzeRBre3X6NIeQchKjKUYV+jOTWDdXgxC9Vh+kBFEQH7foCT2bxWKfXvITQjESq//emTYZrZODs+YFKoKuQcNmx69uzbAbzuP6kL/1HFm+mp7EMuUHSofB/4yssS2ZZWjtR1Jzv9obz32lTi7A8fJCls3tJPBz07ED3bZrvl4CTFK389/71wc41EUhKTjorM0cbMeOerAlhWP69WuNLnz6lREQGK/WAxs+n3yxF6qmk8HEw0KC3m8KgLJ04+uQb/Pgt2ims0ztk/tl/d2sOYQJhiSs3i5gcnEGpraifwLlyWb5gozKR9zqL6z7umCfbjKu1C95GcyiunCbHf61kcwjoAmSy6VWR0NdD+7zKx9RfjglOkkKpDI+Cm7d0jyMWY5NR5jOYZR4DpC3jrOXoRCTWVNIHFWtZ9fijg2mVvNzp0eTah1gEbCivKpycqERI98sWJeMeWmkR8EetAx+/WRWMjkR0g9PnPOhrol5hNOHrIxYaXPbOczDH3RbZq53Aiqry6yzxssjKs6SlKcYtcW/JRYTYyOFrn2WlYbGynmNhdA6SjbuBdNInPgquNuONXTRn5QGaCcq5XpAgjkFEQTjXr4s4AJ0wGq5ahY6Rp+1cMXHOFj/ObFJv+GSvRzx+hQimvmMw27Q/WoN8psAkculBmV9ODuoUqg5yNNc8VTk99+/YQL5asYZiKXKhrLl+DskZ9Z45uiYRa1Jigf6kAlYCUAHA4VgoavrxdCoq+t2A5kYOE0VVuKA86dS8+X1Fd6eyvg1jAORBJ8syEZ5r/s6PMTP2XBcYQUdvQOD48bbIA2dz2OaK9pZ8bwEsz55WL2wqRp4uCJmRcwC8UOmXT0QGGtYuf5b8sH1iwLBha3yjBt/CkJFLUdxyi5SNQsF313Yy3QruRYJE5l8P8OlmAelPrWbpOIbSY1PvvfIKdpVn+WD/OOCZqDsUcwavsDBrlLhQ8GhrG3y30yQ5nW/3kMi/MDRwBgvZOjdmqZXcOGZ+iNvQwbQFWOHj8P4sq9CFPebyds7G/huVjcMx/KDmKNv9ZxxxTnn2Bd8t8/2Z/5F+RgoIT6vBktaWmIO1oFjVp6XVFcc7iVoqDzS7ZQ5DFTOPMJlzkVvnJV6lS0SLbZqIg6N/Q/7HPQYyDdomyv4RFFp8QOrqAH5CZBvq/TejTQ89QL+AWHVMy73OUC9VuPSl6/RZTv1yfgXVriHjvSTjFPPc2cPoQXthuF34XmRMUdnAYv2SPsb3huN62Lnhy+X3tEiR8J0b0HN4xNT+SA6wh23ZX9vtGGn48K0sEe54EgENQufUCzD5ouqQeihWBbjzFziNb9Koo+ILQnqcDShcLRc8HQX4G2nAbva9fkNWYA6t2IjRUf2iH7ANzOVtDSyItVIf1i+tMw8sZIRgTVz2B6tNaJTeiCg87lj7XzyOtXvZE8ox2vQ5pXnWbWrXDfhWJ6SHF9Ke3nJCjZONVIhAtGqGW1F8o4lL/LzZVJCdX9gA19dcqt7GP0V9xg2qIBWmrtx4ER4KwOy8ytC/aSqtg1niT74kTsCEOeeEVEz7ECtGva68pMVL4dJGKZRGWDy/VWDbfkBGRTbMViqzd1wMlgvR/L8hte9DiYXwnm3h2fK18yZVtTMqBwyHH69wz1i//gpnq8KYXYyFCipetX2nxZKjm9kBBMRS0rMIol09H3F3BowPVHGwTTDujD5WjyPEU0glMGpEtp+5bAwWEepJBa7/xkKNR/ymL84xVlBsVN7uzny6z/cZBlcybUAIGf5y1/NJ48fyANRHT59PGEJML3fW+/Jx0YdJxtXUmQQDTn6whmM65+a+yifP5AS4FPgoOmYnW9Vl8Za8Op8sESiDv/LvfJNqxA3ff/b7B1AHwt0wbj64gjTv2Uz3aWpCZK5ipa6j6Kmh09EG4YqWDMfbuU97lMOi+uTewOliMSuc2daEVMyRVpPu+LLQfMurCWuXnbdd9KAWC/P4VuEVBuI5lUweyWegqkVvTp29ynnrRTvTdzIYN0cM8HZF2LX0uwqy9mc2nZ1M3s14YaBAFUpz67uVCOddQUQU1bH3pvSQq1oFW0bHxZVVaqorhNqoX31UCXj6yCAHqCvsG9h0IBSVOL+gwC/1DIjdJmALc+4WdX5bljQMzNAyFQEOXvb5z+xqur2txR+1Wgk/lJGeeC51EE0s3u/HzblnyGy4tR4g5NAJuEvUsaCZz+8pND4aZIfK1sz3r+eXI+oqI7pQ8d4B8cquvIPGhcHz3IfVR0mzVqCccihZUHF/JGlwPa66UtbYS3TvmcLNPPGFPWk2tu0y3V1Voql7VKB7CNQ0ezACre3G+ADassQyWmpg4WNlpdlYLH4RBjnS+Nriiz8Gg+QqoLS9aDdevQuqqIjTQo60PpI9lvNCY5Do+cq/otyQwLDcdI4/ciE9njKsLdtqzJFa9uOAFwcP2FKlJQSoHACl3u3rTKhQhA8wJkjm4v9X38Y+0rVDLyXNLpQokqc6wcJ/gV3AO6W81XgMTwHtRULe5AjFlQ2F8VdtyN3bIK8Y2l4klXK8MW+yrITaI9k7u3NLMBxVc+UHYO5JGnIwnfz76NXdxy/N+WqrceTe1yLaaXZsWhP5P9HphbRN/3mut/ioCjm1stVASPLzLUjVeW2ID3qvb7CZPDz4y/9gGUTErEV5Y7Gei4nWNhlCnOer2RMSP1yXmk40Qkcqaf4YeT406YhmNjogwTNrR8kpdGc7UrxSjVaXJJ9j4zLx1yt8xi6sdXJnVd/wTYtHQzmHnK3c48vwA4PEM9qKkd1mZ54CDUFzVnImOCLWklafi7MJXl7wKzrQkYSceJQU1PGkquIE/yH4HmQORvf9Dx/xxbX5IkrFPabPlYr3BlIehxiEXNNlbLa9Sy7H8jc0YJTjBJ4p88rUBXYHEy5b15siVpsJonDi2T4GXdE5+BwgS2qRjVg7riUnbZ1NtHieDskRPbmJOBILpKTOIfjQjHyN04cBncMwWBP0gd4vZXZua45/njnQFXNBvbvgVL4zPkmFCE1xoRvyKE1wIBOzUufRwUlwGmJu09afywkkiel5SqIQINi0m6N6Xon+hg5CPpHkOlxC2tbe6ZyqsQNyHXLS7Z9LR0bs1T+rdyUvVpUswQL8sVpV43NXw9wasZubRzZEsK1xMjCuVHyV67pvXZYOn8nBEHzOG8aHqXg3FIfcIJZLdDTaU8Fu8xQtEK+RxxVLJ5/vbZu6SYj72h0er70A0to6mRq0ktKZy1RSD0y2Zh7lIJT8LjxwBiAH0KpnN+VHbDLBjAnpqga8LJBs0tIpJJQYeR7+6P+62nMeRlwu2pauP4+Zqf/oO7stxye88jbl4B4rsXQOQ8YINSppuOGrrLyuxy4mguvU1VMsIIH5CFFA+pT5Qqoi/JAxvuzThFeeoeEmi0vY+9ydbWLoobvwgQE/lZHquPj3MoXJyu9hmlf4hCjT7c16y+siX7/5R6EGkg0vq2yx+MTdxsRolZvfrj0JtZsqynVGY3a1tJYl7VInrxD7ecH9+/OMdYfVhe/wQYmO5LYB4MZ538cAACvFAAAAAAAeEJ1Nha/mvmelR9MctVvw5iTxL9jtm7dhLH0MQJjY9mEYGRjIrFM2JjkWCMXP/CURXiEsEp6C4lwq0lWl8cUgoze0NZ9mqKL/6hZk/cqwGHLl23ptdXoMV6hj4EyFwYhtj2iw5vHtFiSOPaLDm8e0WJI49osObx7TwZMHGNwVM150J+HOWchkGDZuRKahxTkd9MoXHZpr9aUQo0tCzs+OZ4nJVAg2+9UUCBcB8AAABu/HgAAAA3XHX/ndzEV/22/9dihpLz+exQAAAAAAABJvSQoAAAAAAhCzFA4IxA6B09p7EwKmTgXBvG1zxItL2dHs5IAAAzf20gjnNCRAIxXQkw5ajoShdFP8ivigxyBBcUttS6RY6ttnNd1+9AdU8a2I5UPhDwky3efT999SIHhmzBkk5FrR4uydQhK2b3L/Vb43ZE6MbZlLN7S/pbSeNqj+ltJ42qP5uiUwSI6wx5k2glh5j5WBCiyFOACNMexwWFRLXXQ+LJO+FHTW5U/yISC8pciaCf8BLfwBuoJRGVy4DBWZEJzZ5if+O1+haFHq/bkxp9DgiW3qMcD065qiLc8FuCmc8FuA6JuxQWZ0z5gbYJLb01+A0T+PmoupxYewyEzADn9BeF17mydUlNxtOodEQH8BAF0M5CTYv0m26piwTASa+5JCRbuXEFhUoPIAYpzLFdb5vpnUYwAirJVzj1HiJhMIcVSshF2/T7wvS9T+Gjjvu7EuUKNboDWhsi2fISeX7SRfqVvKjLbJWvuAD3Hlj7CA/hhuWKvFAABcsp5Vm7alcxqX/AeWtSdAORrT9W/6gABzs/0TpnUN8MTkEGvoD7oHDdvDMoM5rhKgDwV4+UCU6cn7DoMhDEJQWs/duPxSKsAXyN3vi7qCgWP/n2QzIHDy0vETUy7lLudN01yG4w7xVlFSGJg1aLHpm1hQDP2uJAjvClam8Yke2T3Lyhw+qspVPw0eMpRhQDHb8YvmV+U1A1Yp9X3l4wPOUFO0YJ1wm5oH1dEx5L/F5pvON/qlmKG/Qs4671RThS8SPX03wofqydT7JICSy7HfR4L74vvMlf5heF4KIMvWLog4vx6eleV/GFe7RH1IJTR2yAi83pR92JnOZm8qkQg4swbQBMfu0FerIkzJtDBxZpJamw99q1YsznoqPIKrcCNxDSHaNcy23uWwNaDN4g3pLaE3DBoiYVzej0JCUaeDKssazHCw2swwmp0Nks1fjX7n8y89SvOJ9chZC7qyw9iYNbD++xciJuXLDy2AFk55GS1WlsjF4teFfazWRdVIaUJgMu/sks8tGEUE2iqcZYmhLISMeSkxjPAUOfN5WyTMttaX7qCTFvQCblYerLgJVgjyTr9J1wnlY54jSRRYuhvLyrRJOIBEKXz0NRhHRrO20lq3FxfcthcTmJfbOWRROHu7Jb/hicBwAflMUUOmo6+mgax1ubRIeiPh23kyZ2ZsQDYWpe0SZv6rlpyU7bheK7LikQnhVrsTANp1J7NzTOhJjs8nw1aF5m1ao0xuIjwQbzPFpGwj5gN2DmmCzH7/+KPet0CfGkkS48o4kbCYKQa2CagjTVBtALvzNnLBmxH3z/4UMYLgtaXvxE+8uct4CrOw2vnT00UDRovCwnGjK1xbXDc4DyAX2UZ1PtnB5KZxygxjf3bNajR6iArtjUcIRdSznm7T8TsGmHdJU3N5jgMVH6vwaDqV4OXoxmZUyBl8yPPTY4HMuBTdNAlah738GoQs3w79OucmLNOkZVdrhMX7Rqcgetl2JglmtCf1O9SMIinISw0s/OwT7506NR9v/mtTOLJHH6Q7wwkjh60bdGyevanhaY2u0uWC7iMqhvPN24n9fQmrNFuAHlhWIVfxZKI7Zi28ezV1dDWdNMuYXe7fglfCI3G0TIBy46abltkoKjrEuVFmA8AKrke8n9Csa6/pmaK/vnyPLqlauq6vYcHaGNT5LDV0UrTedcThVe9+/SyoCC/uT3o5QBWpKj5NTZiTjZ4cWGzMRfetyEiAzqLRudwH+2Y46bKafScYlALGfJ83qxgaomskc89CBawzEH8+n6EVZFxEPe0w7DlApL7zYyFi4S2BC+o18asGMqNXk9Rxqot7w0g4tXhGW5onKrUSb2jZqaQ7nATswReec8UHq/xRYjjZ5qQiKNSP6rd5pYz0SB+XV+MuqWPQ/77V97i76FpEMcpNrhXTJBBI4sg4/Y42GS//QIr71Ddep+jCpX4fR+UcGhO+rNOscKDXIu5qPR8q1UJnUBZST+9cXHHfitubbKWBmi/HLNNfzZ2gvwpVqsLuYBw5fdaiBzAbYaB8h+ZM6mtYYjt8DjFYdsyLASb4sSwzhBUb3/3riCmMPv+RkjGZUUJ2SyLheuKza1+g6jggDcrkCtiwSWTthKBXca8l/jP6GBiwIhueIHwEeEjoJsyTT0Am9+yJd5/B2G0qK5Q4UJI8z7V+DiCH14LMIoLNjMr55jBIuhSux0bULRn8KMwsbn8XlUbRirObGZ2juBIhtUGOFMULq8H9iF2/3w9+xHu+oLF1sZnUL19AhitYMDqaEwuWL6PaVWmS30rwOvD1mJw4WARxXHMlDd0sRvmIB/a0qsuBWkvI329NM/CCIC3kdEQ1Zx59ZgVq241cojSdAA5VWSMC55txb7ni1NZX3XZfhqedaT03reiGRmll4rG5Sw13tdgzAgpVXsH1mCHxec8jZ7Aoc1A9ojf31uZPt1CM8l4+zn9N9vZS22tbFhQ9skAQ62B58iNS9Yi6Ylna7nwa6jX6S0eIuq5VUvQ/hAJ4ZrjdtuNskOiJFpiNtoh40OczMfsx8yhqfFHIMjfumY+fAznoLzWWhXRAJ+2zchDlIyOLetGHsEzL+tYSNlgpkubttDlcYGBDLs6X+WIqH8rRa2l5vzWFGvPNHI/rbFtcpTjgXTz0nug9KO0IYTE9fMQeLr/Lm4ui77KO+EMMVyMVIh8I0rjp0q6avXQWif648V5ZhFceYQ8XwWVS1yAXbcxd18hUmoy557PBhe+LNLWbezfjzzs4YtTFEIreeXrBG50ylB/YKrDNKLCNZKw3BNK2MbDn+GOg8dHzpS2ZEtkVeznRIAKd5arpCSn0OYBoPrhB5CcHJLGXJndsSGs27eiDkWAs5TyXyouF5I/pLn4oU82KTeFwTDc+61cW2KRIfx4ZeqJweuVxwt13fkAAnBNS9LkcgArHLnZ7EwPhyDzBZZIxyoa5zJuXF2SbNaiI8abo2MVtsg1vKrPS4tyD/iiAWxpvTYxYiowx0yBIV5ap3RaZ1cZs0ayS8siylIBKbIVX7gGBbwDlnpoBjmBoIxbNQqOaOJDVegXLQZx2/8BEE7gj+bGjH6x/2pd9GbYQ6meqGvFOVgOz7y0eeBpMgTsXPodhEdCz0vBF+MNyGOsEfGYme0g/GJG7kbbU/IhVh8c6eOdfhsNOm56CTiKWwSOiXGlug6CDnRPcxmaAMUtpbIukbphchI/n4JCAiTFFgSZZZJy7kTmh6l7IZk2KobQe4i9K+KQh2fQVOUZeCmiiFMDHWhNobWPmHSPIqXmh4sfk73sjGTI74KcxyZtGMqaqEJ4/whyPJrMQKXe6mEDLBNzId7NCZ4H5L6Uy2P5k3tHr55AWs01tLe3jYVsGsroL7hNh2JCSOqTJU9gQhx1zuUzkJvckEWTfBEN9jJpFwKvLCUjhXaUm1F/8zokzhUiIRqfvzf/iVHwK+uxWxaciF8XxxBwjQf5LPVhXwj7vYm/YIE2RwyLRs8+Gqo5oUjB6yJPUt2ObCmzAfVhtbynM3e1yI9WUVd6TGAD9RcuvnAYgbZD0kmMzLaXFIzE+KQ5qJ4DjnhDNp+yS3hnCjs4QQlbxZWsE9jaM1oAfW4YKM+H6IUdTGVstVr/yZBQ5IbcbDdpyB9wdnTihXAqttpQ1ZMxd8KfowS3wzCzRGAj0w+F08KKo4oZZmBtlEHuNvqLg9TnLbXC0MauyWeHy6cMoNMkMjf9/zw1SMLdoNdhhpHKXzjsh7V4MHKg069AuRjiH5BnZCjAqVg1bnOdjQkiCeEw0KTMHbxyduQ8AxY9FhvhlBvDKPfk4HmWdyJ4n8ENQ5ksy1qGX6Zbj865AXAOsXnNQb6NOrF6lTBb/rgsNG1wQHVATd3cpl1TvVQl6xwDbE4x1Va09/Ipk6XyabiwWcp9c/sZDIU+9QveAhk/SiwcgnlRACprMEmN+msozZjgqRCgM9GbK61SJMocVuqZ87mcBj2y+RrPpeCyxeYk3z/9ScQUHfoCHjicg+XQRdnKenCFVTBXhtBGl5r8APvR3prR9R4/2acmGLSd/I4OvJGQXpmxqYFVO4OUevvq8TQbMTtvRmmZuAmUg89W5QzBOS5/i9+F2L1zZAs4ZXbbZhW03U//J9SwQnnCWSlS0vq0Clj25G9uy7RIKUInZGhMZmqx7S573CTtF+tuvGZbT2dycVWKA5v1/xbjmbbUm64Hyh+nNRM5aM/tE0WOW1DnJlI7wvM2AAucCMatd3nm415//mC72heDKniOjeiomV1e8aDmx+IyI4U1B4//SMSOwYxkgslKsnSClu6o9q2NplKcdfuvnlGQIEYznlx2m50DfsLGoEcbi6INBdY/RJWHqkpvlMavhRvIdQeLe+UoUmgfCkYzB1y+ufMiL6/tSyiHI0/8VTSZjiIqUwYnwstqYz2HjMjHEzPlS6YJyo6fZc629RcuMEY6KC+usn+1xvQjksOQ4VCctZxG7OjjDbojVx+yHd8pp+vZiMUEzTHF3U00+4TOOkdMISFe1BwEs/MGyspmwZ2rz/uVhj3MP16EnxbNCQnpIIsx5JNP3Egs9TJ0Me2oFSDt9rkR3h0iBN7TAqyUXQVA3eboCT+55qvDixlMgIvAPwmX0BoSMRpq5UdGTT5TCyEbIU8re7e78ycdgFDcRwnxRhOpdq/QgvfUVkkcxGDjMyqrix+0f0t4h3H1PaUzTWRYHiFsenbaUAMEC0YQWNw17YqJWHR8uRks99A2ZdE4m1rzDQ37hCSMu1zjqvoClglKAm2oU3efELCoPJ+rZhxtjjKW2SXM7rLqIKNnby6HlgI+CMhObW9AL3zjaJI6PD3tOEBlmJ2A66920Rjj+4XPt58FTz+c4o9WT+ro3JFxjjsOsXdA1+ZZ9N4M5C02cSoMzA840CnzmrRm1CWCS7zWFeF4KoK6Qez2uGsgBxNXgFFlbJCDp9rHWIkdAUI26UYiW2q5GVOyl088e78mRtY1Ui95/RCE821viBd02WfOzgiKHTC6juUF1k5ASU+0XczAqSjSgQPUTUWO/scldrq65DLsoX/5B1vjggFssNIRFYvggt9XMb8R45TIcaky6k09sRvsONdUb13BcaNuFzM9RD14VKGzL6HfHeF/NNyuyhMrlsqZGq6DcF5aNPXZ7skIvojfb1GozyNcaxN/+UqBB44WpVP3gw1O15uJbe/JebRdkwGf5YcBJnpeiwggoldNLiYwl39TtDmND4sjvR0maYngo/dxkIVnVT1d2rtD4/Ex2/J7H1m/7vc0DrC5XZ9V9wESetjZ4ClnZXx9H63SUDVxn6PogSK++XSM40P6jOWeRHesYzqNbm6jhfIhQSwuxDyiJIMWXmF4YbStg1ttq4VZhgcyIp742exSaSfRVt+xKMwZYSbQp6IiM2lKwpgWDung5BGJReENCs40qk0zuLoCVIBitYSRjY0OOAwAsrSgQdr6xCbgI/QA9ckT6B3ezDWXbM3fZJTLPIDSsPM7nHj7Je+ngQ1ykpb+ZikxPjOYDZ2omWw9yABX185DqJfUlY9vBS0htZQzlJzl2z2+8CfFwHS2rUvqf7AH0wP8hltLIhnqWR7OgcXbQ5Qa7ODD21t7uTSfQkBLSgktcEDm52d/nJNxNpBKGdDB8n7iB8HXocBBmkcFVoSWdpuDpSKL7T3mxQYwYwE1UHfExqadWeYycp6p/Rut3y7hIkgtQRuOcOJ4NNm0W8LBI25Lthqlz2IjHLHxGQ5qIDIU/2y8mQE/wbzEs/rhdylIxf0Sw97nn79NqVE7XpZHCINJ/0PCCM3aMww+s/axg6tCMdPbElZNeTYGzwGu5AAHjFmTbpQYXjzoio1wMG88UflPTQV4WYM0ObD8aX8fw3ZyghiTwoQNhCWUWFOSZycdrn8kIO/jLKLiXCHPiPyD01htTPyJgzLp6uFt1girtPn6EFVx21rpXUBoJoRBa5LyvBrGz+KLKZiw0Xdvege11JYWCKjChMKUNZxTRnPfOEHQzvgme3OG3TYjEJUKyr8dJMIpVKjEKIiWqWFQYwr/Dsj8X9hQ2KeOhuMQSgw7oQAuIgct51C2HZJzmBoAAZ663HPzreYh9+xbsTcj51f9JBuPpNOtarmvDyIfnecgLKB1UMLpEHA6aH+XF/MG/zsZE0HJMmVXgnujvmm7QK/EiEIsgw598Rxvde651iclfDggQjLhRo73o5cYO7H+5yMg1AOYfFEWsdBipuXpk8DTZ+Cz3rNVlow7SCgAofEmZDZcsg8z7rI5tcfH+v4zgz7VfxIpg7BEdCxw95mwxVKY6jMKmMOUaC5sicGWbR96Ndc8R75NjVk1f19F64aoNtSvcbYEdQ3Y6vRm4wCRh9X9KipQQdEhKRuosrrWkIu+P4q94aYKsLRCkerx9OtVqelIjClXfcpUU6sBlYBfJt+V961/wV6Bj5pda6O+veHixknvjg0yJNQ/97633D3ZojEzvpzTiA7R1ZgYrxQTW/JwPIFe4ahWYguQcpOmUdnqI+PuCNHtBtqgXY/rUclVcbpm4yOIxj3MC+a4aPrijaCsP9Bd2N8d5h6GgaCNN1e333q1AzCNoR985hDgPFe9d+L5qwu2805xrgSycbuYT+FJn+u9mwjk/lrsf4GAJWIu7dUH129dFfwnULEW8G2fg2FRlp1CZol5PsBk8al6C8vYnIaBlcsBQupubaTBqSjgkitFtQMdwMkJ3YzK/qVPjo2TlevLwuKJR2pffP6agd+Z9TSzz8eyb5ml1cQ9eZz9ld/8LPp340y/vAk5FZfXz+VRN4hyW4EBNpwCb3a+BYqra3f0G2KozWDHOdbuOIch65YNO+c+E5ZczcdL9pzlyCLtDC2VW7RFqShkAFo3j81dzmMMVLzcbZpmIJXzY309T+JbHkR3twFjDxAuwNKbwKixLs00xxajEQ7dufKQgaY2wYYTMmijxkAIlnstLm+nfmyNa318SmcBl0nPoM+i2mOz0pMSBQoO58Ufwq2DDIL8Jcto0Dk66LtryckTTwQ2GCosr3nif9h89sbPpXo3BOOKpgFGwWr7D4tY6JLX6aRQThm8DWAPheP7aI9CwGolsi4ClpGnZ2GeZgRBNH5sKyCwi+H8RDsG34UW//t3zUDyTND5UU20kV8SQDWCBUMSviwaTDv191D+Y52hXvEkt1TqPS6PJhlV4IrqSkedllvliguj2i970a6kvxSkUqUyFXomqtDuv7503oHuHf1PAwZlghnMVm03XoqxBAPLftOW720vW4V66DBVI/ZW+fyH5w1Xdxwx9nOfpZ4gBuGDDCyFKJHjR2MBLio4JKL1DAdlR2bhqYyZsZcj8ziAq8AFzhafCVxQhc4NCo6Xe7Jl+RvKBzp6uTvzg/fWVOndh2uP+BXT4aKqTGARLoCQJ3wF86zmjFAROXj/jyTzqPKN926xyLYlcituQ76bquqdPNWNzMhq6Lu3f60ih4EX8jtSv6dsJeuTYdRDkogxdsK+XbVPCYKY0SZoZknRWbhvnltqIs0lP6GQBoINyF0KPZbNvXrqxNsuJG4nMxd62Zl9aXHL5VI1100damd8DxWaVmgeKheyrG8YmBAz+ZTRcv0t92tfDoIIdAAAX6t5hc4Y4iWfcuYhYxT+y3Ho9iv1sN+YtclooPYdqzmBNJ4GuY3IJATzHYvy/8R05o4Ma6+ihllyCILZaAierDqyxkcWeeHrfu5a5gklwTX2uFV1JOSDH/Vqr3rijsSOWapPzeRlTZaRBKR8cBQ8omuYHfSrDHUer0ylfYQDXXoliBRNzqt6Fdo6lREW1y83oA06pAuCbu/9mQL79kD32RLUJYRgy4Vb4h6QXIx5CRdXmeVIbT1p3XmCtKAAfAoAAAAABCtgDSL6ohSgm45y8Kuxj9Ix8puAnnjIQjRNmEAADU0UVc9FFXPQEaCOPSEAxk74v6aqtr20jYRnVRokhV2CQxnqIHfHeWiAddbEGANCa9gAZRmNQTknFHI+nSq2boceCF5U/yIrPikdrdNBbgpjCuCmgtwIXBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnPBbgpnOYVwU0FegucwrgpoK9Bc5hXAVAU51RDFN8sjP+1SCGus1bUmY1SVDzDDOxA+XOjvm6zzFR/kuS5mCdR+ZRc8bCgNBf00d6zn/r+oXHMwhKDDvuVBaVh75dH3NOv8EalqevNDnDe0QLjCOgWlVZw9g9qHzTuxkgacKsfu+kHs4AT3cN2XCMtjNYC0fJTzhfLlVGz1Bbsc+3P4uhXf6bamgfn7ofLA9XCJbnwKunXw7wxKy6cxR76Np+13TaP/vZkL6pUi+5ZQ1TZz4FqXNtV9lJtoBUSH5MwI77zkELkY4Krx+Tzt67jG2SUzA+agHLhR0nHedH9In+GpHiAdzhPSDo1Zb+gX6ZYmU4EdhGHFbzJTdtQr5LrVV28cslAsg68rhEkHGFPWQfZHI9rrrZJSFkN6aSgEctDVv02FzbTUNezj48C2iSkhG0b/6p5lRJghY7pp7ACu6+0jAhlUsBpByAzBLeM5uqlgwwfvCnV6eRStQ3hMhnL7olcXzama/DL7vmDpczoNUFUilPPgA8x/Q5LCv6CJVZghA3T/mXlm4pDKxEYgdzdhT85yrPU1ZWwemR/VlW7BXntDNJw2Yxk/yZl0wuCug7CxvAciHWKO1rH8bTQe3p7vJ1X0eBI8BlUvNDX/Y9O/MUYHR8tvHFsRKde/vqwZGR/HqB2ViZgT3107UDm/NxkwzsB4oaMWGvWZoWGDJFBgBKWnmz3+ZeR9HsmmoKDTVkHjdGbIgX9xhMmA607mGKApdQlh5K4w80gZ2IqFU5P3YDUS8pmqhYlRM8WbVSPpkGi0Sb+pZ4MkP7rBIukAPHjQq6z3HZE0C6Xhg+H4MN6CeDjnhu1m3LhcrH2Y6yKkl624uhERsV3RLD2COFPPIW/ce7shmtUxAWw600B8IE+BP7uz2ASUqwgcENxVXJ59alfWuBqq/3Qo/l38aeU0+NyZ49JPsmCQ9DmYCd+wxy8AKuVZ1RcM1xWaOMLkVExWESQMqu0WbLCyHDijlGd2hWARwWzMBWtKZZTJEkACfhTrwzJ28EAWzO89zXPtkVCl2DCMACjzjxph0THi6u/XqdIyJz2H2QSsdCmri8datRg2RlPo68wkH9tXeBHgaum7NG8zQcRSeK17Tjw7FP1IiOcY3haw9eWW4OTYlZoHiaZ5VFZfNE/TA4iceLWnJl/8FjvLXIkxEzGk2+XRwqXfMoGP9Xc+MI0T2lbIltEMRboKZE1B/zDWSxWO7+QSnjn77xBU4n3N2wHFqQYAJdrQPnkeUCUxqYDqTCmXE+AYqVigxNV8LskfDCip9+AQ70P1Oh7Th53aGT0fggGaqt1FPl/oJuju34o1PU2Z2eWCdZio/8fJMsH02Jxx+p/8G6mMHvY3poZon/mRsURSJdxJWklbT5YI9IF3Wt+D7HmxLYQikoD+b/CmcktS72UMTektgcQQQRSbuCkTawTm2zQ7mopnuZ8tk82Si15PCUeCChBTR4ZPYYezHWTefQYvwv9CtTKpRD1n2qB5hdXevLcDijjx5ID6sGCY4FnZGuT6/HAH9BGxOoAEu7eVAA6jA/+WrDbpgH7OB/+bcNBak5J1u0NZiV/r3IFSzmCcqh5Mx5SPvuY1XbPS07f+BIlBz2X9z+56FDWxU9vqEzBlwHYdTelqfQZNXK3+z+TNKLLkhaKG7c4QEvK9bjvClKjJch2oGm7zOUXYS9naOVjR/ubY84LV8oAZc8YJnTlCvHg5pbyX7X0DELFYLEovAkyKA5F4NxHcn+hq/D5NrvpcjYeo2tOCuZ5FnqPNot3i1LQs4FLmzJKj5jNz2/gmv+c9R4lSV4tGmKUQ1RRsdSXmse4Pwf0E5NEc+YQYUTH6q236QMmH+LcXTmxvXpvhPG/CEUD8iYq4xn7V4FjDzaoikI+hdrzLbwdnXhD6Os4tDBKdb4utbTVvp7R4Zh8EJ+UCZO6ud6xk8RU/E6Z/JtybDi60TnFTc96AR0jhqp0jHO4HWhk6pIkqw/Dc1rHj9Pmjg5Spi2SNoNyJH7Vpm/AYAv/E3x1E3SHENnYUrM9ffgwLEYV+NHXecf3++oCwr6WLPB0q3po+lfpxPsNjGqVoc+ImwJvaeGekE5zGjakts+3ax1E3162SDTVdcLCpEfDcLO9igeV3hKWcfqyNTzLvqv2zWKPfO+huQb5A/My6TETK5fkCdpY9BP0m/lDSkYZjRGT+KvZKk+TQdyqKLtpBhxXsWufrRziibPC1Hb8VbplxEpwOhXkv/045XFRk46v4SBuiv5vUP32r4G9cvEG53RusIwxkYE+sHqX4QkAGGZKvrI/zfHe/AgUTxLe9DN6Fb23h/OlaZkunaUSiXEt2atDrB8WI7xff+VAlgx/gIUIjPjNorWo8tGMLF4vSqMOHdx06eOLowt/mBdwnOVlIORHI5H8SXcYN64TZ4eUDlmSao0I1+NCQx/81FxCBee7TebL3yx9qBCgpROJv0aLIxFCiLYTkpujUJxk8Bei+5mQeYtxxWrsVV131opJlkocWbIJGeU/ol7Z5VEUXyiVXqqDU30nQ4BgUREIa0oLbEQYnRjStXBZds3jbM9sjmaV9/wjdPs98fyZU8LBueACmyoNgs0EgqKl2tKHGmAgLe5/+qO1BWJXwgWdt/4ZBWZbJufIQu+idDRQqdF0tUBXlZOi4CJZmBrMUm764lgK/DxDcUnsxFErxUVRjra/f0KMSJ7kxNFtw6W/4aGblAACkG0wTMyBeOXiEnYDSvKV4XWeR8eUmX9U+LSOJnWqQKAAw48SrsRi9Npdxhhr9ws/qBBl8i5jBjyqBkrBh8Twvi8voWXLwc6lHczrMWBaCJ3vog97gYRIug9XH+8w975oXFgapREjuOKAJ9uQSkRqfRSM4BiLB9kFK2oDzRmrqoeaZeCMoflUd2sr2K9L4whUszzRwRIl0e8zxZtVI+mQaQzfcFjnWYOsPPszSCUcbJ/32i/UN6IdDlcGkFJNX299hdHHTCxBYHYpXCtaUBrZPRY6/AI0yc+KsJ0uX/raGMKtTel9Lh4UgZOkm5ep25kdyruj53lmpdrvLuVWts+S3qYFsrEtmHqW2VfqyhXbkEkF//E6CutuSIYLTgLCvWbYoAQo3qZtx82cwR+dpR5ddRRb79cNBdPEvx/GgQscZWmPlZ5VCe8KHo5oIWizd7g9AIaa/FwPF4TNnY+o4MeghuHFzxJd1I4Nlz3GRYSDJB3hQMiTNY27WBlZPq/ZGPY95nsZHZ3UfMyHKD678ZgabJhb5kfqkF0QyatLebvD9uvNv+7AIv9o9+Pm411C//tbIaUIk8pmoFo7jkmDSIFthsriVCnrkBJrr6LHAEdq03UWPVsN/VfAcC+ktgLWWJZ4koyHLmvOz9fCDzlL8fh9SlL4UaRcrt7vrgyln71k8Dz7x2oYfiEFKHIagIYvD7brStQG8oeE4k8Y/wl2oS7BcaApq5J6CxZBN/qu7PKhKiJMaXubU88ofaFcXQqk5KYkv+RpCag+booWyfz6hJieBIpN70lJ+9ZnXBeRhGjEHA5rX/cdVuNA4HPDdq6dx45sNYptgTFq6YTM422/+ThexjEAX2U8986xREeR23r1dhyn7fEq8zSXJ5yez9h/RHA3qAQAAAAAAOMAEhTkfbKej+cJ9b4S5Ek42RhB8tFs2qcypzY8T0AOHFFBHT7dZJNQcQYRBQ15Ja3Qb+lcQTbYiK4E7UnNARnCfGyG6QqotkuEHTxo2WlBTgO0cMGBHaHT7Lq85tANwe+tGnmLMMWPgIFg6zQjLnss8Bj/4nAi87qoPVjU2cQnStX3Uk2/qTXdi0mcj1B9FJqp2QrBPzRIhWa9DaAclnTkjMV28vgRPhiZrFzs0/mRg6JPHIzWASqScuQhjr2RFPVRyYEk3DcIiWI2eVvye72ENOmMhpOOTvvjR6kJKkXVZobIqkwhBUm+XPRSLYDF9YdDm7z+tT0EgTAXrEI44/h8ZD8YpcR1tZzPCs2rcDz0Yr8XL5mkPWli9xUOFb7aTwYloSDBNTAn4jA8WyMfa20CN3XrFR1FOMSHGJQ1ltX07GyPXNpjAiREZYjWJ7IBVr2jc1xgxGMuFoj7qoMLl6M1LH6WZmHkeKeD9459SVM3WcGsx/xIKoRg5AhDM2fxKmHtfEelbeteXd+2qM1TCFjfqTr4sAdkJjyEVLX2kEdSpgDgXAbIULBqwqjQ4/gdYcfiB34+ZTu4gkdJKPTJVnFt698zu1UUW+xhg/48Mdb8iwyHCoi5i4WQKSr6kqopWVI7+r8XK0fhtSvHFQrruEFQrsm6pIffTCIfj9x5wlA55m8E5OJz5szk6E04yozuPDFreJeShkyvBkK2qiTeDqMfjZB1zXc3RRBE8Q5VaNnj5atBowElOqNumiAVGa09tRLcn2bxucE+pTwPo7ejv/lBAfqNmHf3r0TIQKpgjhx6UrqHCa4OJu7fboIqJG+SJHGBYzSXmd/r88J/AXsZhd265LzbVFcVIBtayaEd7OoEc5i4nj49aOl0FjGL3MCCYfMBrfOQAsn5Q3o6S3Qja/6jlHdOLY7YIdeHy/T4Ap7ghtd63/Z64oqWQNHVFhFTWZXOs53MT+cvzeQXMWLsgihpkSGPOsekUTvNkL5SmOPkxcK4aJFy3tB/FRHNXiiuKQmoAAY6MrMhxUX4NxKHRkQBUpEG/c2jEoBTUtW+JENpgh8Q1RTgfub7scLz0u4fyxslxuEZUxPNIfT1DuJuGcPV8A0g6Edmfsmae5jz2dbTK4XejSeWGdYCMmrakZRnEnzGpHvkmmJ4SPnlwIjHhtQFKPiIlWb01qViJonHIKXmhldpgoaOWGXo1t9bcVRVVabjmjDiE4qtX/6YPbVe52pkZ/dJZ92fa57t8DIIV8IRLpv5AyFf62GNA0K5nRGBIBNLVIny8PoBM8Wl0XbvPpdue9E0giV8VKOdwR40pRDM6FM6dI0KGwyY+7QW26G1ejwlK9VwH68yk0U4CirtmzjddFARDGXq5Z/Lz1pqTl8u5m+Mt/mkfhCu3YYSFy/cUul/S2/DjGKNeldI2/143zqSAAGQpRlfwco0sG8sk52Ii28EM/vqj9wgUmC6ZsgIhG53zZ3UO9R+U+t7JYSldgxP732hsbd9FuzZcYT1hsnYi+dcfiHv9MEVWG7k7DcM3asT56ayDlJgQ+Inj2oZuiB2LUu0wJkHHqcDNBKYQjpmjDzzOJxStXVIW7/jk+VT2m+UOjpeFf2aXz1FndT5649WE0CpSr90OBD9rsYBUolo0zAsau8sEXsiAmwKo+mwElISsaRJy+CFKoMbi70EDiSVsmhOBEr0n2oADvP3jy7ZRZRkRPibY93OmzeAekTQapEoqIrxxPwg5h8LY0MvxKbog9+Kwn6Da4XI9yKvJJlhsEKps9Si+aFBdtk2rjCvnrcbtljuUne8vHW3nKFCWVgdF2iy026yl2Q2kbmYMF7OV8svvqiuGlPwhRN3sEEPKiMDDE31zcvmfyOJrQLrCHJ2kuDytDRAhHlmV9NcSJOATka7KRfZCdK13xIBLVcLCXh9DHsiMppsLJYbqbMuRPhQmRozsmktlO8H0hyFm75MOqr3vSCrH3sk4cRiIKdsUbApMIGMBDQBFmn9gogZfycTBCn8+dGtKcW3CS5+0hsfmTPMCQ7G8HW05ax2IXqSVnRROnwVkWSz6NpaVY7QDS6p8s5gtEf9D6mZmPrVg55aYb14HAu+EykOt+cJaNzpoi/pNUQdKuqW7xX6+1yiRkCj4/9DTRz4Kj2vjFMZO4BADEnAAAEEYMNosreUpO1fQ6rLCsTJM0o7QIqtMgsyqA2w/rbpj+Iq7whoZBU+95keGGdRfZvM2q6AQPvxa7ne+l0eH32oC8oEzVVgZk6gvzHD1bjCAl539WwoOMF4lVCOh2NaPrvKxZtV70fml9P6CfGUAnJ6B/wN3D4SnEd/QxOVZBVKy4SyaW9CJhCaf/9vFB9DGW3/KIL+ALKHUGoxwfX4sgNpsgN+TcD1z+PC3ur2L1xK+vr4dfVFB6Ok92UH0CFOyMASDwMZQ0hHVd2lTY8Cwah+Gzh6buWXcie05DfCVzRvENkivY/B9bEstcau3h/qO41E83aTFRHvv6/8q4198zDe2btttLptQ7SGaNnzWtiPTgFmoPxWtl+LcH5f0z6GAHUgujynUcHeXr2lHZD83zoT86Zl1WKbDzRAC8xDOaYdzhWg+YWpVdNmneEabc/emcQatksH8U9U7iIQ/2hVf2UvzrtbTQpSehfoMItZRA9LznVU8dFEvTeU3YDB547VG4LquXDBfBJQbvmjkC6VG0nCVla6jSHDx3HBgyQbnU4j9bA6lTuIkhBLs2OYdj8PSqsFajsfcWrMdhkjAuoe2TXu8jk7VsETgVB5qW0em85KyQcUbcP/DoXzs7tGH3OBGpKgoCIEE9HnRAR57VbUnMtFLhWUS94+qKoTHDPqGQs94yJVzpHiaoLZU48Iy+HsreJIPcybAwfvmF701+xGTKrrisarOspvj3KzxWfk9DS2z0xFd07aEb9BvIbuBoiRhdVrFwbEPmEqOmneLxkxjQBFgZC0s1GfNQYDYKxgYCEaNlkoxieSiPt9/wrtWNlGpoADfrZ4hAbx3ZxAAAPd9Iw5C35fr/Qiu+5tIo8cajdJAONp6GF1mzGxGxVYYK4Rc/LDSapfdOSlJ6lvIP5CmzHDjjdbU+rIs+stnRbfxCY3aJUTkvhBXQJZXJjxSJgPJwCparAynYGV0LZN2ONbnACVY58KKoyq/sjvAZ3LwfC7cHhmHMFTsK6sWkW5S+MZ39NRx5gTx4r2+xHpIXzIbbwok30aWQwTnoXXa8xlF+leWFCLaEq80Fj5DeGYqgz1El7+LLzOrmwMTbxTXVKGfbRhPIkZEiAxmRy1FSStK6PXV6BhmA8tcyv5gh+o2mF94NpjmY84fmdUVQ2+I91ncbLClHImBTsRB9dk3y+9kUJYcFi6ZKoitdfkYS4b88e5U6W8fUu248LbKrmFf/y5kz5Cry/NZ7SuzzxysmLc7toVFH2/uQfKmtqu0w5vCjObwU17f/dY7xfHDiNSaiHlEtRab/1AOpRqxS2o9wIat/Zv7zYuVsmS83mfcA09w9IOYDpjJNPCY8/wGiJDz0Z2tbqDjuM5hxesgk5UNMtYDa4E0CpqxcTzXWeGQlgopaNXI5bWE/BQb1ZhmeDeNXZSIaHU6+N84Un2wiz2uBhdU0zOt4Mt+Rql8c/3hDAhwIy5N4qB/TVqfEa67+dXXhptPVj9ojnCwgD18P+1vqf1k9bbnkZx8tW68d1GTal8m/BdCAv5He9cJeUzvuKjj7qh+Q4P6jChpReZTv7HbplCwgNR75BavyOm9YT6dQ1SsiIocp2HqSaHlSccYGie1uCJycB6RaoWapM/JRjgoy+bD5A++jt5YVFRTRNNItMYxgmnqXGnawwBKpEe+XD+GN7DmivhJkpj6Vlbmtyqqrk2r4gH8f/zd0vcmx3otJUVZbmoYrJjta5B4uJ2Kt+cHOUC48Fmjnb1YDG0dl7sxm+O9kznyugPh4dw6Xf9BsLReEKGs3rXG0+atjWHWPiKHNdqVL2CwMIr3br0W78+80M/Riv96+7qCOA60eGfks5cLSbs6q54R7kSjdoLiAryW+VY4eGBJ7ZKGNkXzIMvi12b1YJ/7aFPp3VUOH5SDRXfUzxjjPynGOWbHlENONbsUPdRQU4hsQgxmpEWnyEe9Fdrt8y/w+tjvEXZJQi9sQcvdVVG7qrrhLfVJEeAwDmXMR8CJGxMgoNgsyGr6Qxchi9n9sSY1gASGjp9E5UbEvYo+AKw8QqXQjF2foWHrkS3pUDGNkMwKg02qMXF3lwG3iFb8yVyYQ564lIJKiqGnx1FGtfLKJLwCTUHyhjdsF7Cg8+2/Uh1yzop9IpWBGZAh6xsWFzfIMfGTDRb2sRW+pZShfkRTEhZW1ZlzVSihbbq/1+gq5qc2Tfjg35HGkSNPXmP5qm05JtkvM68wLUosV3VxSKKkkde8nmGNox4OSH9AG3SItfEUTonjT6P77rSlMCnPgYfXHSrjttNF+YY1xPmkHm0xM1AtdQp2eDPwuflwh9ft1FwAeV9rBwMBacrtAW16lr9lpPoLPRkuJLBs0udP6I0UXkJ0ZB4HexDM3RYSrT7QXjROKI79NmwM8PWS4jvSbXku2Sknhl7djxkWSNEQpHc7CM5SqLpCPcjwJTLKnqUKJg67Wwj30SOiKkN0xyW3bpzdwUxhgLlMfLNpF6sdYbhKEqMlkMnQ8TicKVkxc74HGxOHYp4y3BREM7aGjkIIknUnWwOhYgby6sdbcxDnT4r0Raa+wVoTHo3rUMLqZNm8ZvPfxAS9fA6iw1XCtZ6iCdJUTGXkWrRNKtFfvWVOk9IMOWG/hWLzpu38pwKOT8j2Zdh/2fBdloYLVdYuNq+50eeH52fhDsjgTJy/llwtsRAX9PlJ8/8VOkBIETfoQ/FP8zpMkjaiy3YCogomBN/mgcQXU7uBkSeFEQ4LYjXs6Ah4qFLkKsBsSYYm6+CHKG3aJUHiyVJfREH3zf+ZKPJrLK1fS0dPP76ncPA90pX+IVSsk2fep2E3ere8vY5bQ1m9LEoMGT0HoncCCEefefRouK3R5V8XvHqrhlnt9wUAypNP4IOYCPaQde+3pHqmCXXVH9XF7S57sL7zvSvzZ85W5wOPSyPV2X4RW6Egt3WEFMRbuO61URAI6TbghrLimEZ06LX4GzqaEbJfTK9itjTg7HDAId0Xt25gYOlLppRKsDCMWHdflUEbduxOodg1nGS9oQP3+QTQf/KU8/5htQNbL0fc0KI3Xvfw8IP12NaPR8Gd8FJERgxR2jah0ViI2RBQAh5Lat9FejZRhkx8rMxzJFYVcNAmdQVCCl90g6AGBsmwn8mFAoWBvoWSg12DE+5W50bJcL+KAkK8/m/xWVxRi2c+7wr9yGpgIZrwHO8J/tQxhC+L/W71gqZaqSaoh/H+IzPQebNoWtK6exrqknGPz62DxKm9wO2pa5MH/tEYBNEtfyeESOCX90YXm5rNF/f4l8yibmspIRwNNnY/dUP0DgC2zacuOFVZOpSkbCs/0/b6CALlxNJNz4NkfDo8BQWds086CV8Rc5a+DEljsGXW+CiamBm3++CnEpqUsAKNVJGvv7EJfIgU/DiIWkI2atg5pDii1CQViW751oghHp+GQTXxtdto78CpFWfBbXelbVF1HNV70pERmBwaA72/W/ZghYoh4/4fPcEzRf7YQ+/mZwJMviSki5EMu6p+UEldjnDqH6g3MWvHIgQA4oLlqbdJTbpndG782RRQi145PbKWhS6M5DV/fyccwBQuu+bGt3NwgTTNBGDTN3Ads36aSkuE/GWu04hrVNKOsKECTKSpkfZRTh4GWz3XAx7TVz3Zy2n+NuHW723fpWreNUUXuvWKla5Qi4GKBN+6xy8PGv22NN3D/gPwAK1cn4QifcXvlXuQP+fwGhcSC10d0TREt2NHlEUJVhU64kMYzDaKD2RiLD3ZgJ7/CQGsSomiOb5G/hefgSWDTY4Syh96WgCio2zChbARoH8HduZyFAoQEvl41zC1L+cV1iq5fDbn8G+1rUsjgCTLvLr/UOX84sAHtOlcW0mAdDQqqPLbCRJTer5VkSgLPWQXNk/xqU7g3/aLUxVvokQ+OXqQDAEH6FOOAKgsZSY/jmVd+dkO9MMUHliC84lU+eBpp34JZHdaaHMqUHaKWlL1b5cYgmSFuyY/N9X+5s95kyruGuP3eu09HzX7Ad6g481nOLNTM+RsYhZ24bLGGTX0Zm14LOX3ekDhmcgJiMYXcJhkC1xIdMMjY6etCR45+3bv+BE9bb4jSSsy+g5VoKH7eiQCOrRx98gGgxxvMVdJ928+OrvyKpdsE1VKxUh46eM5gIbSrwAPznjONR6FCOQkhCTPI9xADp/+BQOaABuaB9KLQ2N1h6Z7SKbAhBYfh4SIfpNvbzfhEH3frIGRnGLLxJQyhkv0mQoQeIAUtCG7l8CtTg9xXIZb3ywBGjlWUT5UDQ+u8DHc4kO0g+0ltNTCrIt0qQvYGEaBONr6Q4tpFJawmvKiThL6Bu6LFwBfWCqRNbiovPgcPx6dU7JGPASwNngjf76swzHDzF3EY8JhjIkd4TsEXhTMP4u4EAAAK38X0Ch1Wgtby40aufyw39DSax90Pa/iyrhP6YZV6JpYhAAp2ErOc0AizTUtZSiywlZ5vwNiHzPPpIXUp0FECzvuVBRkhRxGqjlu5pa9WQkp7BVF7WdB0QGvZHu1TZBO9jI9Dm13NggGXd1FkISHNruOq7toUdAWv+ZoL7jIOKPUzpwQ+bOYlcs0hc6z84hZ4nFocXC4XWgNm8ToK/BiT+7FeRWB4TA4I6HtJKk8SClHB7n4AqK9ZrJO+h9gQrXEdu/w/jEGFKl8+B6PQNIQdLCsT3amp2Csmc9spuaswhCH/d+eVWAYcag8kxh4pepakMK68yYNl0MF3AipBWv1tsmbG0XlZcKsxLuoIw5aFpvlCKkCiHzNg2Qf6aP+LiVb/VatKc2xpWXUi81CllIkqxFwrWdiCg70BA5H4TjcjVXFlrB+4QgPkmAcDz9DY+nr/QnkT5lLkjNesS3YP2xx1qDo9mIQcOTt8lKf2paq0IcQeKqjjchL5NWQDJLLOt4eBLEddsTO1bX3ROjEbxx/q2jM9XjGD80mWrHhROSKVEgnfIr2lMqtJd5kUm/ZRZgubpR3USrNHvElqVwB4omOEDhAcImELix0aO5ofVW7CJrsFTrU1mGaw8kQdcaa7C2yMud1OvZq5hIJXI8iD18MPia4Veabi9Z4qFlA3QNXvP5itZn0bFMTshbJ7kbzj0kBek50pETDAVoihr6vV6yc2mdRNB2eBs2FrR5LRvoC7aJrthFL8/Uwtvz/5wuwdV+cIVx2yYMbs8pIkwhW3POsg1KRZKDe8EEwwAGuQoDnARbhWvIfbhwdOHCcXFTs/P3qCbMA/VpIdC3hJ51+Hg7IiZQTCXQYU2X4WaC8280EqiEvr+a5hPYn7zOoHxPXt2VuhCwO0QV/yeJVBBaki0cagBPeJgqxJOBOWZqnhemsnJayx6l4hk2i66ozWSeQp6WKual9XyXzAEwmSdF0hT7gX8eMCdzUsNicJAfvthOtIfjHO1Qoh53maYpDumDKVXcjpddXLKjPTD9wBQArM4Jzf3tNTKX8gjQXN0IgVAPcUorLtxvJqJgfqNzjHXvgT8sb2PIHZIzVjpr+oo5VoBXIffgbPxv+/5TEvEXO9HPqQU5QyJ/OP63rbdPVA/ly3LmLxmoV2D9KIjGkr8VGd5jkkTlosQ4PCu/nZiOpAPiNyhw+FNHPHSDXMNWpmZXMW7EFWLQwBbel2Odl7N6QQ9vSUGDZltTw5MjFjpzq93ecS2p+sgUe8ltb1TFYWnhWILLBXaj+8s6qMcXwKT3J9jrR4K8gh11GGt/G/IL3DHfaVj2rJr85mFVmZJUzNJHv9D0DD3Wq/0zAo2cSvKjOFMD9RQfWi+pSMyFX7lF8nl57hdZ2yFtkueTq9QSFcJf0THzAw0HiKBDzfuXhLgnDZt8PsdN9Xt7eeBF8Xfemo31o/YJok7TzMcr8NKOCFI3t73qlKbeMx04QHRbWwHF0RbRlnSDUUdBzB9c9FYc51PtdLY/Kt8yvs6v/q7kisxwbJp3cuTNziKZmKYiVo5PW3nMG8gr3Cr6GArVxNz7GBGlgKJEh8CoTm8p5hKzffIbUG1KJod2cXjrBJZJQOScYoTLs8tNaaNvyJ3bKAAE2Z0CAAAAAAYZ5DA+AflpYjChhVTyDF4cWPIg1QHLnc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc7nc4EDFRvEAAAAbNcVLRJS7sk1YQ5NjCpA2bXl6yknU7Pzhrj6vklschhI3Ym5DCOi6NEHX7ryCNajPeJjaWqwrN/P9RgJXXGtEZ0evELDWmaaks+Bb/5NpJX59FEllKmcA3g9wWt3ormGo9eJmx0ioBRXUsCcFAVBTG0NPD/QQp0MS/ydghod6Zr4nKSd/XLff3Zdduv6H03Lm89NBQOcvXodkfDk19VHFOI8A691JVc9arKOPGnmjocwmIeiQrve8lR4btpqCZdwQeUxm9ZcnmC+0/ba2demxXx6Xef5n8uHK268seviZ/sFkc3/XpLrStTa9qQvoQdGzRNRjBMYb4qwqODv+PmE7EeJ9Iutqm/7NCs0AfD7AeB9xX4mHGh8llFqywNkDPgBBpDPN8OGOVzvfMCu0WGW6U/fin+89gFBjzqsAsOhMfrrkX6HoD/wBErxqsxJ7CsXSC3g9JJJLkRnp3R/z5WG+JVro4N85VSK4x8/TWiWcbcUI/ibzYoDqXSTNYPAYW8rP5xB7XgpADNnXFPFR+dZ+psKJJ0LdT0M45gV6aP/JHzWiVgXyqt8OlD8WRpsIC3THCZJ/3yRIPhBGWdwe0kfFkslvUXZqOVEydjKvu1+dyCnjALUWtkD5faf+qVf9tjLkbb2psHfr/Y1A8ioXDRJXESIGkfPi3pl8W9FF6WfjrcyWjrNpObFJK170w5SMihj/gbgJOewDwp0SmtUxLFvK5CJ9i2m8WSGY2fgmSLXa8hfwLxsoqCVMwPukSgMu+7fugN68qMwyMby4YjuC4/i+sd0SfAGrgLclj5xg4Mj+F2RltjIXp8AJ3SCZT5lwx7OS9bm+wIwB6ygE50wGFYXTVBfUHI55ercWK+Xa9RZ1PARtUZDE6cpIxxJ6e8S7825DKBITSFDB3hNrIrStc7mBmCJhcwSP/xkh2IYYd7pxh5y1c2QQPeGV4C7BgYlF5s2JslfbW8qtdMbJZzZKs/e2yFMJk6K9W9KlVbI2Ba3rgQaW/VnziuSlIrnG7QivTvBKqwcjc6gVqcmyuEnhXUh+4/ubQ17mRazTa5ZWT/iGNnh1cF8r+I6DU5FPCd4XB4Kuw2l09BFNVrjzbVm1yDwQQbWE/5Qu863Ywzt36i8BLDnG7kbizO66Y2/EziSMI7GirSzCD+Belwp+rPm76PH921vZYXWXjWL9PvI2TienhkBMgfOLOTfU34wggmBpydEWybJcqiUI76lL8IbvTvmRQMW73TZMZmsiTD7sreQ7iLhiWydKi35VQobyNG1+zGJLlHSgVCJBRGfnyiks5p/cnkv3yVEOlmUYNiQrE8qLZqqqnuMk7Zsa4INeoYJjcvYbywGm/NUfoTt1OLdjAZ9IjQ+7zdWGWzmaID0WBwO/TyiTj30qwLfgwx0SfAm2PaNqZy7znalxdqHMuma09ZR6s0qmo5XaDC2G6kwyjQzN4BOCJiEY2acYGxhJHbw8dynweG0KTyvfEKMTC4mKsEbMlxYl47ZnmjELcII4ypm/fsc9K7ny2UPQOG4HTFALgZosFpAFN8SUlHNBTzhA0wyzPr+qj4uNPdRuZJfwPXJ2Qs7332S9dv0wsgFrUKjtzByLZ7pGEGhZj4TVrdRICBJJaQnYbDPx0JgCznXF3m6kZzmeZG9TwYFKAPYURkVrWqJ8n2gADwBY3jXTbWbXD1RSNiD7+WXD7fZsx1Sk37cdxqtdmRfEp2psTZvt3BkuwU3NdLeZQc7OJLJJ+osUswPvrVXG2C5blCOAjk57IjtKCbZUWXAzk67dKwy7kvw8WvzA3shbLZ4GIAW69o4sIC6aZQbI4HQ7oTOEvgNZenpukPTaMDoNU9Sk9hL1seOvhRcJtE05hpe8QtuXSI2UVKjnR1m7W3PjnazGdjTp0yY/GxIKN7Unx+qX1s/tFKUcp5ri+rVVE4fhA/yOpLdBd3MpJRe/doGgTe+XhHFXYDOJC8gr68QGlgQ7EZt39pTnXFBP3Se2ekomtYJTRbB8YLhVxhys5Jnhk4+lzK46NuoElk15+pbr5uuwJ+QbZu+AX5SnIJUiqi0CyMy5Qm3XR/RiVURprjCnGO6xVlDpoaeR9vRENu24KVeNRnHCQWFhdOR/srk1NluJYiUQjcCsaNCXZPjRsoT2WM+Capw5zla6d4f7pJEFkATVb254nFMEPCd/CQuWSLQPExB1WgBKniX4NoqT69fwy0bKW8hfUichOrKEQrtM8Lk1PJfpz28F7Ds11WEBt5/jxc9ofcwD8a63Qx6sKA0Cf+lwc58lcOzWwWFTb/O2Z4/oZSi5JiWqQRdfJeNQ5iZ0YGuTbTpeoHaNwctbFAyk3H8qEVGS//vzHq3AY3Svam3EzzfxjQBZYun8/WSh0t3j0XMEey41hE/0Wzd/CzvBKbH+5GUPxq6TDDW4cb7HycpJkOpKiXkUjF5M8sG6+8tn/Dd5kNpf5Vt3smXtjrhQrTCR9jiUE+6PAi0VG2gHnAm7X6uufWZwOyIB3wFixBV76Fcf3ftQrn1W2lX4HiO/62BcxKS4xEDfUsF4zCbwwNvNBsZP3vq7fUnVBZNwTB7QQpLEqu08O6i+s24cJoppPlZjokqFqkAVZPBzFV8zdPwe1vvsF/YW7pc2wUQsaqFeGTpnu3/0XR/Sa1lR+Q02wmVg3IyEr7v4ASMVbDcwV8otmCgx8yHv9PghYjf+gXVrpmTAOgJWFjLliqKg6bwDsweh/f2FMGrhj1CvGFRdWUbkyWoMVfxEW1i+XMgEF55gbIcbiEOfMvCGPArM3bDWsT9xumNeFM7ubm3nvsJa28Ui+er20JCVon4RnR77YaqG+vli8QlckUdg3tico84X1t8VqokL+UW3fWK/4R3IpWNj0WrYSvjzBIV9cLy67MzBV5xkt5pDxRTRvX1GTLbo1oJYRgDXHbOFCOe4v+ygqjNKPvuBxXo9n+GP20yQhbshqoVo68OUaktTetBsU45DVEkJ1bJKWajeptRUk0ZNgjYRcULKYLP7b5AQEFNCDsprZC7/J3WeLSYyl3Lx/ZXWPdmtinvp0It8WBi3qb2cDQS9a4OO/Qf3mt5QTmETyLZtefFdxdi4cRSwuEzyvPloSpNnCg/K7oA4rY9xbOWEpS1rxnAcYaX2+sjezXeDoPiADSHpnz5eeACvVc/k6bzwJkyPmD0bCZe5ZXVBmUvgB2krW0R3y6K2k4qKca95UrJuA1pVMppZUVteYcij7fQBcidwnUNIrGwxN5sFz1ysrhCbNZBIjMHwLmA7iygY4FQ0KXbVZzzkL793CS3NOOgQUqxHp2OR1BjxH09DzW834yRqRGLkFnAD8qE9HR4hBr+STGcduGjmmWOyjfuP1+9uRGITGq0h0T+fcffM8a2HkG5C3tOteefvUrmD8Kkw66zFrYwXmXDW8eLzG3PlxgFMyf7+OxhUoDcgKyEDn4NvL4GmfIryxJDNThPydddW4VJvrVrOn6QX9VxvE98SJD3fTDI8HiBzQRj9+9Ppwy5cWZxXMtnH1+tatQ7x7lYU/yHDtn+x8n7YFi4yPNbLkB8Nw3OZb5NH01259dNbjXyKUIXl6GX1/ZO4akx/Rg0qN1nJxq3oO+XSM7R6v8A7al4YJk3Nl8rcSgjLyAB6OMHRAr/vEPcZ5GFHsfEg7Ndu1Hd+u8nD2FKnxxlJskXMZ5Zw5yJNmHAbDGnaRLwpyesr4ZS6/a9MNDFYMbshelBImTAql1qBXohSHzB3X7Sln3B8bThUZ/echvUpGlAdhwpqGrFwWwwzCDyJmenQ/Ot+VSv3uvmiFJVqEsQJDDABxyIRdKD5U/guia52xxxN5La9xLwgu/s0qh1GHF8fAn17plER65UqY40PJNIMdPd9y4Zw5dwXrf+GN3SfhBAvPeDX4/6XO2YebAJLyTd+oyFqRssClhsSj/Rq102REV2qKKen0ekJP9oV7e3eUKyKxcIER6JMjHRzKNJkpTerYNoXB/Vlj+Ak+l0+JITVb3Rki6oxcmEmGaX0l8fJ9vIEiOdxqnQpIAHay9Z2e94HmF1kYIG/U2z4edvq4Mna3C1FOsBD6HKPWVfnVsAz2jOmacF1zc3Aw76TTQBdwGD0g3CdzowYHQNEBY/2xjZmYK+OEwFXm75CfXQA8ango/FLNPAv/EPjoiAKR3tdPgFMCJcjzOOxcfAIxN+HlXGcRXSoGvm9o0tJJ9Wu6SMgGqqSFE9XZ/VQX2PVLGuyDsA4cViw2H6HaLW2QeKgD4FAAAAAAADtMphO6XX5EzkRNHJmRQAAAAAAAAAAAAAAAAAAAAAAAABpjKk0gs7cPiGAVQ4E2qIVKqovPdFM7JYL6HlbvKAd4j0iPhzCU3jGeivghl/U6dAXDR2MR8eTuQIggTOdl/PiG1M6w+Z5cp9n/dnFx6hAXpyzcdAvrNFAj3/jtbPWigT99ou0x1LETxS9I4uRA8w7Rw1L7hUvDNBtOYNCMILp1z02I6ZrTByM79st7GzTYvlxrzJOJbqgLBQUunG1HDp/e06wLuy9bHQCpSTM/oRuSj2RGyjFk3GgegZfBHe+Odaer1/cXcaoPii7HcvQY+Hz5+VIRQZnmaT3EtW4BFKJnV6qniQXnALgjAO0qaCL5Uyqs851GQRzlsBiw2o0wR840oS/g2MDL3QnwbBEM2Bv4+1iJW8cf+7J3T7Bjp3volsACBC7f74x9clsKu7gyRo3ZfXgUqGA0GIiP8x8x66AAAAuaFifppD4TiN9fOeg1wwYovDM37PP7V1jfusi2V7Tdl/tAIG4N4z43te+7djXFOyDxbMWzkTeT83DY3E/dFxvxhYjD/eJeQOi8Is/Baol0YJ4QzOgk1TBC4bZOQd+3svVtLoAEJfl90c3WPorKSSbRzmFGU+gaU1saLbqa8Ak4nEzoyzPcEdD2cpLEWKN5oNqtpfAE6a7d8cslp906c+xqfwcRRR9OL83AdPFFLflKjPeC3JmrdFYdEw+Pqg8+F4hFSlC029wH0DSsouK7vM59D8rU0xl1/NfFUIcIbAfGi7IUSgWOd93LdNl5mxLbMDB2L8AQJvj1Wx40gaq5KiIR3DKP1hGaWzaCEErVYipQA4E3Uj/4q3dU/64hUCQcCgL5eY9ApfhiD4CbeCWsC8uR0l0jgmCKDBMJz8C+zE9tHqjouCckU4o5KAl1jHVvMgVtrIirmChSNtgtMfhfp8R/vOnWr+sIaNHycLfavJNYQZ0zHuP+xHTvBlJQqMu2Mx9v9+1IzEOTuOz+0D2oyqr1w5cwPOVa4jpcmbkAKe/CMspKTNbrcQVvUkSY8CngPbiFq+dUzsSOtQLkTsLZ4l5AE9jY5z92Czljg9LbBp0HA224X8rh9WS+aIZhitWMyAlBgkFYRnuSJhI9SAXkW4e+3IRc8QolnJtQUuC2VwHyU7PHPWHw57igOgUlEkJOwrH3OvKDOGEulbCoeeR+TlJJqfOXx+yNYBqOCP89NM6gMAugyyn4uBRZtZqz6etGZVlqctF+uWNNB4bqp54APgREZMQlatL+TOyu3jV8nfiIRDLqEeTVZJgba9OaRCVJi8SAJJW0e9hu96OMDncJqRFWux/jBnWAQtXyxeeH7ecv/359Kbn06FUCDQLmkTHL55sR1kSB6gqbbZL5CdJRU88Ke66Gy5Vf7g1NNRQv8VzT6M55heFP+cIYPHNEXCkTewqAZ8hK2ugpAy2tT7A1yarTij+kgyjXIbHpgF62d5oum0N9ZVswR3nvvAX0u5lASb/gjx1AW7wRpw0DmXs61a1CBXWEisFeMxb+GC55YmvbxCAhSD6y92ouXQYSzhNJQfgZ7WLanjyPyk04CsryeYHlpv3QaH05tRLyhHWHGDEb8Uf7rC0PMCh0suGFfVSRboHWdfduVEfzi7lvVfsl1XyZ1mDLa7ehBxi6TvzNRSA4BbSKfeadU2/vOlijibxrinPY5QQBQWXwLz/uklafRet7Aivv1VoQ/KgDGik3ZTy9KhsFwVEtmILUpdkbC2xWQbdq6k4yYwdnMT2fw+oGe1Ekq+D3UadE0X/wNS+GGq8maXkYaP9ArgLHdHEuXk33ZFEirRa7fWyS2KrgRfjt+rM1HsWzBs79jNaTxHouRX7w6kRa9nq6byfpu0Xjg9nCG1cl7bBU9udjYCy/JJz3Ybiz+F0ufJcs3971v617nfA9At1Gd/jF9+Xa4Nt46mcHrOpnRJsVfA7/vum7Y3s40viDyHzXd/yD90WoDPpQIaTjeHkBcjv/d8S85MH3Uu4LibYy56nTOfouSrS5L38Jl8pAqvax0awYJRAM4ngRYk0iQsWilxaFEFHi+uRp4cDPKWojatR3J9zdSbwd2D8VZzWP5fMfFkEDx0GGEFgc+TmLSs59gPBZ5bYwT8OUH68v3msQGF9+AtZNv0ldaL3v3eAFRSAU9+BOkG1c2HRpZEDFWkt0ybhYyluvSNtSAZFcJmEPlUAAOUcc4bqMJAF3OafCwh9iVIO+deHsieh5ZYGHtmOPOCNw/6kw3pOBVHqlXQs5O4rKO8VruudO1J8g7eCGbjh8HVlrudYh96IGfPwLUDElbEVWtMeIZq3C5kk+h20s7/HMPgIlh/RzrHFhfxbcXOFhnz02fy5GiNJEJkjvLjOkL6LmE44LvdUEfOo98LZ78u4vPCWsP+mwOGAXCbByj2VtlRyI1Ow7fFMcA3iPrVcFU7HrkV7FR9NJBY8OO2+ESG/TugVOZX3roTbMk90i1pKXSKMWXssqNYY8pRHn0kwWbg/xWTcQA29nrEyLtUnGIFqtiot+IIAE3Qrx5YQ8O3fS/BXjhJ6vh4m9NkhqjSqVMcvo2MSGzjPgKJdhwSu616rypR2ZaXqMIvqe1tHWcVU41SHVg99R7fkiVEA6jqe2s+i4rEPBnZr6o08An4lLwhsgke4CiYvY7/Tnk86CyZLy4uXbVTOv+ldKEcxVE9zwZCSu6+My3/UX5oC1EDMskS3J9rbDsyaomLFOQghpH3/AfSqO8kliGcSEfhmStNPepmQdQqFiVvJrUTOTuSwZiM+O9Qc/fQOl5stCagr6DfqwO8BxEMnYajAPB8PwFn/P1e9MR0gzpS30AXyrYDI8dcIS9cd6au0do3hnZrolhKhmKwzssVJk1XYZuPPOCrwKX+h8Qi7ArEgybHK5bETxEVDqprA7BSnt72enKo60K+kx4k+4TVhu95ApFIZrlNjfxzZrXed86NT5zx65qMAuPd6ltzt0Y7e8f2zRCyu0DsLGxuGYT2H1cKJJRZ5b8THUk7H+mqoFMVRlvYTigTcKIvGurUJ3paaxIVtYXwZdotw0GCRHJIxBvYGDFpcLGJpuhNuR4NXU0DWBJgpCNQdK3tgwFgtsDPrMaWo1/nZftQ81huYCH4F8V2EiuXo7VmtYooh+gANAPZOZ8l2iD+1FvdOmtNrRp73Ko1hhX2PXeyreeDDjTAdw20fsSMPRn6wzYxfOFotI+yDwGK3nwURKISFGGvL6Z7TG+QTTZHJO9V4FRL3na2fnYqudeb6JJkjbudtgkg7N83IyNpgXBIVCg6T+wLEvk1iK+LTh/pnoNEmyFbCFnnah+4y7trJ+OqoR/WVk4BxI2gm49cbCpxlaC16Z8Q8pbl84bXLmmCaY/KdqyFRJZiFDFpcCoYqOJT8CwZvjyOYpHbK421n41b8cS982P2MbyQif+RBBg563W+s3j6K29u1AFkdP3LdmIE9hRb7P98Hzp074QKsz+naKGTQzVpf8pNTHoGVYgFmxL0ZiMnzQI07hEsRXtytuaNwk/Oc6PpFG1d4z0gK8ywn3+seBI13nNwT2VxIDHk4sQ3/a0XFgpt7tSBuFhEqm5Wz5/6f+r/8KcNMVtohzg6374ma4rltLXPxcVLu67bnmPt4C4vcBckUCykzyp7qBGZ1/SjM5OwjKbFULKt+TjlTa1jBXa0cwGh9Y3wBGjyUVTl+d65foX9NAHFuaJ5gef2Mr/BfeCCY4lFMoK2L3CVrbd+Rhrhkvj9v0ay1MJ/yhIZHsPcylpryAWykAAAAElFyRkvaZXZoWA77wYfgpQy7Rp1sRRgZ+1JRaRFU0eTRKwjXD+s1bUimW0X31ahSx/Te7sQBwXHxSnTTCPUyJ2GpsJyD/UNQyNzspfqtFv55CKOhVBuHqPWt/N0KJh62zaLhiBFB4n15BWEjcpq1s4K03ZAnPquj/3fR6lTA1+/Ybbmsh3MkF/NQ2dWfeYMEqWi3WoUamCOiWRoMqsZt+I6dmHXfI4O1ZKo3/VlGrv4NESD41WbdX9ADwZVXBk7TPDZr0JhIrkMtsjPaMFWQiMWivDX092cayCE9OQg+NhUNrg8FAX8wsud4/ralMd4PC9rRBJfA8FrI7jzJ2m7QBDg7LMGF3CEe9DNFiS75Ma61lE5nCEFMYiPU9SeZ/CnEr2yozkhZxS6SbmrCogavJFxduzdpTgENM+tDozIaPBhK51cvbFEZNcNhLrmtG9ONeVNeLokcjoyhAIPpIcRsmbQOhqU5imNliPxcIlQQVu258stunI9Iu4OquFvbtvTG+aMWJm35C9JeviDNQRso7Z9Ak/UE3h+g2aKPh4xfbeavMDaXFTIHfGnHKtgBx9rj+/bUR9aq8qUXkFdAMrUO8Yi2mn4ZOvfKrihFu3dzVSBHv8AeGGqYGuF57FaTK58ODL1Y1TxkN5Qz9/Ffcd6xhSZgDIr3pJDnitPH0RAZ9Tb5EjFIisv0qenFgh35SXoocZzWyaTYCN6jyyStnJrWZZmhjUrnt/Kl4XT0RWUi5Be2ZSIRKVMv/jizcFQjrJFLLBSaEyLh8gH/nVGiBFzGLmcZSbOQXdc01Wzd31OZph0pjvGA5ZSNfJLG34djjzc4nCAMMLQYUlraL6YUO4BzfGIfj03EaydBE8myoQ+qb8Dr80ByqSJsJ1k6SQznv21z29OOtBKTTAWVfx/nv3BoyeJkkYt2kTy4YgUcCgizsUxeg8J9k/5wjDHfvb96OpipKGpo5C5CRkGRUJ3SdUssupmz0HsfqgRM1zNlowI7+5hq3lUEoYMvrZCqVqXPluB7uMa6Bs6RrmaT/gPzJMPYFl0JksXAKVAUe10jUpUcmKcOxM6I2TzX1IhUqKq/fbUu+/N17Rj/iq1/CEqYwSzzxw7O4VlT1R2084meyYBRegWDqs5mZotIxdz4xGhwMcDXTrEmiSelb0NWct3yOxjTc5rTCPa8cKlaFMi/DHKiY1L/XZq0SXNEKVvgJ5bSD1jKM0RR3e8CKbE7NXTvNUWUWVolOu+qOePCWiAYtGf1HhrqKwadESPADUNRSvW69Qe697w8JXFHjK/sKUja4AAAAFcSsSMjAFDCch0wRvfljL/Sb8SB8nx+sNe92UqW0InIk31KIZZi7tW96/iwVrVQDPsL64lYS3RrbpdcKBhTYAB4lx+nx2lbgLR92oEguhWKvkPuo9dBkIrLE0ZEIYEjqKEV64NxVIbOEg+zj9hvtBS+GevXT6DgRSIDD5fvrNOAiKqVLXpmZyOGh3BhgSAVfkP+wRJHBiJW3EbOSdl4vs1Hu3fNBjtD+AYQ7yTagcm8H6WdSc3WTwcYz3GLrM+1DlE/EO8YNsp0wyfZQb1bg/rVrermo4DfKTAA0+APn6o91rtzhuLk1YKQdjxo8BM91e8T7k3jN9TwwS6IODBiBfvXLBv48laSjm4JrwCE7N5fxtQ8xIA+IuwTIjgnxwoNlsc7kSxoOd4+aHQM5sT1who4B7KjjyOyUH24qYbGej4OZ8DueO7OCaVuKV0d6weJ1+mGdBGehfSpSRnLPfRsCqN7OSekaUum+uXFzXEEhk96fAE1wRoV16o3cocwaLGc6OXXSYFhUG8c6Jvktxwpa2UrNNmh5QIXV5dbm4QligKtDou7sybDZUbF0CP/KENajH1tx8PLM450QQb275aYqg6+3QBEjnP0OsRIoBwoGhFkdXUDHcPxk4cjxEFhh7t/zdEomNTIioiv3Ovcp5WFP5XgznN82+T8/mjnoamPCVkQIklWw/jJ2fSoVqk/z6q8oQx++j13+rfD3ygjk3ZBlHzPzjNmMYkCMfrfuYIgDYKicfj1UQHPCBFNQ/YssMoSABFCsMvjRo4ZaqcAAKQSSLdBkR0LvEjGenc9mHpyTJ5CEHkjLDLoLDv0CF754DSqoe4XTv3LCdWPueJwh+7RTeOFIH+ImpUoFShAWCiYJt65wEQsd9PiH3XAcYqbM1vDF1986xXiGiQFv7b2RkF6B4uE8AWqrtc/dRu5Z9oO5MfuGeF1O3rnVUXWyQeaF8QPxiP5hgAnqZl9VGH3a1N6XUPGhgUhiTr41OASC3eIhMqUHQ2z1kD4pRSKtL22Ol7s5RsKHMXSyjjroBuFNQAAAAAAACNQxgtAlzZe7FkKQ4QvoShNdZk0wdxtOSzZz8qRL4oPZykdPBquc1pf2xx1Wo7Sgn+MEoDOvA2Cmzf7bCpCI9Dp8I/7W0iz5d+5IW7j/P23b+v+ZYAzSxWFcIGiFKld84KhV+uyG+gqcpO0+uKRxLqqK5qWNmkOcu9KRtcnEi1q18jNvq1uPOhxEVRisVsYfApfAYUki9U6BljF+0fHN+jWitzbxv9FeWd3DawkvpAwJAy41PTutQsjWCjsGn29H/A94dXdvvuT5taLMzDMhizEC4tR9eTmC0hKA/ibTTeJngxiE6VYxc5pOCezDrKMc702LeXD4CbdTS9fuV8Unj3pwvWkrqCJ6ewFAE25QLDiUSjECJ/7k+B2jgWN0+jpg7K5MY488Lj3QTa6j44g3yS2W+42ZwpOhBjPUVOojLVklx1RDpvzeC4vdm0FLitlDqiNR2T7vwAaoKh42vrcXmtdN332rpBi3xh3Q9Bn6ANyp8sbPFIi2N74chPcM9ZRCgp+WEeEc2KsLm5SXMnUTjxTKaoR5ogv1pwbaoOzlCHSFJ1ke6q+c4hK6sNIaIoSxU2122A0LM9cnX2MNoveNvDe+19fKHQMDJ1+h/UUCZIvM561edS1FNbNFd71l5gp4a+GtWyMpLRlbota+Rh+sg/ka8sa0F3qY2yMpu/Qm6qt/aT4p365VdNVGEUGYg/zNIXy4NOMpH2H8mVBxVw2OlcqiC87ArexE0kuFdvOdIEZNmyYzi+CYWHl/T4GXfjSvLCYNnBLWklF4Z71F3R5PFh8DHOiMFxwF4YU/3f/kYL6Q42jEBNODlTvg5UMGE4vXzrjuN//asyQYI42CJiszanYBoFf3UZLNTyKh+4gn4ZBSloQ2qDLVsu2a/BmZoEVuQaiSyfv0gU+2qfX6ZHnyqDrG8TRGp8fkvcA0Y6SBH5N3vUyQ/WKNidkA6Uru0axTIb38D9K8b7OO4VbP35remYZFw00dbMLXVBRpbodR/7GJiD5kaHIbihRiwFf7plpah9E6OOi/CsBC+TCU0Ds7XlWHvVsiEAGFQdHDKVTOjg6v0rZ03U9+9KpNWHX4zkGJViYD01JIzZABw4FQ1dte4pjn20tX1KHDp2mdQyZeO+ezWQN7np6X3PGJZcyUMvOE2kAgz04FVtHxo32wgQCX2MwaaX9X47YZoXK522XRyT+RMC41Bb+kDiX+SnIyks3rzxDMw9m+wGJLq4yxiqxGlStwjCUUEl+w25LMTiPrHd0MLyeFQ006/NvkOj8D4MlgI6vhhFt4GCDCtrRtAIjAXWVm+r5zI0sxlgnhqkh6LFUQJV88aaluY7LAqkl46XeTwUdT37ermaGL1djNEmPnlWodyu6/44bAwsIWr50JKdRFk5y9Y7h7l4gQyUIyFdaijmPMRnvvSQSINmo1UoxJfOc/NBjrq9/WjDHV53w55J+VBPmilyT2CheIfEVWyb5+zbzYe/zxPJ+c3v/+gii1pfR0nat1hoNO7e0VQQhLsJMMKaxpXZCe4iHdw+qzhaX5DydiUesZPoCETKvLfFCspJbuOnymWcFctOOt/tmz09sumN8NcKYJAnW9D+BnX2XDCdWRpPR69YhoTJbXvzJ3sBY8rlFKoXsYRrEZeO+08opVW9yqNeGhaEGJ1J71clw6i2azFmFFY8MiON8QY+4c5KkDhGZPApJGyTlY+TxkhdsezbmPKwDgKf04C9SX7baZuiUSgwIVnb50sGKqSzOO7E5acJmDl4iH7XstYYW6hh7+nzDa2Jn37U5Sr6jaeGmeXHOBJfyG7J0HAKYLll/lRma77+D+vrj5BWBMZAll2GtP1Mq/VSUPgVR0WD5tjliDENPt4riJ7UxmM9Dz8pAsyGQYrH/aWola8F2YuMYuA1I42hTGofbwzKqCf7pcd8E9fKow1U0yq1kTc86sWrgTZ2tLbjUdIQnwFY6nmkwv56vQV0jhccsNZb+CB3egkYBeTjqBGOq9YAfqt7VgyvZgS0u61SiCvroSv1a6SKMzsvSmMUyBjQiYKMEc2WzjQCStsiPrIZMV4XHxrPOWQo454fMLXL6RRkWtRJA5Bq3hHCjmHcU3+yexZ5z5dhUzXGhuBwzqHD+W3kKKTQAo88Ji3zKmg/U2sFLkjBVzzTbS7DQxtpYLMsWWFfvAOBS7ezF6Fv+uwncHAlXY5RXJRS/KZhwRVvj5m9Wia+I6GUFaopflQUGRWyHRVpAZ3ZNzikW2wgdxfY4H2tiooFrs+P8bmS+5Z4vRI3F/LnCmHJjCLu13BkV7CMGefTVgzD4eGe6Mms9fJE+41QqwemNvf0fU3FUEmiACUAlFLF5aoTsjxhMTcIFYXfuTeREgJo6faMxmxA7RHwJFqmDqw+rvIwBf7DXQdkov8ILWmSxDJeMVn818DIfwnudzhtCXh211It7BIM/Vnz7t1ZmOF4z/FhQenQPYCyYGFsy+fuQKn9xK829WRjTwe7JSXyYAPMouuzPB5/jUkxFLvI5T82Ve+F2TTOVsJbfaO3nAq636WN0CJjsbOBnUvE5Iz4hoPxs8uiOKpm8qpzRFVVOfDZyCwk49x6vYRq+CE4BTX9YgpI9kZK6XdNamH8LrsT3y3Mh67dwlLmpnYtxB31o9YHs0v8JPFmb1mi9M4zju9U+AlclwAuTPVdUlc6J6DU9fy2qniRAzFBBl1uuPLVKGZtidbr7BAjsZiMLojYdXpM9Go13FFOMIMWNlBUeY9jpaY/urM7LHiktpk8qk++FXtA8MIAsJAc0uqtj+KPVmzkjCNm6htaW2/R8biIVWzFc516crhNOBcf1w138ivVINDmetbLaaMKDyCB7/BZ3UwLLF6PxUI3GjdErdNygKun+QCeMxSDX44y4kMWqHf3j3CBXxK2z+yk12caZ6npP6UfWdSnPCkbtDSVpH0QThGX5FfHz9RNrZgZAUx2EAeUkpzMD4G3qiisKQtyFPm1dutGnw55zp+dq2kClasy6HJpBcTH7vsPfC1MjGZ65tRclgdJ+25ezjX0XewFlEwnzvKRqDeMy6eWr8a68PINy6Gnft9XjizxcMuThXAl6S9gBHOY1pYWCHIc2/u7lEz0eoUPlpyk0U8h3KgYMnMef1Zx/Hu3rK4/pIzCw03SZqnMC2QHeR1d6mzGw2+0psss68FOJWZ7RNCZPNj7Je1Q9N8QC9rl2z0EMudqUjGUbxXahjR5NduJKX2UNkYkbwhYmIdlLouJ9Z1xR49FdfAx2tQIXpTuWxhcG2GGHIJTDQs1Tl6DJrHHWallDNw9oHo/DlKl/Iaun4VqiC7TQFVlJarhkSGhCMAgc57Q3g/InYmrX3BR6kvJIcolnoPTEGZYdWUapyRKi/y5xx0hluMKwYTARufEWklHE3Xa3ct/2d/51gy6Aru7mP8sen+DLyYHCdNHUTYMRQT9SRpstDUbT/PzwV1Mg8g4sPV7ghFlm3A1h5e3cA3+JKTXQVKLqpHT3XBjAAYaf/tUBAuj4AFczf+yyiqond56ANUlWL7B8A8bVsWN33Z+UPkHWlrEURYxqxFM/oaIaPneDp7VgZ0c4Vy2B+wow12LBzAhaHEFhD28N15mMsG4TlMDWoWTBFFWyEVzmSQ0OYc/HWyrZRjHHhuVYQdI1A/+5s6cesN5kUBjkt5jhbzGx4GtfVqQoiDUtAeKOSBr7ChWOiMqMEDWC88VhRu1PgSh/4dKv4AsBWcjDYtImwwVrHXgzDuAimlyNj275P4Pw8x8kRtUd3EVNQYy8G4iT3cqMH6tsfbymW4p0AQRifjpYH4zX0DTVAR8TvFQiGuWAAAAAAGWVzYpoKwIIAAAAAAAAAAAAAAAAAAAAAAAAAdJI44at8rax5pKNa/rHJwGdCnOubtc9W9IyeVxkAP3I5h7gTxXYNNSSXkgB/ll6pmJHm0zFJaPHD3FlDANHtFgq6Q43zLqCGCl+NHJod25QzEvMjq09GlmAz+9wuFgjvE+x4TV8O60F1Nb2B6Cxe8IWe0R44QHhtekGfJFAaddA9w9naFq1ymAwOk7FyLT0f3AsbwlkAAbibf2vfWI7FLr5qP6LQYLWmZW5o1o9BvT/DFWXfE3O42pjxbYTdEzie/I0KNvjEeL0Xerszf0f8Zppt8nMGF1rhq+RaKiFfQHrNKGXEd53ISP2dGC4vfgTrWH+/YPpJJRNS2O+d77w4WIvAazxsahCoR5xsZcAhAgO9bVOXr+bpBl0rTAB9MS88inz01sHqz+XB2neOWv4LCvc2t+hri5OVcfaWVk7aoegRw+yTQQfiBBaJ9fEhnV80osXNU8xhdCpKyzzgh0SBCH0J182pzyZGM96qdfS+Cu9yd7vY1X9FF08ahNTC012XWj77vGYGJ0GzrYFTMF/xGIKVHft7ttzQXVb01df1/w43CEpNFugtDtbdaccXbJEquz591El0zQV3JGC5x8mA318Ib5G1aCd2UMtBcM0Ssifz8g/uq6eL/0U9483IAz3Bhp6Ox9WQJJDPmurHSWhSA8gp7z8pThvq4rcoWyj7n/lLFi2RhzEBxrDNxhXIKy7eD7b4Z9744O2HNukuMHlW67yEcUvkboPPWj6KV8CjXNNpENXFAfqcC2Q4EEqe4nuiME2JZovXncgORurGn45UPmUPKDzVnhMhyIh/WS4iaFjVU1Jvuyh24U2WoZ0v288awPib/MItiNrd9WYtdff31biDjK7XO6FQDH20M/A0PpgIW1JjhCi/4QgZBAvSpVPfPt2rN8jgDwCwm1OHw+iSVezcuFBg3w008KGKA2u86RKsNsw3s+K+1QFyApZrATD4qDRhOMu9hrcIcRl4llNO42nDRMkDVSLGIjdDwTDBWEzjTsxcUdfx7qK4/XCkzkCH5YgYd+AuFbqrpKBMV/uQw1NWnQkkaGPWDkiNnJRFGoM6bYaEwZr8mnw8VR45HOyYgKQRIPerIzRh+J+XYBFwJiGZTD65og9y2+BrKLLEMvp94KQVUesM8JsaBFlelQZ9RXN6G4gKkD2veYKrB5KDiqt0VmSGKl/5UtriGvHWelTy2BpQwbx1eu8o3Wjs54eNThDVizWQ7K+VAKqgTXpk4KhRZZgFYfQoTTdz4v7FeF1TH/pGuhk0qGp7wji3JFXk5fv3ErYi6kjH/uyJConIdpFDkQy3ZLLfJgspWWOeX1glGuqi1VjsCxXvWlLvFse/9zIEaJxf/Hi1V82QXonJQCDoRaqjWxDHGds8PexaRUTmeflnUBvtYQ+8pEL4qLaXUoEVTDcUdED/B3IzO6qOrOE8n8l5LPf8e5eZ8hC+qb6EDdU/FdnUeQBS1pdIYeqWGWp6DhPWwrSLXGJ4VF8RmPxR655hiKLbORYjwiUFjtPjhgbsN5wBOSVhrDk3+EfhzKtFl5bFBZmJPvpZdHzDpAufEIWL0bat3cAdILtYsOp4lc9uNoWD/thQpKSdR66YjMD7AJVueovNzsUWUiKEoBH7yXq/xtZNfDKOEC8lET44QL7d1dAEcvHIrYwt8EwOmFS6gMgcKjn7PF5gsDW8KcKBa8nRkfXclpJztpAy0NywGwWFxfMFogbO9CigO508O14TPAXzecEegmw5ZbWN4znsmuKsHTKuMyOY3GqLIGnW+mlz0fPaRujakW/qDtKLHsXOQMbwTYosoPtJIwk6iz2brDqW4T6jdFgGXbpFRYe4qkT+TvsiUAdd1ziIT77W2WSCW6MlzF3fkfwBytGX0BUBCFIcGu6BtqFiIM2xkP+87e5eOm0rYJdAidfbjM8abr3Ht+QpzWiiIu4iYp2j50Ms483RQGvCjEfIgqoDm3QxQjmh3o13yfPe3dCvYkkv9V1fm24t1qm/DpxWoSkQTOJM4TCEm5/O35SzvROrVT9S81UeyGZ3JO4annjikNvutH/KmsVtLeBJTTee3zBl1+YUA7z0It0arHvxD1kR4eRHS8OLe8wVdvKk+PeUjuEzNcV4ospJT7+AtY9h0rk5qrcCOQ54RFQDk56fe7pLaGAqPqBlicXxXZmc+Pc4xUdVQwTADhw0q1NlmTkecyTsbZjy7La9Yr1nV7fEKcKiZj84w8HvO/Y8AcPs0ikYRbenSqz0boyGBuzrh0kSvHgvNfCUfitEkryFPRI9Eq8TEGtdhD5Bxk6PPUEl/htbVDZPtZ2p6Y79lMEcZwezFOxafgYk0HIxa1AQEvuzLUzjanPKRQmqhaZPckT1+DJHr0/OzUuwAWZD+WmI0TcvdJiRO6lFN3SH0i9phOdJqPDLFcsoZGbQwGBM/94A8IU3hKBRSIGg9IgGNVSwcIosKi2DmTmeofTLpOATcix5iaYdkVqAbWd+a16MZf3KDr5DU3HNTFl+tFTpBYWSD0vwa6YtQj/UZbAE0GDFo0rnz8SE4gmdIOfhL2b2U8Bf/PK1Z1cnYJMfpp7WhlNDDab6Y0nfJvbEU7URe0SOot3QrQR5fo3sggmmUGjM0ati/xeq+JISDNjSl/537yvsD/kgXuxDLXYJ1SOa4kp3oMIyUPKslY5+lFLmocoMZVw84VUcLO8W4XLYuRfQsLTHDFLCd11Nzk7FXXzusoFLrbZQQh9gs5woM967VhXU8JqpO8Sme5acaUGFKtULUfouqudtc2LgicxtoWm90QjF1ARSI1jlZl8BX/zjBw4Y5BuRH67F5F5oLEiKi04tx9Ch/cRFUMjevl0lOqbHZymyvL5ilBGYgYGdSgBrceSEn3yJJBWcKf2kSPQwFtuNdbCVXpgXyebIEfUdO8W/7f6vQzSHnosCCsAdvOwXvHr2/csSSwXYuy1bz4djZy3VDhmryNy2XgpU0eDcvfvjrizT1lHOZz/91/262/u+Cm/AP25vewnTPzGSDn9Bc0x7vlFTOfgBN6Td5Q13PT84EwTRUkFt0P5AxwS/sYWy7nrQsz4ZYq/8MoY1FHlO7qIO/zGpkUWAcjNoNvvcUFKVPKq1nXGm+5rj99sdMQ6uVeD0htDh8llGf3+otJRrzslGZeloBag/L02EYRhhBFQ2Xl2lqsu62LAVtToboLXEM7uENi76sK5Ab2Ov0X47nOiQP4mpDaYBedJa/grQIk5+fZhJIJHuQv/dFrCrhnG8lznaZV4FgHAKHlFYaPyp/BZE3o77xtPc1KyZ1CMOUdQTOixyg7Yf+AMrnnlI+jWe+3kMPeLeIS2MhImw9lLps0jC4AngucZp46tkkTEXhQAXCStQWowZe3srkZZrRWdaiPQ2U6a7CfLL+gwyjgqwdkWiGwaJscmpX8E1Q2h/5ILAklLhDR178RlzXMlWL177WSqTk3SG2AIiCn9jYY+hLDCp3Ghe/NjijX3dZqYKKx9h39awJIMEkio/Tnk3wMW5iFlX2EAEYLwnAIH/y9rk3ZJDlxa0P+VkSGtjd6443ghHUj5L0xgD+fxjaAUhhItX6AIWI5wTbxCtBelTuuSHpMirEwyxD016+YLhENKkG6a2DW/5T2A+9Af1CJ5V4nhFLNON6wsS7gYS0f9tACgTEkTHZ5QWBNZBVfwfNg/USd3vW/G5MaLUEwCowAACVubsVXqVvL9wfmv1EaUbRfjXrgU+a4baBak8hwKgHf2zpfE9zLJT26hPzxzWcr37qbdg/PAMKNaxVV6oWSZls+4AizHy0Pd5g7j+mGLrwAAAAASij6Ny77EeFeESNWFWhPWbCWfxeRafttWXof9JX8Hy64bDMHsS6q3gXI6G1liIyZh2Td7kAEZXK/oQmG8QuUjz+L2LHYn3n1/AxwcFo+neAXmDfBLL4Y5PDq0DIWxdsRxxpTLwfArN0bV2c5Sbo4c33uqi+whauUNCDIRdUM68evtXV1BBYgTnSmO3SmZh2F0nPqeAnRRQdft/BreXAJT42Zpyys+nzANmtvHuzy+JbOb0A0qyDShzYZxjGkRQZxXTVjqZ6kdaOCKBiYszPQYzE1GJxYoDeyqm8dBLq07/u7seOW9B9DE5Fcn4MQMciSU7QcJ4CsPLkogdegRUceI0Xdri+46PWoTplU4wCErXF3dbKJBDtDxOQtbpQrHHKzfpCggAyFfrkA7RIm+edVPrKQxPZHwLvT9zUFHURpAzxVjef2636oNEPGJInRVjNPlXvUcmbJGQtuotp4ITRCKDJy24Um68i3l6AtMfL3q0+3TKR/RyjdJxPwYtJp1G7orUxy70M7DaxP6G4f7Ai6EXnZnY4ZolywihkSoOOAFiolInJRPIpxjxwXvvGwnY2CNJqZc8EGECrVpzgWLXXqOmasQ4mjx/CeL5OmZn9zGZw0aJSRBnongkCIulWZKi0qYm9WQQpUqobKVqyRmnUpvYp6vGKZ935AiBd8pTmrwR71ESYYf+cUE1e3R2vA1u+Wt265rfSSJmY1cIZm4/jx4xy/QrEQerYnYa+eiHFyEOB6qeToxZAXHIHqWMUPeKaz6KttQt2srsjnzNFVYnBQFgAYZmcBJCqAvFIAAABAGQmPA6doSsBrTcmf9ElgY6HPa+9Jqquaw66SIe4SdElNch10+TZDnSGFfUJz+OyUhUWGFphYBfX6UdINMoBT0t+hREorqbH3vLKpicijY7XjDYasec4Ed/Z9Ed2ona94MIMYH98a69zkOr6p0+WoiGnV7ZRgTIQh3QikAj+Zq1UDx64vDlSTmsPaLM/Gvm1H3q+Qwc4FtBMxc9lIEWUf/t5r5XcKTmY4lY+nm+jGccz2+lXlQwAgXzKDpi3TURimVm3mK0/KjmYFDphUrcPA1UBqqT4Rc7YXQ2x2HveqFhk/nqOcsIT0zFR67ynAiETSEpdRfZNi51rFkCIr6qiLnKlMM+k8P9C/hgSZLW7cKxS2yackqnX4+zUiSBciXsQVF+g7HyqKdUAQ6F/9mIhbCLdhOyQxxpxi5u/JRlpIUVyNQBBK/BsYUd52M6Xzp80oHSnrIky+NnOD0J/BFcatw622OXVSlnt5/NvGySuIpCc0sHGBYfNgSEejErxPxpYR/lMY/rqUrCZMRWDwKzJhbpc/XoHjc4Elm1QJvQ03yl1GgpAXKHxJUzdOgPFFThYmI1zeXdzWgorJluT6NN3Civ/m45P7EV/6rYrbAUOdGktj2Ow5dI6wrMrt45lIjdgh2+rDA8ee0EDn4N1yPbPfYnKaFBuZMZ3xpOAKW5cM+ZhF348t4fYXBDmv3qxTPActKD/1YTASWXS63bCxJQseJLjXSbztTyCn/iBi8IfRXazjY4tgIHEg1abgQUc4ORkxlmZ5vEScrFtzrs2gqxpqWwEWhxiBcZDp/jrKFzVSJlX2nrzAaCLxsWNFYfcliS7CxD/t9QI+tj+jf2m1t0zrM1+yP5DxHXlKNgqwxcY9ztp0KHeLQfr3PFKFc47Tti3HRWmpgnHpHhiUA3U9aWvbLS1yCm65JYm0HNEkHZ3yKF3XO5MuzM7cWYQI0LAkBm3wMrMpIXGeDbDher3sr+V5up2XgAXKvxA9QjMPd2p/BogtEhzCZg8Lkkdv6nRGexrDdmgbeZIWW+lxg9NAAQSPjZ1GUgIzcmETNiGA6v3GPmdOYFXhAJYJbp1AQYqbTMyUDy1DmIXFQdjWVlbESmQHkUh/gCbjw7iDX+ctj5KiyKOcnWrXZB+u5seNRg3gE+P8zZQGZdl8zlA7a0mwJAIsgnjSRBnufhe0pXQ3hjFwfFuKXhAmapt598u3o5XImGMPzlkrRPVXzfvZUVxNf23yrZXSqrme9i5GSpHkaAQxNbiXQ+z3GRLx4UGRmUOXwsVG6Lkn6vr/tJxcZbjwdKOD9OEdiQdPKsdMVpaabA5p9cgHGiWDUUNm8i4kvqOqouPPnRJBTzECXVZjSrFD/W7tA2V4X/iSt6d50NTqgXnsG8E+j3zxTrKhbeb6W65mZo0O49PoQE+Ce8mOu9VjwceCZQvk7+PjnmpozSAaFknWM6T9r0y0kvTw8/uBWqVqb+Auwbh3dC2Rhz1zMBFsMK/OGzMzQNRaGODjIv+bzeHifpm7v8bBiSxcJsi422vYXXRvEKqnQQGrCiPUp3E6pvO7BR9tZ1tD2NkhIWFbKkJyN2+stBM+VHGTfCxpxxxvx7rEQcp7rt5FWrsme4Whuvvy1bpH3UeReZtmX7/I9IQLdqsckNWgt1CYLx6vsQTgydrCFC8gfI3y5CD6IUKL75y/LQPCsQw2eHhpcO8yiZLCqwF3hPeqTAMRLfa69obReut4nyFgg9kraHdRcVNebtzgikITDsUeby5dJ/XRrJvXhRoDmzB9IYrnvDdU8pABM9olWrm/CA4VkhgS2FG8z2P1VfXvZrJytmr571zuO391KCL5qLEDxHirSLOxqhdbIVabXqectQQsA21IdoNi4+2q3ZxO3C4O8fB2yrSvgnY7fCKOqOI7vmggY8htr1lOSnfVRMeT1iD6GJL/SJtzDgELZ1B18CqcsFdF+EPHBmGYrTj5hu6Fy6UG8YGwFIlean7b9LSrGPGMQjE3K5uy66IOCIlJjFW7aKyEfKM/Qi6PN8CxYuybtWcsOaAtLgGh3MJaFxils7wDy7TgAGcu7ac67HS9DzbsmULaXBkBZfaOdHSt+R4ifxga+dNzCCVJeKXMwebSANs9I5BKLu9rwtESMkcG4CI/IRuwx5Igo9yDCublEWjizRPmvsPNLAuCZ9iCXXcxu2tkjKQKjBiQ8Ntt9gzbXI8bQaDPc2ecuFHldvuWEad2K/2qcrxYuP+tjA8KKUcdZOz12JYAeZOSY5y83ixvp4SmMeF8eV0pco2KbURRxlUkV840Pll4iz9Zb6uDuFd2IdDIL5O7oU1MS0wGzw8NCGrpoKWnhoEEW/0oZOBiBUPN9f7Y2gKg5gX3bJx/HeEsY0Vhbt+/WiAvUAAySuSsknbz1kR72+mZrA2SQVHTA4zCJ8RKgm//zfVtSav0cu2MmOT8zVFNUJMCLOsnFFS5uAH3ldzqOQVscfv7tBgVmMqDNLJngZ2p3e8bHbOUh0I3Zi09l0pEF+ohb7hmIarCmO3YxoNn44qYBwGa7TtEluzB7ssKbqeRiRnbD7zmFlCHBnOKPJ5uYdQaeBUKV+qgmNIJa9i1vSUmWhlyVKltWEztNaaypgO55DBj5zlVKgxxAcZXBQOrwCUIduFBJs1WLhGmfjPv4X34/iQjplr4qcgcW7o4iwoEJjcUEHF86sCKuecDdVKWKB48tnLB/ZSj9NwqJKp25CvcO0h+uXnwC/auPmaKSe3XA3BmcZT5U/IU5k87gTBavyeDLB+jJsTfNfzz3WB8mOop+ZKX8lCcAsqJ4aPgXPqB2WGfPl5ItgenY5jYABTEuLsBQ5YxcLMkn6J+EUYY1AzGGn2Kb8tnc2/417S83kbCyFTIp13mEy5Y8YUE+sIhTEfzdLDX1b3XcXCIIVzYxQ7TqskgQLtCXNVx2pE4udTdBz0dBReJrqhio7nJksHCjyQExAkOV8armhKRS+LOas2vtt1uCvb1CTmjwhFPs9M0MZVHTD/rzYziE3a7iy2F9p7CGhFkCBZb1FHcbeI7ZvqM7hoTuh6yIhm3l2Tb2eO4CRVc38QLGDWmbLX3/jwoebrjCMro/s6rpVoFpeONGeNqCLcRSUccNEIUtrNhuGF09+xH5ySICSexFiAMBNBZLCizXnLR/Swmx66/jE27UOUovQYylJqbAe7U0iL3O9gzMN+CRHuJoUV10k3bbgHmJFittkYel3BTOeC3BTOeC3BTOeC3BTOeC3BTOeC6dha2gsgJm4KaC3BZjjlltPdNMMVuRd+m4YwKj2cfKn5w9dRTsFuCI5Uf3QLfIweHl5F1GZx8k5Nlu+kds4Z6GxDMVgxyeGyE7Lnp2B14V3a/BpRLrgRXkS5NQZYPsjev27Q8F8stEBdw9GlKr62Nf7fEpAM4cR1Pnnjk9pQdMwSvbn4B4ysHU0gRmuLDVByFojtXilxbmpY9+iPWAMlFWXfPKynTbivllF3i1+ccqGWNK2tFsTKrPk1jsWV24ihfkAf3pjPze3Ih3Z2W/x5fmO4czaRgkvxrCZff6VRuJ6f63QkU05HvumgnLlMaTjtZlULWuUaXlDY4t1gzGSyOBqUvP7mAq1VAbrJ6U6jpkTEsKrw4If2gMYehekFQ5V6W70QotHTmthhvLjAaauSbBC20dUmAKlW71f+3zBTVZHduIZ3j2zCUBcP2Neobx2z52rJnsegAXQenk2AlwfqSxwJ7ZS7UKGWgsKb/ORYAweTjUOCMoBqdXhO3azUsJrGruHhKSowuPNDHsDxPqEX6K+F+PcShVPSIZSQRJMDCmS8jRRX3xBY7ueDboia2vjxiWNxe0u+RlCTorxqNPTWxqQrvREDtKLkCHPuo0CjtABCe+qb16QFYK6W05NelJuhZndEeMkknPz3TjxZFpMqAdDhF5xm9GIYrIcpZvcYX8AOwIMDxfbzAfxyVmYKrW3f67Scg4NyXf2LNV7FAR57daIg3prWvV+4wQPZPGWDvFrSU5JUly3OTTZ1QEHsCyDHGjMFGIs2sqeTKHG+xa7bRqTtgdNAVMad0gAiUdzY3FxOtyIR1GNaZ4HizxfedDg/gNOIxF+GfoushaF9O2Uu8j1q7cF+op22CKfaGge3Wpsp7FaKjHMqheXjvHfvg9lByQSSajt7lAm82scoo3Y58s7WiovyFmBiyrV4JkoCMJksB8D2kQnX2axvfVAZZq/NRqQZTwj07BVNX/sr968RcXlXatZjVnRTWbjPERXuWcljXOxZC0LRwPgQuO8JQiVzr0llPcD/l547DyqDPEG0lhg1M+x7W9PsXJ/+EjibgrZuiefboMumSwsRo7emoStmh9lZRWwISCVE+o2beNaVD7qutHKxhUcQQHjU5CLseuoqpxqoAHlHFkb2KTrlRxrx0TprcKPheYme/RbC3y1mtnMgzyHK7/57yWAQt680lD4ivx8SceZf2V1gwrLv5bEyibwCQqhYNNX1jE7XashlNTzM2nJiVk6rK7eZabxmsbBqAUywcgqH0PWyu1Nq+97xAsUhViHzISfJv+CGYOURQryVwLYyY58slcCf9ov5JXo+kQAtjji4pEBOWNPlP5gQDJUpJL5jvsNEiyh4pKOtAWZsH4/Db+hK7+V1oVhf62mwVoNibu5Dqgt7hVCOK8eKlmCuM/8eV20RAk6VS1XmEVvYqsADcxuGgWMKBGY1hWQwM7o4p4xeEuek0tfqlQScT7vyNnhxj0ZEqAI13MLFA5HxNzrFzudYudzrFzudKL1mZQ0OVivUqyfy1mJjDDNlDmhsGCMmFMtih1CJTsAQtyFF3jpFk2YWLnbSrowkcxjQz1OflLir4bK8C0QkOnJ5q9F9OgK4p0dAKb0uwleuIbBAqmt4xSkUNwtjqC0iwkxypiF1twiFDUOz2JgJIgpwAba8Ibb/qtJ+V8HKQXMafTVQ+GwzOUCF1hxESvVu6cqVAfDFIHZfE+uNxk/7N42MJFfLT+XFi9O+SYreSK3QYtQ474Ge95C3bTkUPOgVI7H7Cwh8wZKe/KQVdyE0m87C6N/9BDrEuLe6F9HG9rwN6s48wvvUgPlaN083ImmjvCBWfZpCFoPU5ATtCosTeobwj+gTmWH9Ws94EsPk7vYK6zJkcCRJ0wt0+zlZ1l1jgDU7vYXw/yqEkPgzs+HMno+ppXUh0L8NzE7RpjPWTq1Q3+qziIPvOo5nxwvFT7BItWawredLRbmAbi5XYJ58t9HUbfBm9b8bEdDg9qDLK+zbLTzeU3p85X+O5Vtyi5GkqDGSaGNbTGV9xcbVI0HL5+tOIRNOD5EUgXQSkBdaXSlkAs5jUBkgzGiotRcoZ1yLD6LDgS63/5IwxGTd0U0ULQBsI8JvlzS33hKCbEElpq7htK3ochTovTacB9HCHW5DH1JZFPkhpdbJXTPtNfD8eF21aAP3xtjn0zDPPQ7tv7T5txy4IrpG1SjSVDh2mcAmAs+HlTECuF2b74bfmEKHE1hunNG91nzl2Sjuu0XD3aWfIr3MHcAPoCRBHGsxBrEzwLGLwQ7YRacx2ydnWCTZYZlfG/gxQzEXNyaeT9rVPB0SW5qtg5qyyLZfLJFXFgNb2HQqiHAeZiJ0BYAYWX4YK41NNFB1Q8mHN/Jjq5f5C9L45eBdQnivakwzhap+NRxV7TmcDahYUpof2BZ7MoQUNPpL9ON4ozHhCdz08bRW8jxQZ5DVq7QMaeOKL/qURedUmf/uUUPANL0vRgm9p0hfW7eWvYalMntSuVxHafV8i7OdN+g1qez+Bnmd+6NKoKPNZD1XtAR10F6w0O5myoCSYhiYL33IqPTsQyg0JVgKANgin/24IxqzSFmMvn6AuMxtmiXjiD53MFpj0uv7mrCXWkTCxQ9bfQjZM/8PtRSHlEXcoxEK2NrZgYWPXzdNIIesdsXXCnV1FLrvWIYCCNa8WnXVnK891TBFJEgWsj6FS03fvViOaTg4cz7DR4S1hzHPvJeSaLTPYBU7MczAW+8dwnzctFo2wcZPDMjy+jRBx41LPxxct0Gwv4oGnfb3wIZoT8CJGxbhmVlfEBfQvBHT8JInEl2zpHqSHX6djY5aQq4ttKzjf3vVvOlmQIKXZTp8wBy//uI6BqKcC5ayekM6JzgprIqkFEXd2LX8DhqRT2L4itEoEcTFQchnllkwZqMuGWLu4XzED2tSg8LbLgkE6QVqxYlzQRZIDD0hrBD3D6eNaGwRPbF9Hfg9bzZLXc1CNYBJyW7rQhzNO+gAxXKuVaoE0FEDM7ZgF5t2Wxz8mYKhMJmRgKEGtrdSBNzRR+Zq490CN0VKTxCcqIU2Jw7U8coKSjIkJIAAgRQAAAAAARqB3AxAAamCmVnGgWABKku41lQYVEOIPgmLWQ/crPBlm/vr9L9R36H/Q64hVlx/U5cfUPudF1FFspMtDhjZZiMSQz2vGE4qfNrSmUgAAAAAKVDgu7LAs3aUxuodWExdYO3AtueK2JvfCr2XEuXpgNAOdn+kLVk+zmpnO3s6IicBE/xQxw5nZrvvCgQ8zyBdBNWb4FBLer9pN1dJJwpXCAeV4cR71SqSWvv6E2xIG0m+iKrrMw6461KE/ZmyTR8PfTc+EHr+AGetopQHZoLguOBCa5YNSAahNzHBMUKZMZ1OmkzEIAA2L4zVeJPtHkxu+9MQDBg0SCoEUrqwWUU4N0OvaxrkygHY4WVjQulAKDcU7YYOo2IrN9FQtowxJWjy0lW4u/OHWt5xABfY/He1s2nDZwsfxpv+64zIVDJPJXd6i3bcugsjHPr81oDpKcHgavctRPbR38y+qhsUITS+jC6xq4J5VMZW5VP+diR8D/EKne9G8vdUNeK9qZfDpgn+Hg2yftTT6ulFa+pb8P9mIv15Xsfas12Bl5YOj6Py3EkEILZrXyZVqGL2afxqzhHRhRV9S5QZKAUh9modeRd0StA3PcvxxFkdiJ1HByUSzAvdGLx/ost8dcNXntxiRaEZDm5buLv9H0TicmqyFfMpYVCfnbRnyeAL5oUuk/e1xCeqVcAS9bGr9b7y7yxf5q2wph92B9E8oknPqKNV16wXRzVetblx3YC7fSNEornZ+uz1jHrbQtA3GkfcTWMnsaUpQtQXc8S7PfmkAd4Z8WnviBSEcOKDSe6oFL+RFhonuiVmxOTOxocHQGTH/5AlSkH0GFYbBdQmUsVHBLbYV9xo/uObMTdh1G0GexE+gDs0Wh96z0nYqixxMmZeEKkQHaz91FgaWIuwMho/W8CjwsPcC95OCMBaiDszUioIFiPhwkTycqEETC4k37HSMrW+xDjOB4ijqYLylr0wbM20GRr5Cq9yGdNN+epFXZ923l1GbUOIZuTadgpv0iAO11LaSniaEh+Hm6ESOzRQ6cK9HGEFwjrrhxMf0JmaJ0ktft/pkNunPMFuRr64BJeDhF+rTdieYQdmobH6HbwOVJbPoh4vVSsZsdRPUWC/hST5LH7ops6Lq2abggy/zo9tiv+ihp9Ku1jXmyIKCv4pzBnBzGvdVKdZ2vXS9NGlExogeFIpWnDuVwgExqvjxNJ1L7gC9LJRf4axqLe2JFXk56eTtYvDphkacXDCelIANFckWgD7MmfonlgG69M7vn2jI6VSPAjRjUey12zapyD352wyRmEjzfc0gi48Fc/QfEvSSGIid7LZGAmNmb2gBKRrsoz5NgLBrE62SVR7TfWu9lHgTP+7CSPCIDKhA/IbkwUwufimlRMoGKrDRY3n59qZ7Yeybacga0YgZ91xFNNswB89b9uvxp470cqf5GDFci5QEzbQWQw0j1W5GDnDjVGRlpteRg6zbIwB8sHyp/kX5bgpMFb2toLp16sLvkYPEMTXkXEAkS7Igu+SBtPiKMvJWJnwUk6goheU4W9etncDhZurWk++p+CQ/dcuxPmgHBpghhpWkRmCkaqgeiRJxDtq1p6tM68CS9Tg/nipP1yu5EmzvlIT2UO/6NHmv8hWKuuVn37z4sHbqZ0WkIWf7qxVvb6agHx0WXgFvR8PFzVWcrbK3UYinVeGA0Evr2eJsRn+1sqkBLohVKoHCBGIC6V71zvwAszKsCpTU9cUHArmzHR82nYXq5a/qi9gXfWOjJy8xbo18v9rabMKNv3V2+7woWHdQOjA67p8GxvTeE6JM+G3xUe9yO+BaHJEySodvgMSb1oR63I5f5+YNQujdiLmg1IvHO4FsxmzSWEKn+qcr2q25xAwoja6OQPKU81XzsPw/1HKLRxX833GOJtdvnUDPd+YQOM6g6rir91zGVbVs37Uumm4Ecc6vIoSsk3xu4Il6kcEz4qDIni0tEdYVYi/OeALpJcZ3X9tBw2rkFZQAY0UEXRsvA6HW5kb6eLYl37WoUyi0EJo8e/242eLrjamExmZUif4HW6mPCEByTIuw+YpqsIRun0gLjM1/kLp7O77ZcQpxeC/Ur/faaJKdOATL2i4FBbvM/UOiKHH35Er00xNycpX17mAUhYYaQEEw/VQFvzI2lYLJMCJIznHWfFA5W1ggAB6GRR6COK10dKTrCMAAAAAD7sK3AhQhBMShtSwni35/XkAE9b53nck6rkRuB2zxDb1myREfCtmQHle6xbVoblV0//wipyyL6DXA1rcDzl38q56ZuUsl4J82CJaH79tCxUWRr9uHRwaR0db1DmZhUh9k94DXjoHcg1ktwtD4mH3fKnaqiVkEqgu08X92xdTiV1JyHfVSntfgJ3QMRKSilCgw5rY156MWvIm8FEf0BP19kmOR3h3J6L2Y1bEWcNdHnaut7iotFn3nv4egjNXYS8OFc55xqLQu3fqZkqAAJ11Xs/cqq5QbEcxh+YCLH7R5/zFOQ7dz8/eQQmTSPQd2SsoHjd6SmJU1yFzlH3Bk1vD/Sw9BqprBtk/xc39W9hDxCboJqa94XhXnv8/HpCk+RLeZR274hdfi+fWFZMfasEYmuxuzN14tGZzzv0sMbnn7IgM2t1sNeQEHK6QCmJF1FzZD4xZ5oU+Jv2C3mLgsY2ifSzTj/OCm1liFgv5jiqt+kjqgb2cx+esFqOXWJueOT/F34/wMEoMa1BSp20p/hflWapvC5N1G+2BbLiyKM5lg/CdKuPCLOyQB2E5LNNZ0oHJcWTf0Ijf4UgDKGBelpbZTqc0gIOEpF2ocArev8+XE8kSEJb8ns2tnYK33M0QYBF+Cb4W2sHY31Qwf3nRBVpdCxpBVT+NXgUwn1dIHEjom5B95u2xQDNESBLmRVl727WfB2MJJkZPOeZFKdUvZP11K0ZaQSdedNX5TnhpWaHCDTjP7nikRQBAjUeBGnUhAQ1h8F6LXft5yqhfn428FZemeWM97B8XsXh9IT4Ptdi49DhokUNgpbxIeLaFnf+PHXkG+ngfVsRuq4LWpx7S9RhpDAzTGoodPnlnKmY20x6LfaydpnODtCnxqMH5sAFgeHhQkVtDCfnlFpnrVN6gn9QoP4gAAAGkVOz/S9LkJm3ucj2OEe5Z1VcFd5ZXtZq+qjJpCXv2LDd5eN8wjsvCW/1tRC3BSP+i6LCaZc5Ku31wh3Us6DCH9dhPF5QdDMiwSQq7DYAUqQe0AtLLaIY2hJiNP9qmpzZAyOxzlcbsll6wNasa1gdkENCHBkx5SYyksIo7roNFBneSAC2gBziJyosLwSpm/KS4CiLxLQJbH/mJ821ukhewnq5BSgmZmid/jbl25fvIAnRsYEEcS26Ki9QEgpHNcghL1UkjXmpJ6VLW1xVUrcor4jWI5WY/blnKB+hFjw6vvw31GjwTSFSNpjNfsGgL8Zd82QKAxA+Ltz9+Bn2WJJshG5CTXqPqVbu35bxcPQT4nJsOU8+obSDyER+9GJNrnUGXGfsje+pyepII3UDTE6Vj5zPoEdUsbX5jLp8EXWbruMOSzkc+XNfaEe2FxqeHwhn6UqHAtaBQyqg/pL7M+2/L8rrZgqAqodi1l3d4lL0FL1zeU1VTYR5AbEx0A/VEAp/94//UMqvAkeBs3MZCWKnR9/0S5P9GcJo5eiGdYH9nxhlq3OTK5vtid0Vvp6mWFmEm3bc7Nl2KhFHxz1AkQwPLKEXgACWND9r0MxqHY2s7RtultO2r9FGxcto7INNSOMT+zTlBCWc+MEUtXjE4FEUICPkbIaIFSLeRnqDF9PlThj3YI8qPSU//w9hrpSSJDu3TlRNGHwQ5/MRNFz5/UMWc6UMLt96fVNgzY2M48SJrF2p7BjcARWXSGqfks/2wK1Fq/6YurOEz9WW9nmposIoILvDaM1yEQdX2s3yVKSmEllEPVl6Tda+DjDPWpFEfLMjfePj2b6fL9u7XJygQ8YP3osmaYWM5ywtQXWj1Pi9IsEbKR0P7GUu44f0HvAbGhdZ7NP+YBLAGSdSSB0qBFxexU4PRJJB3vxL5ywF8ddg2oEBR4pki/GhthUpyzV6s74WhykOipGp1xvMy83eDEJf0HEbIXH3ssadpZ/Hgy5NYustk2y0Ewz+1mJQGoUY5kTXl4H01xe+tkEUW/zKLVwEcEAVFRPXsAZQuHis2ApQDRJ2p+Ia5YGcal7EOe/hn54m39Gt2XHUIjRJaWrP1T/v9vu7qUKIUwuk7VAed9uQ1pFXMV8s1WaSz3nYC00YkYqDFLfOBZCLRNejJEvnOG2gp8WSOhGAocESD4dQPlw2iEp27diL6jcW3qmVVhz3nGuZvvUO4PSRpmjYntuW/Vcla2aeNGORabTtZsGCZnmG0U9vL3eP6HOUP2pldPNLl89V8S1mU9SvPJ/w3VlVBbwvpmTN0L/F9Rqv8K+FycrOKZXqEIaAjHbdeLw+7sHrA9QZf1/mUV680uyP2OeYFIgWQVmNzT2VNafsWI6etdDAWxgPyMNasMmYfE9fE/Lij+ZoEgBSVGJJ3vk0pfF4qpmLDTp7cB0gHy/KAfMlgGTXi8U76AK39KxtnCFDq9FLybliESF2L01Pj/UVNc6R6+ZPKnYXxYjVdwxnwyz834U44zpGQEG8TNEbSa8u0RcmAkkKcAYdrmyRodafTRbGescalsminnS79R/a+Vu1ev1cVZb0fM03gRm+WiHZMlW2EBYn/PagA0hQAAAAHmFzOKY4Nelxg2GcEF0SJzf0G2ePqm0cVFCH+ie67x/RZ4QIcMSAaVKQ/cqoFUUFr8F+S0Yc/HNzjo/hrK681xOHZ5j2hzrx/8BIDTynz6bbWecNDwkSxXIjFVWQCAJKRc8M9d4Afx9ogUgvSoenrhW1xqmfkvGCbcQh5iDBnfupPzFloCa3oAAABeCinQI51/r+Z5uNMTULS0Nfq+1zA3Goh4lRcsAAdZYDkgf+IAAACHrKECSN3AJkJQcq9ejHl1RIVfS2t3air6W1u7UVfS2t3air6W1u7UVfS2pVDgPLNejrnH3vKNTyWR33ZahoKvMZDJwsIwrWYueOVo/thNv0gNu/YBy0amlkwKrK0I3aL35i9xoVGDiY3B5XK+DL1pGJF0H/BZ4KmiR3waH/yxHw6YvDCeKus45n1CWwSvPPZdaWaUMGP+jZsnX+6dQzsd4++5n1nGjr0uyMaYE2C5AA1BEtZn/AbQblEIemKhXPfX6QIHBEL9eSCxK1/kx70fC2Nv9+uWwoP2Ct8Aob+zWP8yTDTuP8k4sLVoN89IaMuxQhF56V2FpIwstIo9Ynfz5RnhMicpdG5a1doSnylq1bZmQOV9septjXb1Z3xy6Zppdl8fa5y30lWEiZm3HmazYMaCp5YczWE9W4LWn7quz+hU3aRaYR40PH4n1TU3RZkg76VGeYB16Eg8Bj41PqiUyqNpkHkA+GwQqmu0UjHM1RCmbKPdXMzL5kLk7NtctlkqgIJQxEMuSl3nqV1RSpyzqoHiZhGT7nj7H9GT2VWQAAABtcLXSgCetaaujWy+WfPGpMc7jmeBRxD+7InthmQKcZdYow6S/IAVrGk0uv+R/+CLXqBBwlGpSovW91Im6edHexJEruTN086O9iSJXcmbp50d7EkSu5M3TzozvJNT/c7LQQ6zTCS5+PqeRIfDn/6Y40xYvdxPlgjCyCCp7vKkrSFIT3cNDW/tAPXLFwIlGy5ISGb6AKuH59rFJYZdkMs/nqBYInN4iMuhy6D5iKvqwBAIoTUsMyFo7sMJ6czv8Sz8uyRXpfko7Mwc1SDueAlhp/QTD6L5wKKTSLT2UqftiKCU5pgT+vMm8y2srVrMzvzAAAqIBNpbrYCbXsaTLt3ksk9JjUL5dWSIMrIFFvMFhMuYiuZXcj4P2sY0MuMe048/1pjqGHbgdo7f1U9+nSwvFqUUlvgOFAPIFS9DLiuGu7JAI71J7neyCF8SL+Y7BB8Bm0Pf2QZjdpX1QPuiZ2jvR45Zvpmv1XSIJ/jutbA+OcwiQGvQaoGem9ef2r+l/lnMuZW6IrbXwA7r7xFl1cFe0XVCufS7L8yGdpW2XY55TEEdZM9XALntE6Hm/jSOuXanSkgO/qN8ZlMg/3tMUvH3uT20+aNB8Ad3hCcQrlcAuFnxF0Zb1g0hiOyUABvgWZ/yxJI7cmErCFhnZ+QYFRcCGlaFElIayVy6ClieitSF75z5OuxrHwy6nai1dfHBGHsahAwh9DQohqnXmWKQa8xnq58dRryO42EzTnDQkBeVXlS8IQiaW5IOP3PM2FIFrhusutseh6wvEQcLNOxU/kunY6FvyVDJ/a/GvPyxDfu2FTXW4bRx4wMGE6kXodNyu0Hwy/ZK4kg26u4mfiqwagD6VKPWY8b/v+UuQLp9nJxfTqGhu091QZIyBqFnNwOaSQcyX/dS6Hx8NPJXUkYDIFZpEA+5QG+tTDS0EJQcPTfBXI1a91al2SBi0izNx+8GBYGiqWb8f/qVD3bvhcpAAABcp4x1BzhlCY683hE0/rx5dby5/UYK8gyQEPl198DTlUm1v/h/XXkhnZMuqw8+xxy0+n+kpDcarTjWzxghrc45Qo2DqeCwIe5Cw3tHNQSiM0fBjE77v+Is3u0imjOUAvVD+BMs7v1Q/gTLO79UP4Eyzu/VD+BMs7v1Q/gTLO79UP4Eyzu/VD7bxaEe2b9uu2g2jN+nt8pBjLmx16Q189kBaZ7VMgfCXUgAAAPYwi7NJpZU5lqEPETZc7E78m6MhAmFShSQiWpcS6nnwJGE+wu+OyIAamZJ0jJzJOkZOZJ0jJzvBQAAAALYp+B4lNXdRAHCgLIp+B4lNXdRAHCgLIp+B4lNXdRAHCgLIp+B4lNXdRAHCgLIp+B4lNXdRAHCgLIp+B4lNXdRAHCgLIp+B4lNXdRAHCgPxBiTF5cDOJmNnWDx8Mml51SmxIWa0pvTTagM43uq2x3gogpsoEDmJFQQe0ySk58k/tHUoxuhlv/CHfi//I+/NVSiTHlDwfgLfRp9zg280x0xMTExMTExN6bqgjyZfDFFHdKkIb9ulKtriUd5vvBfzjNbsOF1wc/wAAAKFLuWbHRV47du5h6mF47du5h6mF47du5h6mF47du5h6mFyOeTQG0FKR55GAAAA6RdPu0Sb8+7RJvz7tEm/Pu0Sb8+7E2rkSwKAAAAGmh1p/qh/Aodaf6ofwKHWn+qH8Ch1p/qh/Aodaf6ofU7hQAAAADbfKJgcD5RMDgfHiKAAAAB1q/wPEpq7qIA4UBZFPwPEpq7qIA4UBZFPwPEpq7qIA4UBZFPwPEpq7qIA4UBZFPwPEpq7qIA4UAS4/HwuT37snXT22y3jN3iJfw7dkrFrBaDbwv1SzGjgqz/DqJAzLIFF7hMY16wZnA0dR8F4DmbHc2ZQtzfngu3/YztSS55pvOSWjZFXWIyf5hO3ZUl2AW59GTDjwbsMYGLrUHW3EuxGE8XbuADbgzj7v++dTm1B/8VQa6eUYGVzkGQjGt8vjCNgvRp61BKlremqiCUpG16W7V42NlR5sVduRYDHNQR40vS4LMfqVvty+mA82WF/gmf6suLe8Zc8mPLbTZVX4kMqYpn71zApN3VvvqxXqBYi5Y6A3qFIIH6a17oUxs+IXeNdC7waEW6eXhxDPA86uRRvlGh19JFinjdYzwa2nCcj04jogR49zE2uvyw50R/Lg/f6zA2lYFGFes5AwSkaCs5efTt2q+Zt+zUfNWEbOLkmp3d/3flog8NZqpqiB96DdDWqpsyp+apFv0CCt3oLkyabZ0pW7rvZo8YLfYKECkxUSQ2kx9cS44k+YZvNHwEhxx9C5uKo9z8bJIu3GC71qvnfSwV4SQL6GxdVSuQ92ucnhv8nlZBIFMvv0DhvLrRqHnx/c4qYLXmINoqVbIPEemk3B/6F3UllV/NTTTaUOlBErnsABKrIdQSU8uB0mygqVZMe2k1GnAMcFgeT1gQq/BXk6AAAAhibhHYThuruDZ4aE02H3d/TMFydjJ9BYpQ5mp3/vbUu1SFOEQeke2qvUcAsrnUvhLikAlGhUmXdu3jmtJvqJdO+7czMwiG1cqsvpWh4TtNnHeTmi/tX3Y+9iJcq9MAR6X4bQhiYMByhYQqm9uEn0TLh/oxj6LAvfpgOD7z0/GDyuuvLuMTt1M13ENO4NzJzeUnYIUIeUclxvFKX4RE7hUzdazDCtJ4Aqo2OBaeIslklNGNBNFjbDLDHSEUqiNFsqhnXEzrN7qcU/zsLOR0yQu7EUeiQu4J8k29GU26DeNFy9uxqw1h63gy4ka/DxxSGWM3yalaS3HtsV0qra+ehGdGe8BAF++GP9kBS9l5ItFh+D5qiOKyacygU8LsGzDuQt5t8mUZZOzD2XGkdP1xJwz3z0IOj4camBgguaf5lsxYAel+WIGY0tQejI2hcgYEVt/iLbtJkt4aANvSGx+RUleapTEStBZ37JgK9VHggn6Eimwl8ktVyqTH2OSgNr1KtDL1XgzV3pm6VYcPpbiV7gUUm+zkR8U+TCm6AaRveoJb6Q40ZTC7O+M7zADpBuzc1Reacerw9T0fsnadxNfItqP79efkaqmAGKgsAzn2QOAUKPgxl5ikZsMgHAZ0GmMQrcXL5/XgLbbY8p6WmTRZpzt4UIYQRv7we6KMuZpIPQ5pd8LZ+8QSKdngxTsDdMpr7EbIEkTraW/YVYBCQvs8HhFeyAJN7uEyiOgpVBQrjr8STzQhPNaWXm0gpW/2JwaSvxXoOVoJQHmzHDpzsvIfVl9DibIIYMErCPjs/qyHpNTUadADKkqHSwaOhVf6uXs9Yb0m2SwElUYuJd9zQcBmMcM3Ox9d40p6d1sGcAwyw6wC4vbfoJHYFL0O1a7QPwhNVgNjpA8v1R+1nGoI1yMkiVswKXJ3yf6w1lyZ0h5zpBiplYGT/fWfaCc8+BBUVelQO83e3uEdSbeqkyYGcN04SqvGXS1GQIG8OLONS5j62tDZeOGKQickPaBoLwW6FafoqUmUXZv7E4ov8OaF5BcipTqlj6+qRMtsbA4DpUmjI7AVnCe7LERHWbknPSJUvYLXoAZ0Tnk2iAWI/S4Lxyp4haNgL2m83I6wHTqBkxPcsjqPEc+80wrrro7O7R7HRpwfrAwr+Vl9awEM1Yn+SO3E8Xo1Iovc7CqA3mX/nQHTuqz817pwOg5gZKecBrEifKrr6Svb37H5u1wsRCRxaEgf64s+678egRimeV9hUvVA5WxK2v1wQAwmiQE/Yh63Cx2mQH3tslO3CEhDeWLvo8x4yWBJ0L8iDiicPhMEee8OV2xtcPfT66ckDUiI2O3wpAyZGUVVrjZxyn2BcuZ+fhpaWT6es6LjqXWZxQLD/cmKu0H/swg0pUFE19gzv7JTQz7SOB0xBp1YMrBbHmKTc/SrkE7hn5Nbd6pyOnm7aHUt1HIzRPpBgFMiNQ9kWP7BT3izjZSOS4Txdae/Mr9sp90U8Uw7McsHDUgkMPJnz1e0A3kNE7ln97eWKAwXd3oBPrA6EMoR7/V2Wc6nos+3fkZUoNxfChMIqPJGhHUqLmwoODmZj5gcIoguW52tSE7sDtIb6+AZTqNdJEnDXdhTeYAohIH8btjGjAaR1mKjRBhfYoLhTO54S/8qu20Q/ZF86SZ9/pmPaJHzqQUImzDzmDCkUOdFV5FBy6Xw+Z4PrOWwneEBrBGWznVwrPbGoLW6LdMoQXvNLOwujngPyi4uZUbsVWmNkmocBb7lFI8x6dZ6s4IEVDsKojL+LgqZ6OsMBemai2iWzmuK501KQGRDotB7s7AcStecmH4vwT6vkyB+UdrKmiqlR2YIQepZ44iAkV10u0Idw/cguGwaiAvtZCxx5vEMNkIsVl4T0o4tD1waxg/fwOMkoFmaEdG9KCJ+dxAQAe+97rcdw3S+Fq6Iq8xtYx+1ncm/yhwymfugnBy5VGTn24bV3CkOZQSKwdKg95JKAKLOX0GJx/GvDCp1z2I0tdfP/ktR3LHLRYQu69LDOtYJsxDxuFtrdLw9ZlVc4R8VZtgLR13R1riR+vri9LHk60XVgQZ0zbQsr2esF0OfzFl5F9Y60DFd3QDTLg1zLfEH2L8lV/pXNG62gs2xUUY+GiJ4srUEZC46BIF+WJOwX9FW/XJd5ic8kYB/nu35ZHegqmwG4TGAA4Xiq366URhP2Ja7PA4u7sfwtfb7QSTSMJhg9wGgULq/2BWgus4P4qH/VdgJfpOL3SdQkaS+RsEMNE+WRwFyorUHnPV/ip4xq+Ytljr5c5ObsyRvtge1fcZcVg9EBRZ1k5GoCZ4C3sszbCMrSQ9D+kWi/2MUgAMOhm7E+EyvqDxDrSvmCuYQryczQG1tyt92Gxg/34jBE/yQg3IK+cYeNdcm/rwaJ+9XhlWs0PBncFV1WJI6+hyVA/VXIcVuyekqrgAhgDEWFavOXUXs3hHycM/4c+ZbuCi2bvenI1ksZwgy98WqbjAZgfqi9EOG8Ib1g2fgYheUUcYgiPiV/18zgQF0ECsNIqj+uXqWR1GLImjjgnLRNeiMY/uSlDEdm7e8i0jKgTn4RSKsIfDLneG/EKeHna62rk3x7ubgqWi+SiFIBGzDiW4XiyP9WanCKWVq7OgXkSReh+95B76DTvVGURNiRJH8fmG/+fmbxMgw5jzm7/hZfjDWfc1ZQtD/saAoR/wDX02oSHvitcoEjwPhsjX1F/by/vjnbNrWjACyM553gTLnueeB+P4CDtnsYRkTMhtuBJRdctJ1Z0mTMiq6hmShZ86d4iL33uPqXWBR0tH2K+b7TdDUK+IkYM4ry4slX6okH1l9iHuQXB5wwS6WJlPMq7lzmYvhlLXTe/rUPpRATLrP4ukVcCpm9BtZ2Ts0GzEDI9c8g8iRzj5ch8MGTdrNOdbdvlUp7CFM//6V9OZhpfUD2/jdlqWQ0+KxhFVJpX/8KKIRzQuZQv3X+cu8wsuWKyTe48ZIshW5J549yAdS3wfa9DiZniFDRefBqCejpt3twVFZzsgqLbMZN9CEKAhlQKAst2D0jLcWuhHwhloyvLDYI7TQc7KTUqm4yDzrncSdYd0DjHmf+miarCPMdkShI8FqYllLNkPf/3lKQH3iqtujd40aNiHkwMXGDdCxTFIQRGdHrJAoQplEr5PNvwLiXJ2TahdxJrbKlV28pxyJP8C2848LB7V/S+VgHQWZe2ebkPfr6mnvSgBR9SjMKeQXnbM4JV9KzfZRX9iVnlXo9TpJceiqk2Z7aGu5PZg8HS2v0RfvbpTzLfQ354I+vzeqjmOVFfQJOuYhhcYB/wgdkiatGnbEgUiZEKDz+tzxKzJnLYybWeBZUSjoycclWsTnW1hxk+yDhyycAIx1NF40Xtc6I8WtaqJSrmWp7wDhmT0dCV0aIyTKfr5Ue3gfvk51RtEoDJO5lSaXb6cZpG17dn09mKyCniQYxqCTzP9owX99j6K+Om5Y2M8E6bjYS9rO3jfcoknh2vbR0n4fUa49zC8zAwSslTJMAQveM+oorF9R3MpYJyIS5Il2ATYj5hSzEs3UtRe3Sm4VMYczAtanC4fdMA5Zlu3aRBJX0sakyrkdCnSiSX67eVqNANe0e+6PpNG+aipmblsp64ASIIsDNRP8Ac44OHWQPMYF/rA3IuorMf1HH0jEvqzvR/CA2As08npMd+eIaRmKVlNmbAZ5uMBEwmOaL6/+yn/iTpdNLosadwjPG3uG9Zdb1s4qmOnenU905OmKDpjd88S/X/k9Nk4MWWiUIupoa4m4MToHDJ0AAAEea+fWAQPcdgJ8ihSAZqFWuiAIfiqgo1WRpB+tHSSAgnHAA/0kT7U6lw3+pQCdWYjcHOvrdyThtW0bXR55mkAZXdGx4WTYSLdWVu2XnEXkAu5qTiPg0s6uQp/nfpBJchRkrrjL1wu020ewRdzuhUfNsuTa3fWEIdd3dWyupWO1IkrdEpRjHScoISkSoqbJc0nP92YR0H3IFtv/MLQRy8Romdrg2dPw34krcwRfpJGcoNeJ0W0U99aNDm1o6xmjtLlAYFQE9O0tvI1DjJlXHruvc1PZX97QzxFwKeG7HfMya+ZLIc2b2DSW8bbOQ2kgRo8pNLNLkWXLDtlIGRcSlsEk0Zh8glaJaZylAZ8yBkMKI/qGnKkCKpkwyX0IFnRXvH6o8PwTyDp54Da5FQyh06ROVInVK5DCbvnDccr/jetqftem0JcTgMKyq6s3LVV3IFcA0K8LYphz3e4TXvcWdGUgFm6BBNxc+sNAj/kmrC3B+OyRTqVcSr2rBsg9vfRgzwCEICZo0A9ESKaUNrPfj2j0ciHK6fhyXtFi0UCJg0cv9AUI0FSgYtLLXTW+7jG4qRl+J2p3aoEliHZ5iGg8c+jwqsq7Fj0e5eYZMOgrHWHDeLw3ggwLq4rlrRK8uOqDnLmoEj8OvOf1aN4NWqgWsNxh5O8BNOboG+DD5qF8ZfpP7ODUO7C/2jUw7EBoWkZKKp1gZnL9X78AHUZLs3EXB5jiccRu9NoSwprdi2Z5OWaqf5jMulCX5dZzCFLPUvWvMHW7IMt3O9jLX/3nbz4UDPjeqf/10rUk/eoviJzGJ9RecZYTW4VOqMtcepqJ8whBnjdikEP2ot0rB7k1mqIoUapd6Ja/k0VeNvb9S2Pd1Kwc58A2qZ3bCQkv6RkxN2ufNVwTewFbHYH1dYsb+OHXM7GSr/HxX2jIdWLa7m+yir7hkXgnNnIKJ/y076s5/Urlgi+hhv6s6pEAFIvxlcIjaBxFrzy4yeRBXz0BbtffxPdHkZDGYJVes2UxSsERJX5TkcWLeom2tGZJz7VCY/sSJWD4zyEl+XY5X5s00DjGg3ajweSCR7pxorVMbUUgfzqt3nGQ5Mh8C2m4dxbJQZn8V3dafKUGzMC9EIAJhFD/cxEyeTsV3foYJ5yxWKrVmi4hNPSy8TSzJb0a+4jmgy6OkuqohyxtSOXMM7HvtR/guJuOOrJ4JGDB/oyOApvhgLWadhtS1nVB26yQW7tFZLfYAm0Anbn6W1qQvRmIyGYrSXqX8Cjl/yx1T7J/sDgK/qLygysW+PJOr53oSMB2r6c1DglfAOQVIgfTJ4Yra0QAImjI1P84lve230KMv60+/LzO/JnZUax2cjDN5W5SecalbdUsPJxoJ20E3rL73dkzodhAZE+WXkvLiP+juSmRD7iXIHKgwydlH5dMKTUxCXmaci9XlvLzg14rPQJ//CamMfIAmtnUOCbq60TPjE7B0JA6w0lqafLiUQtUmNjso9gKVbCbmK0aXBdDgdpa6L0O3MWXM6xrD5V86PJQsI2fmXc/W2krbIxaJxHn8PoaEosRomqvAcoMFO0jd2oytFx/TBZvNkh61+DkWMh3bT+7jaALwwM8PwjE9SQigBtSurHAJxeWXGVkg7WcyHFfKluaS3I8ydCKpcFU9fyhA1jNw7JFkeW6e5EoqvfNV6FkpLPemhnzfSM/6/IrIhso5Mh5WJM95MavCj6fZ32LRFhD9zTkLJk7argRhEWvC6ZVXzpPDCI/LwtU4XplWpRJ8mL8EnBeZJV1WBQbhlmcac5tVZA/sBNrvkqAQ/OVv7yDhh+QsNBmZUDskCXqglS0e3+JQ4SQf9YX6CZp6m12IKXdqHH/48ALJtsbt8NxMKNrpfWnAzAeDQecQU+an3rt13AGE2AeBQGT5Js5PcrF+SqvbsnEyKzI9iqZpPrdFyy4I/YsikG/W6KNUyOcmYELsZUSDg/6z13gxQR8+e2p1iTbiPmxrc8/6yHIW+PC3gUtcJPl/i+Hl9/BC0TBM88bHUUmX2tqNRVSBgWmE3k0m4UvsyaMesrMyzHtaemiNhq1yhwQAFy5BBi34cwEeKfAYOxX/21Lv+R7DTbqLgQFfDMhNOCzouhULmh8np6+mLNqia7PkPbHHSo4NmdIS6X/T1hRtmSAm8wQlJwbrsyEZhH9uxcVYodsg0okOXyU5X3LP+d8X4Q+YOqacXlHelqq9IsRohPZ7CkpiLa2XFJZMOHi8Gik1AVeaYWLjuAMsYVaBaWP1qkMfKoghfbyQILnPZnEvz6B1Kq2cSsrHUK/X/oGw///vXI33YsjKstZMxyIOUtsuJJIoAuFrJYDKAgHmgd4KHYwoo34sLLKiRIIrE43wkWc43R6civOFiV82jN4AYeuVi3+b/5buouDlq1IHCfwvOWakMkXx7iD2D6T6SvjgZf254rXQFIfhiqUNj9SzIUPD+x0gL8rV1K6s5k3DcUhW/Pr/sgjND34rtd2/HNLu9AGGwaDFzo2J51YtPXwv0qOQQ0LiF3EeKw93UKE+6pX4luQ6qvSKclAQhmN55Zv5Oy+SVZ57Ibpc310zFP3X2hV1lFh/IJlyHuSr6L1DFMiYhl2cCOHTNASgIBjic2p13mxxHdog5wIaRYqONm4T3OP+iQuxDQ3UYBChb/tW++N8x3o5QSvyeGJmJRwtp9I7coKKblxqefsarj2bF4H8JjO2WrWGxBKza9bq/NpeYbzrf/slZ5pCz62Iz55AqZZZsDtnGV6Dlq1Yo5oqGTE3e+hdjO0oMObwqM7zNRxDS417o0RfWzy7OP+76FNZW5MBVCgOpsfemvS/68JQc4r3e0LXb1Kwc+sXeTngjWgmhiYT+kDy9+PAgLs7gdm1DjnZv37lpUSqk8MEErQZnb7esVYFl7ZuAH1YQEMGFL6IiBe+J3DCZdJhFLxRFv446zjO7zdSrtzRjP3X8pWMlFqOI1CkaTk6Eo/B0yjjaM0u1ExFXLaMRjidfT8Pcq1SsKSQ3vRw93mcRCbViK4nBnKbgoWwAkSNyZLdc+i96+pcHrJ4KoPbtaRiFHjonOkfZ+6gU3Boz4CJAPmzPYImv2v2V+BPwvmh3Obm5rbQgQY/QNmwWAe+w9+YyTCY4DzcrFY70HsiWVeei+dH9RMWQDCpKJCymCX6GWphchfP4mUR0SdUMRuA+O7tO8BnqvnNgPhqMSkfdBDljnvzPiDQ+xBGdl4ZZ6jISMy5XUn2nYCrS/GnBsrxKPiu9fjqisJkqLmKBh1n4SgmitxQI8rYiBqsHfQe4gCWmSbMwIqzkaXNs6bF+TpGLtUOKF00O24bRma2DqbAVZmgMAAAJcpIX0FiS3NcOE47DwbxHzNTcET3M4XXLxgjtBu5x/pOVk5hjlyCcWdZq0LIm66jxdXxhrPzfZEJQcWqNTg81KOMCWUXXrVPmwXRL1xRasLqdOnh08O8cbU6RZ9O8Sp3/Y0jKNDyosNweO4YBIMvh6XQZ/xYQi3v2eC9LF/7S6NRiD14G8vA2migtd+B3h09TsA5YUriNIQ4Yc3nNh6kyeObrz032k+zcx4au7QhorQgWkKjMBH4SM66moCyX4jF8obcJEOjc0EFamFhlrr77zAlRpljazuUYw66d886jLc+nW/QzZyCJ4wPzS55ETr1YBTcrHxCDXqiZehxPIsLKm3R0BKXLURIYse6z4ko/60Px+3IbL4LUzCsNdLH6ILqv99iZHmWk1EFRrbu3IIV2he9jovi2wtumkmxquqKLdd3k74AsVhwuow8D3TEifjYT2OM1Bgh8vVZppaUzyn4blFLFFRJ7cxAJXHXBv+t+C9Z4p6LGE+/GHOhd6osRZd2Jxvpf6d+VNr2nWAysW/atnAe3fIb/g2J4Y6FUqfszmenPjOPzJaJhtqxoZLxMO9IzUUrJUdk3luQq4DtahVhDZprzUvqLiAb6FIwN7EYXiPerv0X8G5wZY49Cyg32Lt8nGfF2Y1Hq1U/Xnnka8z0ZS0jvgvX/hUsE0IqL9CCF9O8bx/J566BxWfiLeUmWga1h6rKAFHVbyk1SiQfw0F9fc78n9oaCxROfisoIYDbtr6kdpvfAr+O+AGU5rZ8a2U1VNKJ0+hwmtI89na+tSiwC3qI3ldqiDgKFpcDurAcMpgj5HXbrdel59IBQWV591y4yM7ddEVg8R5IynSW/ZdokFsjanuG4EByEngmq+hpeDsDaFiE11d526IBWJCYF91y/i1vf+xGQVU1CRJAtWkWLgxLwV+YZM0d3uVCd1Ikz3AWjZ3vu719v7gbQBEvoXa/LzoKEgRkhnx8HnUXfDzj5boRhJMBr8iySM92DD+pGWWj25+UHo8jGW09qmRT3C7cuvf1zN7RrJ7YjVhMc8HjGbVSLaHa8fU5f+EUxXgoL8fCxokoA4d6RbNt9IXLRlsd5rez3zIbxdlz8EEZjAJqUHWyxkz/AUt2qInsOoet/hss4WANaVY5HSGT+CS6pAIL/XRZjr83shzu89qA58HI8axjTx8a/9kR1rEee5MoQIOfcpOPYasHfenBOdMbGft1uZOMJjCYZJP4yOgW/OPdG/IOQmY1uSBYsHakrO3fBcmivV13ngTn9DEVrqCP4aDDexZCiFp2Sad9/LuIWG4GydmmmN3HGOB28GRllh+wzEpq+zgicBVg5tPbbi0AkTr2TQjXSfaa0p+l3vHh65084ZyW+9F5k9+OOpcdOWNHM2iBXzPXx0n16KjE9+AB49BiHVz2zzZ9EzVAyMPQsQkw4K11mJzYskFJcD0Mp0u0HaMLTLIatO4JY6NWoFhPygwQZ0Q8pLgBNFXBVjPudFu8rR0RKpl3OdeJnV1Y5herujQY6HXhOAotEYtyIXrgV3XYtuXrWkBI8R6jqVpClbOR+CMKXOkkUOaCYtI3jttPZIeI8mKb9nK+zA0gtnOQVYg1dSAY5N/nlCFLFrlVR7TMU5lOC4Ce3b8h90MElhompyJDyR7xNktVbt4BlJFob7Mh9ArZ8Y7t+mmSmOaIp8ktJ6j4IxDJ2Bml+xed77knoK9SI8vcmUvKrRZFKWMgjUFUbs+ZbZZb/e5+nsDpo1dhojSsgmOompqhfD7BgzEnsj5oTzV73pGfvcLK4zy6s4QoBySvRSAlZZHYqo5gt3iLaDMgRu/knIeMc4zrNHrpO5NeGXUoo5ovEyyf4A/ZSgkhS6KEBTfXmD1/iWl3/VJacFJZgZdQHWjoxZnUVtr8vNgQY25dopT99pZL3mYkqzcOMrXtHUaX4/N8Vw1yTTYPql4aoNAByT42cPn5SKg1upSBLLC7fjliu4mqfYZQiD56gSbHPTdsJj6WPBseBUfiyeN+B8psVANbuE+8MIqy9N2ebfHHjSH8tkyMH3wjvxn7K7mbtA5p/ezBHNwVgkSQl0Ma/Kq3rZKz4a1r4r1vvxZgku19ZZH9QVD5DA+PT39W8S6koQ1JpnICKWWcK/9tn0+NbILkvT2jbPRKUhO1ukABGketLN41kNJBBt11dQxYE8xFHOPRhGtxa+bJM2nXrLUIPhbJCLfo/bhphsMb7wkdNty6P+VwE8i7+8MVSfbySs0KFLkz15SDQ6JMehhWZ76G2PPYSSWzyYAAABMigBeSMzJQohUSofhPDzMeJ4IAsOJtaXWA5EPZF9L/EbJPj4OqrwZarAxgQO6A878Xz7+ZJ4ZQ164lh3CyAtUAAAfp+q/9ez6dTDLhHItB97XCviz18iCC86uTc9NLUjboXMAoCNJH1WnNRFH3QuWVJQpAAAAB79GjMI4Z+k0EUvpUUI+JO8vwdAS0GpNXWKpzrpGDHwgZn0obTsRQT9TBitT7uDRwifYhN5g1/bwm+lfSs44k2a5BHV+4N+QoV6Pvh4eANrURI/S0bzO0kwtXyVOIjcKrhFJPnD6nWiE+m/ut3IFly9BF+jta7OZn4Wim3/Bmb8Z+Qzk5i7Q7UxatIdG9bLvmPZnnB7RwhCBYoy3aCC+ilHkjexVChdwMCMUMk0TVNzL2dVvZrgMreUIn5KJImgXhtHi6E0EeE9GakE+JTTHtdUxr6/QWfhc7Fz7JmRHLH5IKyhLnDFPDP6Wt3XNkNxZj7gKvcJ+ZAB7HtuszdRdb4i6a/uu5j5oSGoEKbfLzH+0GDTsVpnOFRePCLwtpD4BCp9KH3RcG7pqredWf+f7ApupTTYtHLLwtlQxpfVXTnDvSIF0DS1upC+rV82th676J6yHvyV8Y4L86GSHsE+eq811tDfw92JaKYOUSZnoc683vea/WFGwiB59p0tgq5623YL+oElO9MM3q021tNBrJhyxSzntlqx21udNd6B6wEoqw4D/1ABrZZGzNEwTWWoJ2jAaYnDB1N/CcDUufxsMEude7EosLp9ywaiMPjyXnVlO7O+VfndQbMjzl3SRR+FPkbs4WC1w9bNuOlqJKF2E9SR3jmrL5u9z0rv1d2VVIkYVHrOKPMZUkfiUDvZegh+VW3bZDpAsPTi5iaZ5V+PIpssFyshIzK5FYmyDqFNGox1JGEg/m5U8/zCaiE0BDRf+J2KVX1ifUopsYSGGX9AGnr+BiHt7F2XyuSpkGGqR+AsQ+3ne5gAx9hODxZ6pOlaQloV8Hr4nTCLlZ2/8n1qU2oF1wyPx3LLrznMibLzMLpKXAfBuQe/ftL/Ew2ZZECDSw42lwEAnjhAs3c55qU+7RSYbLAxYKmdL3pAIxZhdEn7PyEQIoUa0HovM73wUmuzq2l/rDz70UPyu66sMJVN++ZfFacR7I4fi8vkEiH+v/wBz5Qw700gYywPBrJ80kts81zl0yy/VtkZ0sSXlouw04Z2BoS1Y2hcbSsuC51WmdTichRSHGhrT4JnQLJWK5Ndu4SdkSm6tmLou9/oh4/1guCHzafkEDxnIYacNNPXWV6E6Ufttxv67KkRL6c9m9nGTPCLx5S673+U+nCOBTLPMG1ANpRpbP5ps1HXpQuj0ZlesNN5xAp6CvsNKdtX59xVukGpKzYWMV5KJniJv7jFGQJVzYoSRYQGSKAAAAGa5cDed6OA4+z7e9VKeREs3nzeixqAE/ljGEhcJ0Bfqi94fsAUlvNDq+8Id9jf7ulpZ4EkBLZkb4SVzLsDjnonvf/Suq/INAHP+Bav6qHq35WMuHVJ76L8NLjq6C6Ubw/Azkkb/VlVlNliTsxXBjfxMMkVBoyMcTL5pAMZkrOj54eWMyb7+JH43jo2tlTugbSJHtiRdF0FLRkKDlI0euXF6Hk3c+O3NtOGh0NOHV/3ySCWgJa1DSJFjWJzpPFWe1ARaLztDaRT5nvyZIV1W+fdVgFrQwzupKQDVfCuThFkO4kOoS0LkNFQZwdGID/VCJQAH+huvFlIPtVXTpd/CC27+32O8FEnNz3yscIfGtO+8xteKIOfa7Jb654CWF/ZUkf+C++kA+8AhQJICha4OWmDs0smqb0SN9ireVQLELuNFgwYI8ZiIl82d8UK84ktZqGRAsYrLQKIid40sGo2JaMGMdxFOQStyox7cO1/po/zfJobK86pAgxXcvJNTO8tNCGiqTvwMS5ACgj3LabslvA+grz8RZ3frmeBsrCTfXLOgX4W+eIPXUAGWKAAAAAAL4jV2emfDURB9dEpgZsxShEbIyNhi5OufyBOnMhCxiaU1aNqgit6Pv7iLTyBStLnYdPAnDFzB6J44tr7aIIqXFtHd+wN7VdcstkiQnwl0A2QV9EDejpzmBDPL2SUTIGIM6U+Qfa1Dobzj1gsvN1NeB8eXv4TnNXb/RBD7/NtUai1hhIObZ8gynISCXwnKC5S28w8KDoDKjzsVObDDySHiyKE6TIqJYap3Co/+li9EaHhUiiFEa05aY6dKq35HkEPFS206fzjED+AM2nfVbn3gfB+SCAzt7ZmoVQCZ8Zfcn7Laf+An2OKvBPbXiyjSD+Wqw/9eFZECpx649xhu6sr+OGqxZ2AUGy4ZyILow2qvDyIh2oCUKbrLxlTA1+Drk2DD6zwqo8pb3E3Tz79EjqUNgPWdzX34o10HTls4skcuNmQ30ky8kNZnKPb2SORF6nOFuYTY1mwoo6Y//MFdGRGfzyJ4K2oK6bH3rP8w9nNWYtBqbO6ymgwYGsraPrDCpqpr6x/yThOF7O1HI0duydzb79ODrFix8Xk3Z8B98vS9oivDUw9S0MRii3TAEWhG4xzNoviSdElLdag+iJ799vX0mLI2I4TqqgONjqol8CIoVZ1K5fv/7f8FEfEE48upRcE2Hu9F2JFCqJQ9qP+0LAgtGsXVMwESyIpcNiY+oys4TUGJxpHgZBeJUmXQWhXPB2V+TFsZVydUNXreQHcBlLdicsVHU9fRvmtTyY7GtTDvbJzY3bNybkPoGvXv1L0O7qDUOVFPMXh6XjTPJstELukNpenxaygBDEwkx5jMr9M9jrUq2awwSHDtvAXWRApRsvtMOj4bqbwjUcelDXXXMhcCZdMe2lMLTem3eKJ7lCAo0GHVlcggXmMjOkkKUc6qfBMhOnUIzJm0YewvbsNE/HXa4FTUWVT1lcRrn50DWcFZ6tqR65UMGZ+8zNgZ3X9/WaQjcuhq+n9D6ZtgHnn3dQhjb7t1Wdxc+lrCKhL37cqW0Pb8kyApZ/3ddK/BsJgZdL8JehU3EaTyxBge1vsLzM6taMsRaYnCmbMvtgFrqA13peubDN3xOQfhPWODqyxOUPgsAU6dMnK/H6TDrE5GFyvIWVtX4oW2mN9f4YYCU9fwbTActI1n+9cTg9mCKKSDMf6+BkwdOMW+9mDrlTcERXHbvawEAGeWmhb4qqfKWaIXOBbHMc6r+G8qrM05opf4/Qix0znnq6AJFfmHGluQbZmUh98C7z08Aa3Fa4UYp6hQYAJvSKaWXPOQL7Tm08MI7TC6XpEWGGVVRE6XPG8noPF0IJCsdIhjF324ygVfodnBC9JoFIavIn0Toy2G39ZxjGoh0tGFHqScoZgD69VIQUxPa1GtfA29AQo2ULp1fdam2XDOn+Qh0dpufxFpw05xWvgaCn0Ah/XEwU+1MAfTli+JPRT0aKkbTUzDLYAGE7IIBw0iKH+/2z5iUg/wJZuLHxyd8dYWquy9LGimH5EfbYoa87JaD2/6x0SQAdhDVTYPjnSVGOTfWR9aUUib4OnVUX7v5gYm2YP69hLLAHetJOSODgdV53m1QV9VkcUT/tF1LOrErW8cNe3alJ8GEFMNLyms9HvD6AGIuhGB9wuU3XnTRv9LLT9xflVBEk+SdG8p7YT/J4+BESqjZJ75A3o703gwfr1r+DHm4InPO92rf6p08J2irNe4HPLVl8hX2Wcm3/4bCieKwmvJfDXZrj521f6en4XbEvahuit3MLj96FEILcOfoeqCqtE12GwdrxuM5Tsa8C10ykfwUdlnQVqImXWsX1b1+20kFmzA7EdwiDu1pDLfTSG8bY3uX4ojnmtPlDQxjDYlLCueox5G0Rd3GwIJgjg9a+jRAHaQQKtJYIJ1kd0pDxwAx+prkM8ZYvB/0+fwVwMwTapRxuWtQLbEdTvete+qYEPfqbLk6ySavjfKY6s2T1XuyNVOBoqNGUVZvtdbnY9dVBolHjr3YB/w1TvJFv1dNs3tkyspFlw7hFB+HbtzuM/6LcOLat11585Mn5zOrwPnAaegKqYkiLisI3iS0MAUqtWq1cBIFCma+i4tq/lYChRoHxcR9G9mwWBNekW3ZZwHMBQQeEJWQ2YUBiKJYC4awN658LR+K/oNu6O5G8StG6ACev4I6eH5DGch+INh2muPfohHEncuPE45MP8cjLukyxBlEgJIsiL4ZzQhGvWfd40k4o7epe5mjCBESKKK5RieoAB/XUVOj5rGoXotJBCZPZrt9LGMG+QakphkZyQOQW4pKedWPkvMU9TqF6IMZelulb+mlTslPkPVXf9BMdYztC7QyO/PMSn2atIWM/33pGIB0fb/YCaHrGwVLJ8qHY/WWGlhAnoXIYMo5nn1mGzxGGbAI6OaAsa7CTIF4QM1p414FEuzxtZjvQm99g8C1qIcg+zJRGqOn0oj/mw5xGBFReZPaSNbM4yzuGhEu3zvjGp7o12vvuDW6ICOVi92HtWPBmgqYv41NWMPqt86K701sJwnLbaNDCXWLC7DyTTmA9UtH7Fk4DeuVKPOZkBlcIJVUhLt1tpLR5098MGgRdSx2QoVtLHq8YfsUarYLGD8sAXUawWDz0/pyWaAtmLvvV4i6qCrbgEdJC7IWmR7zn5Dj3kdOxhScTxI6sWnE16OES+K7Dn3vIFsCZJADFWuiPxYThaSLKznp2FHO9onIVa9+cMyVZ9T/u/96hiWw8FughKctCehEvWiN0ZFP3shdqw02QnejznV2hLeSQux9ZjwujqgXVDSrD1HwffhQzC9uWUSYxeiZLd+4OKMwF8QZskLL5Cvss4aWwBZmUcih7SEzJ/39Wj+ChOp21f6en4XbEt6YpugMw/EPKgjB83keRqDNng5tJsGm3OYYdumKuOP519FPqNwwpyjppJq7lYFT3R8oQPT5KnwpX10LzzmIUchk0mlpQBigPFTDXcmZSRB0J7MX4Bb3MMTcqmKwmCcxLLV4RqKXERTRXA5BDO5AGWTmmFls50xe/q+Cd0xzyw1TUV6z4pyxRaZoW5WoZaYoX909ZrTeptYtAVSlKYqSQaH1olmw4zE+P27wD68DnjneYTCp8vVdNHUQg/PJJvQHkK+wJRyBQitQRzS50DizIxOf6ZXjAIelbTp8tyQLwQcDd3+PIA4/Isd4loWcJUe2QQKdtPQblsMJwuKeyHhT2Gc5VDc4LVUFveg8ldERzmBnMDOYGcwM5gZzAzmBnMDOYGcwM5gZzAzmBnMDOYGcwLbAGZHNhGdOBA6u6E0mQCiueCrW1eGdWRdKpsTVPHh5ots64SDT+tv/iVD0txsKC0dPjibcglmSf/Q7o10y8DipXPBCAECd2mlrY6sPlWvOpDxQkXtIV20+K4Zj2qo+11I0kz3Df20UmeNeYEWkkmsViOW9R9G9tISD6QLBXXYNoJnjPQk48BUEBG+sx8qreMKbyNy54eD1GZ+QbxSx5OFu7G/GC15ySyLR8GHmr7SzGJGG1xuf3FSB9Nnc+RUQRPt1eToOF4NtvokG2YcwSGg8pP9osSdt2zMLXgDjYNU3vYYWIpTXpa+GcLgbMyZRv8S2fDf5HHVla2c5rgP8GRP/ftSjEp+EL+cmUgG8fppb56KfSPQuMNSqHP2/4FkKczQ2FJ6+6cyDs1F6hciS023Hqxwh+GBIPiJwr0xE9hIk6ddB1s05Y1d+eERg5eZV2Z/cdxwFawTl6fFAt6xoUFLagAWT5/TlY5TadKrcc8YV9KbmRYm72dqP1Jy0fu+fEFNnAHXfOw/xKpJaYlKNuk09vHpTaoKZoAs5GLYthXuPFJakoauDxDmeQplBTRgFbDHy+V5s/ztgCxvqfXjm/6P9/W8R5kT5dKjEcE/e3XAgxCsVJCYZaMdUm8FsjQqBt8qpBxzE6+Q5QgkR4NzdymZJY6dEjccpaeZrFnr5vZkc5g9vc9RshA7MByHy2+MuJtwfCC6PVUz63nnYWyK289KVQgxrXO8jFntXThuKnOd8GHpvO0nB46HlSrSadI+8kG9ArBhhC45ijuHPPRr0rb7klUXdHthEq/iWAZ89FBQiQPDzu60lVYdshkLMHZfRlGlH5m+j66I4ElW7he9VObToPzTDgOcDQcAiiLPo2MOFF8IASRWSS2fvoW0A1qNPN97l3N83lR8NSeg9pVHor1AvWNZxvRcyRVU7Ft1EscwaQ0cjWcJih2ng3velttMdMNYUP7BFnZLCGQs7qC/rZcuFb8+ThW46pwD3sst6NmcSIGiBuG0i4vExhEhkxI4/r01bMOqoJxK/Gch+xIrbReF9YnRe2wy47cEL1qrlvveE6CPiRe2fMDqkYS2KBJu2ewAjFWRdtqcBZDBlbRorvf3P1UkAzCIsI4XBUXrAKeUfikA8+uoKSlL2z4DS2jnXPFdfdji/KQ0i4TgaGPFPlXWBdxQqvF7aY4ch+U7U0woScNVkwX4uroSPH/E6NEckT8FIVEk17Rctd4+4UI3M7yACpmvCcbphKT3n85L6mdd6RrSL4HH6iB5zgd10Z/RjX9cP42/obgd7JGKiLp2f9GAuCLzY/z7YcbasGlZhsdVh6jOWPR/mzk2pzYQdO1kIIsb3vj1fKFgFGxRRFQu9/jnw+4o+9Ni03gvYWHPOe36LOqoM6387pzM8XHqVl4B9wHuogCjXAXfzsYfVB7mLU5Nij8YixSk2i5QS7uZY25QiU7RXmtrZoj2FslZJQnjIvgfyIg+ZQH9k846IMyyJbezOuSdZ1Jt0zI1CPLq/p6H6gtGRf9NLhxrw0VOblblUb9qBUAO5n6sdjpnCwwfqor2by7PhUlRJ5O4ynXlgINnBv8Fxr+3VD3iZV64ceOnHyIhV7aVSrZE7+qEuImYgA55e6AXtkTwFihhwRf0QqVVgQza3jYtfnpAamuJN3X9ylqh3YiJieG2U0XBA25rfbSExtHcxqfACfWhoiW8ZSr/Qya0pZ4VWDYrDpsGano2tmx48uxjn8DnKDVMDrgpwQRKe6YxCBr+3S/ioh2FohEO8JBH6C2Lvgp77gTNXXYYBVmsZNmsPv915g42ZyOrXWG7IDUUpaHWR6ikZcpQr4lsnPTyH1FLmZJecIjgSY1pKh9N9Wka9ODlKbwkLgpYteKdarouXArGJj3PS0NmIsgGlLstb+dYwrjovf+2zwrFrE44XSbu5BeWYOU/5vtHXGb7UBmC9ykJOfaYoTebmWcTBbRqN9aN90DhfDmCfXAxz80e+t/gt25e8DM/w+8jJfGopi3Zkuie33LLKInpN7/X+iA3L9G4j4/KP2r/ho7+E7g5YK7wpXbnhZQXQGMcR0JIwstXa8O8rcHGx53zw3Dg4nBNX5RAESmKO2HBWq3NC12PLBrMom4uDteCaWFRK5YIcWICVHrw8ju4YXelC+1OKS1Qb4lc31KdDAJxLNE2L5iV1FynJvVRVhCqCvn7ZT/y61bCGN+T38u8U1NNL8ak3nTlWHJ3mfkqUzejMUh2/RlxrxT3Cc6+d6CLAHTe9VyPklcJoZ0toZRs8IPJJPoGKXdLGzDI+gvKS64ogVavsPvJCOHkv33y4RU/h/cWSUcyEhNGX1GYBqOBxlOg/UbSXzqZBaZZkX+5ZllOP+C7uccqk+apk7U81PDeYp5qdQuSZ7V0kDAqf8/v9vZMNvGarYykZFJnpDMlReGObP95zKHzv0uM2pk7uvMoTOMet7P9nOUe2cLGk5vjgb4aJxdk0V7gXsbMVN9mmmPCqdoU/fYYturdK4gbKYh/9LAr2no9TjIoXCH/hxx2cugYjCtNKLQrD2bqGt1ZmtSpNpi+u4P7ZuKtkVZn1lZYMOEY2oNhDnxbrGknvD/rCBeYgIWMtAhgshKabeZKNKynKoU03Mj1FwWEjfHqzvjhMsQm4oiShVLmHilnDQLux7pheSUGqKjC6ECIv3feeTI9vgZCJlCxc3/uTnmTDOXNA38a4CCONyeugcPqx+HvbwZ7LUZu4rfpmGz1X4UzdSzvCZdLx8pVz7sD2TepLiKk/mtmvYmSUDSt+vWoxWt3AXTblyAtXMrRU10CMjiXjS5HtSNVyJcNtDDX8f75QlnCcCxcE6bsaHvoOJg3NDN9OgZL8xaC1RYe2iHwgxLUINfwzA85NLVu4LwhGsAFe5KWonKnuDF8qVdnmia0tqQo0oAmy6uPeKZVfxJ/mRPYSNf9EGZ/NjYz4Rpp09dEFbwlxQsxeCaz+YGe7sqq8xjgANZkDkDyv875xLimfAqBnpp0lpNWhnxXIvOsSfKWB6fI0mncr3D+smtJfoIeS2Nr4E1F7VE+KMavVPVAkMMHt0Wy4FFVGd2hPOQPtVHwo1jM+aBUH4amfGI2tkR2QvrBkThsQ7mfEXtyRhFgCK0fsUOufQYGX1FANqKiBMtm+Nbqp7anzVfs5o29GfqXfsf8zcJx50C65n0o5wwqsKKiy82Y1RHpG7LXCnasXIHmMJrbPU8+XHX/DpSRo/pG6YHkFOj7nX/EFpPnQOhQhiS/0mY0WjlVt/n2nIskQdqlh4mNLoGxnhHsWOCNBTV0/4Iw8uFCctOm2nM56PiG95elupyxMx0hQaA7fnv/4LwlcgyfZr12FJ+0vjJuwoz+Bdzygvx8+tMUk5ezznJb6lHSw8a6eyqpuYebKcwKkAQpKT8seXNmZd9hgMizcYtt1FGFbEtNLuf2vm2rP//GnRSiizVQyroafhEEtIodjiNOikcK8zj7eiN63HBxypCpF40L3OY9US3r/dkD2Ks+u9LH2p2DKgHNbnky0a4qO0WUyjY6S2jLqEy4eQhdN+r1v9fv7KKR9fhBWibNpo6AyKKDKI1eQAUet+LmRVohjVjU9Rekf+QvgooAFPrKjPZWFyxKJd5KPq6UeoI97vOEgSpAjfjP9Vc1fLwS9t37Aipm+epQwBODnpsJ3IEiLsh7rQTbKnM5KYR2s7wizLBPpRsW+3L/1tyEQemURTUqlwyFfg9u0n5RzdowRNm5vZcJgreCGEy7QQrbjzM4SYRnERbNMwNISXIE/wuKk7vrvg/rJd3t5ycyzm91/XhMxOIYklK21U1LoteNIrfO/Vedunpog9gf5dlI51MeFa0Gu4McDO9eN2SUqe+a35g2IGsQNH9QD2Q+I+QUCoBCkO1V8XpbR9D07NQy/C335bAMzxcex/hRjL14fshSmIwGkoRlkE08pA5ckACj+VZsV16F9PMCPYaCThgsJLltZx6hfXYj/BGaL6XfOIsHv4slCqQ1QxZzdBLjWSIy+SciLZZmIp4gAuDIgS5xVwpptrwvYe0Dhjk3KQ+4zh9uRrvhixqWNEf/otXSNyKrjq3USkphCD75inpcWlTfBERuKF64x6GiyMpOLfx+qqSn11qthtYZL2B0s6wlTG9xTb4M3EAxSf8Am1Fzx5vtzYYPndGaDwLtKadnf3bW+0efqk4bQOQhY7RkllzkdQbNjHCmqBDK+SPLLOeiXfpIbQto9Xs8sXDW8e4CokP0Kyx2MsXtgXMk3Saxz8vT5x9A/9P6AADq6olP1jpN5AD8WITO1eAP590i1KtAE3/jHD5PYNPZXWfPkyrvpynZfsMidBKGfMgRKg7SEQtYyE6cHDmGa/hIiLA8xWQriOVt4RTUM3vg+JRB5uqsJNUDiFUpiv5pI584lfClNwfIU+IWYiIYSHOHYOb8WoQEUtF5haI1MEa1o2Ia8EuZKVKjySr9GQvLcbDAaF71UUeLI+Jxa6o2yyFwG/PH5YmZKwYrJDaivhE6lXMHsLJ5C2kW7VYDs1hdU+8d9uyVLVllmIGgbGraJt9h8tsR708POEEfEqr+JXX6TBx4KV8Btei20P0yJviqEGL3NPXEkb6T4+mb50Ifbb/C2KBLQKoi1Emls4k2wtEpjGhpAFSOsCOl1MN62/ULWBWjxnfKfteMJAtBTz9H5hq5abtbSb4evxQUdkVPOrwAlJJU1j354u02i80U84Gcrj8UiQMEisbPAmHg2qmSngEUZqBb60KRIDC714hV9otKqH4uQjlQPpCtfHBccgVZuTszfOnlSrSadIc39yIY0hK75kmhOGEZGcXUajhJhGpyPwk+z6zn5lWOsMoMDLW88jTTQbTadRlgf4i4yGIQn+uyeH8TK08cNk+s7dkAxngB7IKhi83FnNFHXRJbP30LaAYWJL3xuneCuo5UfPqOk78vApmlTZnC+4gAmTYHPDW1/x/CRO2oFST4mHWFunUo4O/ppRG/U2FkEG8r/vOcEwRjxl3HHgJPzr83ezOmqB5jZiErHqMKtVUEMDXjYl/4tSB78CbDIJ00+NfwI4mu0zzk1Z85pZfhh5ge0ygIMHwgU5CnFMutZepDjcMHA7w4h1kD6mAr8z/3mYQnXAttxuNuz3iZrsGL81xzuFCSmvB7ohLwCNKTtbcK63eiyTh2Ye5chJBU5iP9saITjgJDL+SJiMNANkrWAj6jqBH1kLGjPGJawhxB96Rn6Pqxkcmwjep17klsODw2jlpk5HSOSsLuziKE1HqFAVx0DTB0TluTjIrt8i6PwWmoAS18CLlTQh/BxJcbZb8BvW2p+7ho2DNI/lfv6k2YIFG7jdOOw+fgIfgtIPy7nuVHZka8MN+NN4bLXvRVDvUQvf+uM7zl002Rb28mJswRI9v9p1kjcROolcYI3zV30ZruY0a2HbkLmHQ4h7LdXz/rQ0BI0IKOce4OD3ZWua9LGHxgp/KNDK+wh6z7pJbGaFhmrTmkqBs3L6ANgq43FBm1zElLNWbpeMfOyLpzoSJROs551aBMpjMfyCMlWB29aqB4m81DVnrUf8UMHWAz6xLgAY6QXmXpVv39Re1+07yFayz6p99Oppr7zYqxjg8xBxqW7yQoFGZTBFwT7yEOAH83nH2HVCLVI1qaInYXcZckZB8uhuPAuLoNlTovrmXqgYKzvDd+XCmSnEckTLJvVqjFMJAF0XCRPE3XdXaMumyy3HIMAuSJo309xhAuVvaqh0bRnE318NbbFabUpGQtSPn7WeWPjx6RmJJzRLKtDw67rJ1HgwJykK4VKcKLtciNeY1Is6t8eY5WgqqZdtktj8+6chg7rxox+X2qDdaXKz4O35LZ8DmK0lwzXLRSfXGdF50THo05FUBpLwfa+pC4FWbhYwvHfq9g4Y0ttfdQqmitxAOFaX7/SEjMrPGhatELzqPVFJToldDNTlzulj2pe/WorPgUAIdDENtaVbLbEZOWNUueQ17E4BGVSHks6onyMOKaH4TmyzzYo6Cz7dNRpC14bA/sApXY4BcqQX5J5JKKkdN/fOZZUhY0FGFnNsO3FzCLP0s7VFy8S+cCHfemi0kqZePjh+BsW8khKP5IloK9/u8By+qlD5MX4EXLLi18dQFukEmZ3zRQ47UpXMRiAYN/N/rE5dzvQ4NhreUVxHPveKN4pXAUGHVMxwtTumhX7s7PPiU31CMlZwVabXbU59PRsLYNMmZe3MuheKwbh+bPcaKTawxn0E2X2R5HU5GlQMB/WvbPP/pRTiZwbHMO3bergw4rIfaO66BIOKbynU6Pt3h210fmV4XM8ulkVfxbOoOGzkbWVrhV34AYZQ2NYSOtPORMzhBPZzMcrV6w0yXMltnkQ7RepU2AI1SF63ku/e3aRjH1sq4KyMTOr8yhsfVDE/HQ+e5xZwZGOq3qJNOCSRMV5JelHk7sprNb7JZPHaQP7KilX44UFxIh0dKkWA7kpUDvc5DKHWvv89YD2LwEdVVfNzbAn8d4XzkRr/USwvBFuN2f3ubT8T44Uy05IPMheXQNpNag2vRmfVlZg5K75mqnOzKDuASulICdOGjscUvX82kLPTymLvBuuLC6awrKMf4pFMHef3W9HI2pilmpasCamfL64w02MowUsS8LLaJPQbDH89NWOL3dvcy8ZMkleMlZEYjb5zSkyLOW/E0CyZukJm+Y2t7FcREhqFJ6/kyhhgAn62mcqvVC5/TJt+BJInjYPWXqfZDCy2kAxTUMGUjGs9zYuACEwT7LpaMmXGt3jaKbaT+wixDyqNGHB5IIEdXq7F1iCrpYi72F6CjyiLtffH51UlzeA1DXKx0J0Kt54QcXHlXy6WmDPuWGtWaCLRBwIQedN0+yVJvLqcxeH4ayZmKUvDfFsMoA40LSZ78RYAPCiQL/SUIsjus6B2cJLwAgWudoshGSO08zIN/wtflNrAH5Urje3zKpqlWkzpz2uZuk3xi+t8bkATjs+1/Yr9fYOK48FNYmvISdE4wUCCLNl/nQX0Fx357465Yck0WIfW7VIdl2TERyj5XOmAsKMn7VfuE0KqMyKvygHLTk8QQjXsHmmw6sMSvi/pQ4F3JDIs8rBI51OmNQliTX2B/H3t7AXS0ohQEtG1JiA0GlH3SqSUndZpq64J+OVv4PNgJJpImB3A/hDaoroOV3jgq8gAKMwdSTQIcpj7MGkWT6UTq1wWiik2KtIFQt55ivsEmhvM/8sYIFwFt6AsUzq4gr4fAtISSopR7l1VmQ11JBWi1zDaVralb1KtqVvUq2pW9Sralb1KtqVvUq2pW9Sralb1KtqVvUq2pW9Sralb1KtqVvUq2pW9Sralb1KtqVvUq2pW9SkZEvKn10R3yI9lqyZMg+Xc4rgGCD/k4S11usQBYIpEFT+EY+yjyVDAqa1gAKUAQ1a1W+7mJa0sBZwECIu5mJEuri9ZU5F9ygPTGC7/4n9o0Ws2+L8MORDRMfSAjPRY1FlD3gTCqAl2Dx3qsPyZ/kSLjDzzeTHhxNGO066TnDJv6QU2KCFxrqpbeZY5F0I7S2FzT69lhx7W/rTjU5KidfcDRNJnxFSI9kl3rPfhYCX99MUyApwQfHUzZBnReltVk+CTbfrx0+fNcFhcsAV+jK3eJUC/1RHWPnNAWE9ifx28tcDqx0MKYxjMH8ztmIEHkxXFBbKVjlVWEGTHPi3otPax1lYCMIsKmdOoxb15OFKogcg5MeLAAAAAAAEAKAAAACjJC0RN6EOZYfa3+c42JDKvlZ54/I+H+BpznAZllDbTbAtJqcCSCDKhgpE2rYAJZ71AgSDRMKFgU0x4+slc4rpy5FEWTNlQOW3Tns2SsDHsRI+q8nEIAFx09euC0mNFh7zFbyGmGSbqDYjoLC5o7KDFqLmoX9mTju25PqjarvzY+SGifRX2u9P5Tz2sfzP5ZX9PZGKD5gX9RKY431ROfSaiItxJ9aQMUyv+YsUXJdzmLi4hWIUb9YWgc5SGkXVVipBAS9tRCDJ61ylZq0MKRWgfyHH0Z7VHSJfL+o8R+marQ4BdNlDDtUrZJGHElFYOlNY59+b+LVe4f6TfC6PDOry/1mzQzvhnRZGwXELl54k8RbWNfO2bU66N/mXE4F8pxnEuaCoq+qK9TLe+WjnqMwyVmwKkR8NS4SJyPH6bJFewySRUQEqpxePSjvFAloxu7z9krzZuFiwnurrZqL4x6q5n/3xIOQedO92Kdund1cCg40H4UEFmdSZ4xORYSObaDP1PnoqiyRp02zpvXmL/s+kpL8DawP2Bus+51nJ540sDdt7KnV4uQF/Mbzg+uDPmoMmBXzlASoi+VOjWwEKBsH5eGUiBCOxgBQcSawvZPoEJwO2R2mSEnhlJ2uoFmxXBg9SDCq15OddQ6uxyuyU4ISsY71PjfyURHiSad2N9CYtgu3h/9tzWb+keR70Q2vO7U/vzQN/7dxcC0DySdvGP1tf6SJgmPOLHQLDJ4ywOvlrv7VtGz13yY89j8Yu/8bBDP9b90MMjQ813t3vhd0zXBPUupS5TR4E6R8YX3b5y798Z7qQLECeRd9eOj+LbJG/97Fw3+ZFMGYxljs2uKq7hvEu19/NWIeYdmOrwPchYmS4mMT8jBCqbu6UW3OtaLGLrXsl4g1ukBnPWGiNrL6wXhYWPqtiv14XPcsoE3ywxzQPTqxGpsj4FAAAADiNl4bt6GIGsUEBZ5pPcRDXriziggfHrPLFOY2QSWdDlWYw3soHaN4zOZ60mphvgHuXnCUhR4X7KXAvH7bMirrr9O1fzyrZej9uN3uzrEbwwctaVIlY0jzcJV7RiokFpkNNZMySroAmAj7a0H/UkWYB2qqpKKNmpJR3IqkdYD5J6o08m3asKEgL1a/gTpPm7TT7MjR1cf1sTqdoZc2t7ELMh6KkRvy6RLiqu+VVJuChj6x8nuVJINvW+BbsGVPI1rlYGQ/ryAef2Ajh8dUiJ49nVpk67B2nJdRoKc8TM7zhVBvzW5BOPzsDEjqEFdfdNzEs1a4Kj4KBC3J0lJBmsFqss0bl/1kHVGLaDesJREPWfP7SEBvRwnJHMr589z+P0HIn4RznSdQxDmGkW7mZqzLv9mXZhSxaOK/umnvAHQwFE9z8PUHgm6jVfF/try//lh0mdF74cKrj+B2OGGzdlJ5ZBndgzX+o0rt5tUhdhB54DdxJfUx5a0JyeKeLSUw1gHysDjCTq+PxVpI5fnEG1gb/yI8Rp4wkYYrs5YFmilHG6F+3mCNrwQIGEWVNLfrFga3ALI/CV96+MWoIW2+H5VqEhbURmxlys1s0P3ktTePDPn42E9Uiuiqo+iUsc7coPpEd0+tydSqXZaa3wJdj/jZ5wcaPtMjYDG0eubX0kuWck0K+5oS2a6kT+VHLm7awsTqqVqaKHMED57BEGfzZu3e+jZHkrGiOOwLMNZG1TGfXmuHmXVDJ17tUZ0DvylVvBiNTmEzNLNMp9WJDT5bQkfHGUMWtn7KuFIU809p2Cf84sqxRmIzxPONXyKKfS3S4fkBF5ZYsAHNW2qmkcbiD1rU7tOVxmXL7B25jd6klO9Gvo4PoJ10hCI8ulNsrMgDdCMOFkAZFzgCcnduu//lpRGid3VUCWaygaAJp2UmrhDcSxYmPL/UlRDeh4qETG0J496gSX1TuZsQ1eXLEImrhgYFACVwZzzU43zb0Wxz2l/Jp0zCVxABo/+r4MIZzo9/DtT0veILyx6G3UZEQJSAMTBKupH8ElRPYyc6JUu0vkE150IFnBC7ZO5HLu9epMj3HUs+ipJkXVc6H4rb1kVOPCw99hC/REC1EbNVgC3P7K5qzwMwXNTzzPAky/UU0cojWxnbmoUmHO+9zM9gqLXFsfj4e2UO+5vStoVwK99CQSLdiVrrqnnApVusbAeLeJR5gfRCMGerPCdcRL/shacxaWhPUDdOVn/MV/yDfhdtT5rtDpoVhGsYfwZE1TfG5mMTad/iI/Zx+/T7nMBQG8mWiGtfTVH+4ABzUaRHjA7yBKpWUPV4TVbmaYDH/WoaguEPpfLpEjzH57XbunZ2VW3zOs3CfKKcHncU2OMfCf5s/e3HoBddRTDOxngNTu2YmzyGszXmGwdfEoyU2kb+h3rFD4FIOzvILBp8wig3YL7532z90gGcspsDtjAAAAAABSEXEffJJPaiG7n6NaVqElo+k9z5bl6zgfbf+9qBuB54a67Z61ECQAPanRXMkwDLFsCH7e1u5U65M4iWQkmU3Fq1z0rgHq1i9NTsyTg/mtVcAeBgNdCezs1XFFXI+oY04yZHlaatbm5P4A4JHZFzlKUYcHz5ETrVjqG6pTz3vCL9Mhe68bpPPt77TCn4JunVasFSGkoZpmYJtvFDIs5gsdcqn4uBO8kvQAu3EC10dXbJbf2XrRaRlxVvjo3OIoz4WoxQD8tPCA8ofUH8by5ASYk3nEwVy/ZZxVGWxgDhb6W/1+m5eN7IdsSqs5Avqq2AE+G9eZDP0qzkI+ArRPTxgqSSaQu5fPxbBw/F8gVXsiXXhFARFbVX5kNFKmNC64tkAFsHj4YnhlMLb+vwBk5vn0R/zcENcnL1KvtmE7jO4P0oIA/hWeLfxMmEOd/RFmYwUkK2FsLhisuri3fdaAJrWsBBFc9KGCdmiFaeF8r9roKYqQwmejxgHB/XLT4d2oBfxbfuZDxqusk1MLGyfiVwisRqlfp5UyvJH7rtmwf3+2rfFj326NZQicfb3zY8KRgw+qeklTH1rp192Xt8bKctJpvenJkHkeLv1loL20A/TRtUkAFQKX1jkVAuGzA2f6QY3yMRqnt1YSBYUDuXXtHjaAmBn6Gwh8DHx7QbIcEK3qxgremi0ks+uK+mgsooaLrCRxyGe2V0NdF3vIEWerNRK9aozSVr4lqd2H006Y8qHi9cbndLXGitN1sdhjDR06ZVYyoc8oB2QfOArGfWazF2iZxL5QsTCztawpbvGpXA4Z4FsBEjSoAJeXoJW2KtHSn2cdHFA+/2FXadS/0BFgSbJC2M9XftWF1E1ySK5tCjbZHx3Rt0J7JsO0rhIwheDIh2cjYU+d/yAtMOjZmnIy6dlCIza9n0ramBHVI+BKZJpsuf3dtqLtqVVq6a5pB7kvYrCtTdhHYM8fHmbiB0VIk5ixge4wHayXAyXZ4mPARKHxbHtGkR6VJxUiqumk8ToxK4pE7I3Uj84iQQAs25kQkGyF0ujutgt/9IOD5Bnna15YGT7sn5+zdyg1n83RFS/e39a2yMFpVzogwgtdmbLZhQk0I6Y8yVyEwY+r+0kzmhsEeWJeoeDGRW8EMFHazH/39zUAVx6fvWRqyRPP4iygsn/NCtFPdJ4cyArp58ip+NuerEWOrSmopR5nasgWS2lF/REl7B6XNkVxlSAAAAADNFAAAAAAA7xaPK9/vVeHeXDwHQ2Vpv99eiAD/tlqwNxVvZnz/88Ao88FzPQ5AnALhhlDWgswmgQq2CgAAAAAHWOzkc/Wlf1/zvfwlHbGm/9o6TxPCh0+YCJiD51EfoA7JoWjtKKGEArl4FHAZtQqRWQZYUnPhn/jRI8f/V8GEM50e/bGu+3FxjnhiN0hjHES8oI0BeNAqn2bfwuTIq/NONKusgEeQwEmhteq2JF/0822LR86fUZESQqGl9eQdA7XMFHm6tCs6vqifz5jBBvhg7H2aGJthyCDheGkzriYgymMlssBJIJjCEmDQZW12/VQsp3kwxCUbgEf6Bx9PN0gUFctyzvU4qKm7kjZELr01FQi24idjLqWd7+c6CVJrxanBm4W2yBDlnfA7qKbPJBNoYFTV65x69OAVmYOiCZbve5DBLl6MlMynT3XjOL43GyEkoyRgH0ZAy+AQu6qnB87apZufiaijW1tbutLaVsc0eAsVJ1BKnSbKK44lX5SAQaAkeodBTLm2bqfWumy6Cmu+1gUj8CRTSR5+Svzw86inbllQEI5gLnZBGxbTGO0tz1aVv0GeSLUNvXQEOuoTOZrWAtL8Y01s1ngqRmZXTo831oQYQge4xZ2dMjMw/sSJCIZdMuNN5Xl55eSqM1PC80caHkMa2PosmKrmulvvzgskWly4vHwaBsYWKgk8BWNqF+fy9VxD+SSOzRYKBx115ii7zRY6cnHRvREV4NiKFmvQwwDbG1PyGvHS+U1Khi+dszhYJ0cikSYufl4p0IR9nRsj9ZkaCqeG1iQIIICY3cm+a21Cw7ekqJhgANrnNxWV8bE3ju45gEyRx1RTu16ZfHzru9cYTVtONR78H3mRx+PBgp6e3W2GtHQMKkJ78mSo4t0seZPiKyYm5EzTw+ZD+E6ZoB7d5Lu9IWPByG2p+0YstxD0xap1HqDGTqLJ2v3TDXoHp4XbrkMXTpc0BrulCvKYuPuhlCytosXwy7wL36SfS29tsR995Bdf+I3YPdpNXdjTCYhcuu4taQEqVrWNW84roJlv2Ekv1bMmEBLgcvO/A945UL4ILhGSfIqNq+efjsm3ssopvQc49mO3j5w/EQNz9TU3c9zmCKm0F1Z/1z/afgKprypwCp68d2pMzN7OiPxx4ApIX3QTSa21VMgVcE8z4MdP1ebx1AVQKzDPtpE/Ss6Ybwvd1rHTCyeYxV5HeTYjZfNsWXcYGXKzQXDsaVLaKbfbvVVIkFgmzBkVHcQElLLfQwLZqJHkIaXB05h6G7LsIcvL8vjROb9rlFRmNZbNiyX+inQSQy/tIHTgrfkuThvP0UxR80sJCXDUpzNubwfW9nNbQ3gYzeTvLfcmi3OZf5r5XXeseEGHE7wrATLGv+r87V0WKqWFQAAAAAvBQAAA=="}}]}, {"role": "system", "content": "Current plan status:\n1. [DONE] Use web_search tool with query 'latest GUI Agent papers' and count=10 to retrieve recent academic results.\n2. [TODO] Navigate to the GitHub repository (OSU-NLP-Group/GUI-Agents-Paper-List) from the search results to explore curated GUI Agent papers.\n\nFollow this plan. If a step is done, move to the next step."}], "tools": [{"type": "function", "function": {"name": "web_search", "description": "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️\n\nSearch the web for information. For best results:\n1) Use CONCISE queries (3-5 words ideal)\n2) Include only ESSENTIAL keywords, not full questions\n3) For complex topics, use multiple simple searches instead of one long query\n4) Focus on specific terms that will appear on relevant pages", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query - MUST BE CONCISE (maximum 7 words)"}, "count": {"type": "number", "description": "Number of results to return (default: 10)"}}, "required": ["query"]}}}, {"type": "function", "function": {"name": "browser_vision_control", "description": "A browser operation tool based on visual understanding, perform the next action to complete the task.\n\n## Action Space\n\nclick(point='<point>x1 y1</point>')            - Click at the specified coordinates\nleft_double(point='<point>x1 y1</point>')      - Double-click at the specified coordinates\nright_single(point='<point>x1 y1</point>')     - Right-click at the specified coordinates\ndrag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') - Drag from start to end point\nhotkey(key='ctrl c')                           - Press keyboard shortcut (use space to separate keys, lowercase)\ntype(content='xxx')                            - Type text content (use \\', \\\", and \\n for special characters)\nscroll(point='<point>x1 y1</point>', direction='down or up or right or left') - Scroll in specified direction\nwait()                                         - Wait 5 seconds and take a screenshot to check for changes\n\n## Note\n- Folow user lanuage in in `thought` part.\n- Describe your thought in `step` part.\n- Describe your action in `Step` part.\n- Extract the data your see in `pageData` part.\n- This tool is for operational tasks, not for collect information.\n", "parameters": {"type": "object", "properties": {"thought": {"type": "string", "description": "Your observation and small plan in one sentence, DO NOT include \" characters to avoid failure to render in JSON"}, "step": {"type": "string", "description": "Finally summarize the next action (with its target element) in one sentence"}, "action": {"type": "string", "description": "Some action in action space like click or press"}}, "required": ["thought", "step", "action"]}}}, {"type": "function", "function": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to navigate to"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "browser_go_back", "description": "[browser] Go back to the previous page, or close tab if no history exists", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_go_forward", "description": "[browser] Go forward to the next page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_refresh", "description": "[browser] Refresh the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_get_markdown", "description": "[browser] Get the content of the current page as markdown with pagination support", "parameters": {"type": "object", "properties": {"page": {"type": "number", "description": "Page number to extract (default: 1), in most cases, you do not need to pass this parameter."}}}}}, {"type": "function", "function": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call.", "parameters": {"type": "object", "properties": {"area": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Optional area to capture as [x1, y1, x2, y2]. If not provided, captures the entire viewport."}}}}}, {"type": "function", "function": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_form_input_fill", "description": "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "index": {"type": "number", "description": "Index of the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_select", "description": "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to select"}, "selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_hover", "description": "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to hover"}, "selector": {"type": "string", "description": "CSS selector for element to hover"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_evaluate", "description": "[browser] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_get_clickable_elements", "description": "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_read_links", "description": "[browser] Get all links on the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_scroll", "description": "[browser] Scroll the page", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "Pixels to scroll (positive for down, negative for up), if the amount is not provided, scroll to the bottom of the page"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_tab_list", "description": "[browser] Get the list of tabs", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_new_tab", "description": "[browser] Open a new tab", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to open in the new tab"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_close_tab", "description": "[browser] Close the current tab", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_switch_tab", "description": "[browser] Switch to a specific tab", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Tab index to switch to"}}, "required": ["index"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_press_key", "description": "[browser] Press a key on the keyboard", "parameters": {"type": "object", "properties": {"key": {"type": "string", "enum": ["Enter", "Tab", "Escape", "Backspace", "Delete", "Insert", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "PageUp", "PageDown", "Home", "End", "ShiftLeft", "ShiftRight", "ControlLeft", "ControlRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "CapsLock", "PrintScreen", "ScrollLock", "Pause", "ContextMenu"], "description": "Name of the key to press or a character to generate, such as <PERSON><PERSON>, Tab, Escape, Backspace, Delete, Insert, F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12, ArrowLeft, ArrowRight, ArrowUp, ArrowDown, PageUp, PageDown, Home, End, ShiftLeft, ShiftRight, ControlLeft, ControlRight, AltLeft, AltRight, MetaLeft, MetaRight, CapsLock, PrintScreen, ScrollLock, Pause, ContextMenu"}}, "required": ["key"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_file", "description": "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_multiple_files", "description": "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "edit_file", "description": "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "create_directory", "description": "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_directory", "description": "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "directory_tree", "description": "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "move_file", "description": "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "search_files", "description": "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "get_file_info", "description": "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_allowed_directories", "description": "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_command", "description": "[commands] Run a command on this darwin machine", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "Command with args"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["command"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_script", "description": "[commands] Run a script on this darwin machine", "parameters": {"type": "object", "properties": {"interpreter": {"type": "string", "description": "Command with arguments. <PERSON><PERSON><PERSON> will be piped to stdin. Examples: bash, fish, zsh, python, or: bash --norc"}, "script": {"type": "string", "description": "<PERSON><PERSON><PERSON> to run"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "final_answer", "description": "Generate a focused report or answer after completing research or information gathering", "parameters": {"type": "object", "properties": {"isDeepResearch": {"type": "boolean", "description": "Whether to generate a structured report (true) or simple answer (false)"}, "title": {"type": "string", "description": "Title for the report or answer"}, "format": {"type": "string", "enum": ["detailed", "concise"], "description": "Report format: detailed or concise"}}}}}], "temperature": 0.7, "stream": true, "max_tokens": 8192, "provider": "volcengine", "thinking": {"type": "disabled"}}, "baseURL": "https://ark.cn-beijing.volces.com/api/v3"}