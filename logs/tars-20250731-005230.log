
+------------------------------------------------+
|                                                |
|    █████  ██████  ███████ ███    ██ ████████   |
|   ██   ██ ██      ██      ████   ██    ██      |
|   ███████ ██   ██ █████   ██ ██  ██    ██      |
|   ██   ██ ██   ██ ██      ██  ██ ██    ██      |
|   ██   ██ ███████ ███████ ██   ████    ██      |
|                                                |
|   ████████  █████  ██████   ███████            |
|      ██    ██   ██ ██   ██  ██                 |
|      ██    ███████ ██████   ███████            |
|      ██    ██   ██ ██   ██       ██            |
|      ██    ██   ██ ██   ██  ███████            |
|                                                |
|                                                |
|   An open-source Multimodal AI Agent v0.2.10   |
|                                                |
|   https://agent-tars.com                       |
|                                                |
+------------------------------------------------+


+------------------------------------------------------------------+
|                                                                  |
|   🎉 Agent TARS is available at: http://localhost:8888           |
|                                                                  |
|   📁 Workspace: ./workspace                                      |
|                                                                  |
|   🤖 Model: volcengine | doubao-1-5-thinking-vision-pro-250428   |
|                                                                  |
+------------------------------------------------------------------+

EventStream EventStream initialized with options: { maxEvents: 1000, autoTrim: true }
EventStream Subscribed to events (total subscribers: 1)
Core:AgentTARS:BrowserToolsManager Unknown browser control mode: mixed, falling back to default
Loading server.ts
Setting up request handlers...
[desktop-commander] Enhanced FilteredStdioServerTransport initialized
Core:AgentTARS MCP client v2 initialized successfully for desktop-commander, found 21 tools
Core:AgentTARS ✅ Connected to MCP server desktop-commander with 21 tools
EventStream Subscribed to events (total subscribers: 2)
EventStream Subscribed to events (total subscribers: 3)
[AgentSnapshot] AgentSnapshot.run called with options object
[AgentSnapshot] Hooked into agent: 6LxbRn3Ka9_j-GEiQCFZc
[AgentSnapshot] Executing agent with streaming mode
ExecutionController Agent execution started with status: executing
Core:AgentTARS:BrowserToolsManager Unknown browser control mode: mixed, falling back to default
Loading server.ts
Setting up request handlers...
[desktop-commander] Enhanced FilteredStdioServerTransport initialized
Core:AgentTARS MCP client v2 initialized successfully for desktop-commander, found 21 tools
Core:AgentTARS ✅ Connected to MCP server desktop-commander with 21 tools
EventStream Event added: user_message (18781bf1-0762-496a-ae33-edfef395303d)
AgentRunner [Session] Execution started | SessionId: "1753894365715-6ievvlf" | Provider: "volcengine" | Model: "doubao-1-5-thinking-vision-pro-250428" | Mode: streaming
AgentRunner Using tool call engine: native
EventStream Subscribed to events (total subscribers: 4)
LoopExecutor [Iteration] 1/100 started
[AgentSnapshot] Starting agent loop 1
EventStream Event added: plan_start (7d35c83f-4b96-48e2-b79c-c2389642e3f0)
EventStream Event added: agent_run_start (748d62e8-77a0-4f08-b831-0b2015a196dc)
EventStream Event added: plan_update (a26275b5-9cf0-470a-967d-c5cd76b464ec)
LLMProcessor [Agent] Pre-iteration hook executed for iteration 1
LLMProcessor [Tools] Available: 48 | Names: web_search, browser_vision_control, browser_navigate, browser_go_back, browser_go_forward, browser_refresh, browser_get_markdown, browser_screenshot, browser_click, browser_form_input_fill, browser_select, browser_hover, browser_evaluate, browser_get_clickable_elements, browser_read_links, browser_scroll, browser_tab_list, browser_new_tab, browser_close_tab, browser_switch_tab, browser_press_key, read_file, read_multiple_files, write_file, edit_file, create_directory, list_directory, directory_tree, move_file, search_files, get_file_info, list_allowed_directories, run_command, run_script, final_answer, get_config, set_config_value, search_code, edit_block, start_process, read_process_output, interact_with_process, force_terminate, list_sessions, list_processes, kill_process, get_usage_stats, give_feedback_to_desktop_commander
MessageHistory Created system message with prompt 6787 chars long
MessageHistory Message history built with 0 images (limit: 5, 0 images replaced with placeholders)
LLMProcessor [LLM] Requesting volcengine/doubao-1-5-thinking-vision-pro-250428
NativeEngine Preparing request for model: doubao-1-5-thinking-vision-pro-250428 with 48 tools
LLMProcessor [LLM] Sending streaming request to volcengine | SessionId: 1753894365715-6ievvlf
AgentRunner [Stream] Error in agent loop execution: Error: 400 Invalid function format: 'type' Request id: 021753894369355215fd9e0c7405796e02f8ed390f050868816c5
EventStream Event added: system (874e2247-ba5f-4b9e-ba66-d57cffecd5cb)
[AgentSnapshot] Snapshot generation completed: /Users/<USER>/TARS-Agent/workspace/6LxbRn3Ka9_j-GEiQCFZc
EventStream Unsubscribed from events (remaining subscribers: 3)
EventStream Event added: agent_run_end (84c66ddb-8d0b-4e16-8113-31f94a5bdaae)
ExecutionController Agent execution ended with status: idle
