#!/bin/bash
# System_Settings_Backup.sh
# macOS系统设置专用备份脚本

set -e

# 配置
BACKUP_DIR="/Users/<USER>/Desktop/System_Settings_Backup_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$BACKUP_DIR/backup.log"

# 颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "[$(date '+%H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

echo -e "${BLUE}⚙️ macOS系统设置备份开始...${NC}"

# 创建备份目录结构
mkdir -p "$BACKUP_DIR"/{SystemPrefs,Keyboard,Network,Applications,Shell,Development}

# 1. 系统偏好设置
log "${BLUE}🖥️ 备份系统偏好设置...${NC}"
PREF_DIR="/Users/<USER>/Library/Preferences"

# 重要的系统偏好设置
SYSTEM_PREFS=(
    "com.apple.dock.plist"                    # Dock设置
    "com.apple.finder.plist"                  # Finder设置
    "com.apple.menuextra.clock.plist"         # 时钟设置
    "com.apple.screensaver.plist"             # 屏保设置
    "com.apple.desktop.plist"                 # 桌面设置
    "com.apple.systempreferences.plist"       # 系统偏好设置
    "com.apple.LaunchServices.plist"          # 启动服务
    "com.apple.spaces.plist"                  # 空间设置
    "com.apple.trackpad.plist"                # 触控板设置
    "com.apple.mouse.plist"                   # 鼠标设置
    "com.apple.sound.plist"                   # 声音设置
    "com.apple.universalaccess.plist"         # 辅助功能
    "com.apple.security.plist"                # 安全设置
    "com.apple.sharing.plist"                 # 共享设置
)

for pref in "${SYSTEM_PREFS[@]}"; do
    if [ -f "$PREF_DIR/$pref" ]; then
        cp "$PREF_DIR/$pref" "$BACKUP_DIR/SystemPrefs/" 2>/dev/null
        log "${GREEN}✅ 备份: $pref${NC}"
    fi
done

# 2. 键盘和快捷键设置
log "${BLUE}⌨️ 备份键盘设置...${NC}"
if [ -f "$PREF_DIR/com.apple.symbolichotkeys.plist" ]; then
    cp "$PREF_DIR/com.apple.symbolichotkeys.plist" "$BACKUP_DIR/Keyboard/symbolic_hotkeys.plist"
    log "${GREEN}✅ 键盘快捷键备份完成${NC}"
fi

if [ -f "$PREF_DIR/com.apple.HIToolbox.plist" ]; then
    cp "$PREF_DIR/com.apple.HIToolbox.plist" "$BACKUP_DIR/Keyboard/input_sources.plist"
    log "${GREEN}✅ 输入法设置备份完成${NC}"
fi

# 3. 网络设置
log "${BLUE}🌐 备份网络设置...${NC}"
if [ -d "/Library/Preferences/SystemConfiguration" ]; then
    sudo cp -R "/Library/Preferences/SystemConfiguration" "$BACKUP_DIR/Network/" 2>/dev/null || log "${RED}❌ 网络设置备份失败（需要管理员权限）${NC}"
fi

# WiFi密码（需要管理员权限）
security dump-keychain -d /Library/Keychains/System.keychain > "$BACKUP_DIR/Network/wifi_passwords.txt" 2>/dev/null || log "${YELLOW}⚠️ WiFi密码备份失败（需要管理员权限）${NC}"

# 4. 应用程序设置
log "${BLUE}📱 备份应用程序设置...${NC}"

# 常用应用程序偏好设置
APP_PREFS=(
    "com.apple.Safari.plist"                  # Safari
    "com.apple.mail.plist"                    # 邮件
    "com.apple.iCal.plist"                    # 日历
    "com.apple.AddressBook.plist"             # 通讯录
    "com.apple.Notes.plist"                   # 备忘录
    "com.apple.reminders.plist"               # 提醒事项
    "com.apple.TextEdit.plist"                # 文本编辑
    "com.apple.Preview.plist"                 # 预览
    "com.apple.QuickTimePlayerX.plist"        # QuickTime
    "com.apple.iTunes.plist"                  # 音乐/iTunes
    "com.apple.Photos.plist"                  # 照片
    "com.microsoft.VSCode.plist"              # VS Code
    "com.googlecode.iterm2.plist"             # iTerm2
)

for app_pref in "${APP_PREFS[@]}"; do
    if [ -f "$PREF_DIR/$app_pref" ]; then
        cp "$PREF_DIR/$app_pref" "$BACKUP_DIR/Applications/" 2>/dev/null
        log "${GREEN}✅ 备份: $(echo $app_pref | cut -d'.' -f3)${NC}"
    fi
done

# 5. Shell配置
log "${BLUE}🐚 备份Shell配置...${NC}"
SHELL_FILES=(".zshrc" ".bashrc" ".bash_profile" ".profile" ".vimrc" ".gitconfig" ".ssh/config")

for file in "${SHELL_FILES[@]}"; do
    if [ -f "/Users/<USER>/$file" ]; then
        mkdir -p "$BACKUP_DIR/Shell/$(dirname "$file")"
        cp "/Users/<USER>/$file" "$BACKUP_DIR/Shell/$file" 2>/dev/null
        log "${GREEN}✅ 备份: $file${NC}"
    fi
done

# SSH密钥（不包含私钥，只备份公钥和配置）
if [ -d "/Users/<USER>/.ssh" ]; then
    mkdir -p "$BACKUP_DIR/Shell/.ssh"
    cp "/Users/<USER>/.ssh/"*.pub "$BACKUP_DIR/Shell/.ssh/" 2>/dev/null || true
    cp "/Users/<USER>/.ssh/config" "$BACKUP_DIR/Shell/.ssh/" 2>/dev/null || true
    cp "/Users/<USER>/.ssh/known_hosts" "$BACKUP_DIR/Shell/.ssh/" 2>/dev/null || true
    log "${GREEN}✅ SSH配置备份完成${NC}"
fi

# 6. 开发环境设置
log "${BLUE}💻 备份开发环境...${NC}"

# Homebrew
if command -v brew &> /dev/null; then
    brew list > "$BACKUP_DIR/Development/homebrew_packages.txt" 2>/dev/null
    brew list --cask > "$BACKUP_DIR/Development/homebrew_casks.txt" 2>/dev/null
    brew services list > "$BACKUP_DIR/Development/homebrew_services.txt" 2>/dev/null
    log "${GREEN}✅ Homebrew软件列表备份完成${NC}"
fi

# Node.js全局包
if command -v npm &> /dev/null; then
    npm list -g --depth=0 > "$BACKUP_DIR/Development/npm_global_packages.txt" 2>/dev/null
    log "${GREEN}✅ npm全局包列表备份完成${NC}"
fi

# Python包
if command -v pip3 &> /dev/null; then
    pip3 list > "$BACKUP_DIR/Development/pip_packages.txt" 2>/dev/null
    log "${GREEN}✅ Python包列表备份完成${NC}"
fi

# 7. 创建系统信息快照
log "${BLUE}📊 创建系统信息快照...${NC}"
cat > "$BACKUP_DIR/system_info.txt" << EOF
macOS系统信息快照
================
备份时间: $(date)
系统版本: $(sw_vers -productName) $(sw_vers -productVersion) ($(sw_vers -buildVersion))
硬件信息: $(system_profiler SPHardwareDataType | grep "Model Name\|Processor Name\|Memory" | head -3)
磁盘信息: $(df -h / | tail -1)

已安装应用程序:
$(ls /Applications | head -20)

启动项:
$(osascript -e 'tell application "System Events" to get the name of every login item' 2>/dev/null || echo "无法获取启动项")

网络接口:
$(ifconfig | grep "inet " | grep -v 127.0.0.1)
EOF

# 8. 创建恢复脚本
cat > "$BACKUP_DIR/restore_system_settings.sh" << 'EOF'
#!/bin/bash
echo "🔄 macOS系统设置恢复..."
BACKUP_DIR="$(dirname "$0")"

echo "恢复系统偏好设置..."
if [ -d "$BACKUP_DIR/SystemPrefs" ]; then
    cp "$BACKUP_DIR/SystemPrefs/"*.plist "/Users/<USER>/Library/Preferences/" 2>/dev/null
    echo "✅ 系统偏好设置恢复完成"
fi

echo "恢复键盘设置..."
if [ -d "$BACKUP_DIR/Keyboard" ]; then
    cp "$BACKUP_DIR/Keyboard/"*.plist "/Users/<USER>/Library/Preferences/" 2>/dev/null
    echo "✅ 键盘设置恢复完成"
fi

echo "恢复应用程序设置..."
if [ -d "$BACKUP_DIR/Applications" ]; then
    cp "$BACKUP_DIR/Applications/"*.plist "/Users/<USER>/Library/Preferences/" 2>/dev/null
    echo "✅ 应用程序设置恢复完成"
fi

echo "恢复Shell配置..."
if [ -d "$BACKUP_DIR/Shell" ]; then
    cp -R "$BACKUP_DIR/Shell/".* "/Users/<USER>/" 2>/dev/null || true
    echo "✅ Shell配置恢复完成"
fi

echo "重启系统服务..."
killall Dock 2>/dev/null || true
killall Finder 2>/dev/null || true
killall SystemUIServer 2>/dev/null || true

echo "🎉 系统设置恢复完成！某些设置可能需要重启后生效"
EOF

chmod +x "$BACKUP_DIR/restore_system_settings.sh"

# 9. 创建Homebrew恢复脚本
if [ -f "$BACKUP_DIR/Development/homebrew_packages.txt" ]; then
    cat > "$BACKUP_DIR/restore_homebrew.sh" << 'EOF'
#!/bin/bash
echo "🍺 Homebrew软件恢复..."
BACKUP_DIR="$(dirname "$0")"

# 安装Homebrew（如果未安装）
if ! command -v brew &> /dev/null; then
    echo "安装Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# 恢复包
if [ -f "$BACKUP_DIR/Development/homebrew_packages.txt" ]; then
    echo "恢复Homebrew包..."
    cat "$BACKUP_DIR/Development/homebrew_packages.txt" | xargs brew install
fi

if [ -f "$BACKUP_DIR/Development/homebrew_casks.txt" ]; then
    echo "恢复Homebrew Casks..."
    cat "$BACKUP_DIR/Development/homebrew_casks.txt" | xargs brew install --cask
fi

echo "✅ Homebrew软件恢复完成"
EOF
    chmod +x "$BACKUP_DIR/restore_homebrew.sh"
fi

# 10. 统计和完成
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
PREF_COUNT=$(find "$BACKUP_DIR" -name "*.plist" | wc -l)

cat > "$BACKUP_DIR/backup_summary.txt" << EOF
macOS系统设置备份总结
==================
备份时间: $(date)
备份大小: $BACKUP_SIZE
偏好文件: $PREF_COUNT 个

恢复脚本:
- restore_system_settings.sh (系统设置)
- restore_homebrew.sh (开发环境)

重要提醒:
1. 网络设置恢复需要管理员权限
2. 某些设置需要重启后生效
3. SSH私钥未备份，需要手动处理
4. 建议在新系统上测试恢复脚本
EOF

echo -e "${GREEN}🎉 系统设置备份完成！${NC}"
echo -e "${YELLOW}📍 备份位置: $BACKUP_DIR${NC}"
echo -e "${YELLOW}📊 偏好文件: $PREF_COUNT 个${NC}"
echo -e "${YELLOW}💾 备份大小: $BACKUP_SIZE${NC}"

# 打开备份目录
open "$BACKUP_DIR"
