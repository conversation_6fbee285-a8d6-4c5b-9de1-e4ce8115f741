Version 1.3.6 - Feature Consolidation and Project Restructuring

ADDED:
- Consolidated extensive feature additions into version 1.3.6:
  - Complete MediaPoolItem and Folder object functionality
  - Cache Management implementation
  - Timeline Item Properties implementation 
  - Keyframe Control implementation
  - Color Preset Management implementation
  - LUT Export functionality

CHANGED:
- Major project directory restructuring:
  - Moved documentation files to docs/ directory
  - Moved test scripts to scripts/tests/ directory
  - Moved configuration templates to config-templates/ directory 
  - Moved utility scripts to scripts/ directory
  - Updated all scripts to work with the new directory structure
  - Created simpler launcher scripts in the root directory
- Updated version number to 1.3.6 across all relevant files
- Reorganized changelog to consolidate planned features
- Enhanced documentation for better readability

FIXED:
- Path references in scripts to ensure cross-platform compatibility
- Launcher scripts to use the correct paths after restructuring

feat: Comprehensive reliability enhancements, testing tools, and documentation

This commit introduces several key improvements to the DaVinci Resolve MCP Server:

1. Color Page Operations:
   - Added automatic clip selection with ensure_clip_selected helper function
   - Improved error handling for color operations
   - Enhanced error messages with actionable guidance
   - Added fallback mechanisms for clip selection

2. Render Queue Operations:
   - Added ensure_render_settings helper function
   - Created validate_render_preset for better preset validation
   - Implemented retry logic with progressive approaches
   - Enhanced error reporting throughout render operations

3. Parameter Validation:
   - Modified set_project_setting to accept Any type
   - Added type conversion between strings and numbers
   - Improved type validation with better error messages
   - Updated imports for modern type handling

4. Testing Tools:
   - Created test_improvements.py automated test script
   - Added create_test_timeline.py environment generator
   - Developed test-after-restart.sh/.bat combined testing scripts
   - Implemented cross-platform testing frameworks

5. Documentation:
   - Updated FEATURES.md with implementation status
   - Added detailed testing results and next steps
   - Created restart scripts for macOS/Linux and Windows
   - Added comprehensive troubleshooting guide
   - Created development best practices guide for future contributors

6. Server Management:
   - Created restart-server.sh and restart-server.bat for easier server management
   - Added automatic server detection and process management
   - Improved environment validation during server startup

These improvements significantly enhance the reliability and maintainability of the MCP server, with particular focus on automatic recovery from common error states, providing clear, actionable error messages to users, and ensuring proper testing methodology.

NOTE: The server must be restarted to apply these changes. 