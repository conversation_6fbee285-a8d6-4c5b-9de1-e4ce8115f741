# DaVinci Resolve macOS 完整恢复指南

## 📋 目录
1. [问题诊断](#问题诊断)
2. [数据备份方案](#数据备份方案)
3. [macOS重装方法](#macos重装方法)
4. [DaVinci Resolve恢复](#davinci-resolve恢复)
5. [数据库重建方法](#数据库重建方法)
6. [故障排除](#故障排除)
7. [预防措施](#预防措施)

---

## 🔍 问题诊断

### 常见DaVinci Resolve数据库问题
- ❌ "Failed to write to database folder" 错误
- ❌ 项目库无法加载或显示为空
- ❌ 数据库连接失败
- ❌ 缺少默认Local Database路径

### 快速诊断命令
```bash
# 检查DaVinci Resolve进程
ps aux | grep "DaVinci Resolve"

# 检查数据库路径
ls -la "/Library/Application Support/Blackmagic Design/DaVinci Resolve/"
ls -la "/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/"

# 检查权限
ls -ld "/Users/<USER>/Documents/"*剪辑工程*
```

---

## 💾 数据备份方案

### 🛡️ 完整备份脚本
```bash
#!/bin/bash
# DaVinci Resolve 完整备份脚本

BACKUP_DIR="/Users/<USER>/Desktop/DaVinci_Complete_Backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "=== DaVinci Resolve 完整备份开始 ==="

# 1. 备份项目数据
echo "1. 备份项目数据..."
cp -R "/Users/<USER>/Documents/2024_剪辑工程" "$BACKUP_DIR/2024_Projects" 2>/dev/null
cp -R "/Users/<USER>/Documents/2023_剪辑工程 3" "$BACKUP_DIR/2023_Projects" 2>/dev/null
cp -R "/Users/<USER>/Downloads/Resolve Project Library" "$BACKUP_DIR/Downloads_Projects" 2>/dev/null

# 2. 备份用户级配置
echo "2. 备份用户配置..."
cp -R "/Users/<USER>/Library/Preferences/Blackmagic Design" "$BACKUP_DIR/User_Preferences" 2>/dev/null
cp -R "/Users/<USER>/Library/Application Support/Blackmagic Design" "$BACKUP_DIR/User_AppSupport" 2>/dev/null

# 3. 备份系统级配置（需要sudo）
echo "3. 备份系统配置..."
sudo cp -R "/Library/Application Support/Blackmagic Design" "$BACKUP_DIR/System_AppSupport" 2>/dev/null
sudo cp -R "/Library/Preferences/Blackmagic Design" "$BACKUP_DIR/System_Preferences" 2>/dev/null

# 4. 创建备份清单
cat > "$BACKUP_DIR/backup_manifest.txt" << EOF
DaVinci Resolve 完整备份清单
创建时间: $(date)
备份路径: $BACKUP_DIR

包含内容:
✅ 2024年项目工程
✅ 2023年项目工程  
✅ 下载目录项目库
✅ 用户偏好设置
✅ 用户应用支持文件
✅ 系统级配置文件
✅ 系统级偏好设置

恢复说明:
1. 项目数据恢复到原路径
2. 配置文件恢复到Library目录
3. 重新启动DaVinci Resolve
EOF

echo "✅ 备份完成: $BACKUP_DIR"
open "$BACKUP_DIR"
```

### 📦 Time Machine备份设置
```bash
# 启用Time Machine
sudo tmutil enable

# 设置备份磁盘（替换为实际磁盘路径）
sudo tmutil setdestination /Volumes/BackupDisk

# 立即开始备份
sudo tmutil startbackup
```

---

## 🔄 macOS重装方法

### 方法一：Recovery重装（保留数据）⭐️ 推荐

#### Intel Mac步骤：
1. **进入Recovery模式**
   - 完全关机
   - 按电源键启动，立即按住 `Command + R`
   - 看到Apple标志或旋转地球后松开

2. **选择重装选项**
   - 选择"重新安装macOS"
   - ⚠️ **重要**: 选择"保留用户数据和设置"
   - ❌ **不要选择**: "抹掉磁盘"选项

#### Apple Silicon Mac步骤：
1. **进入Recovery模式**
   - 完全关机
   - 按住电源键10秒直到看到启动选项
   - 选择"选项" → "继续"

2. **重装系统**
   - 选择"重新安装macOS"
   - 保持"保留用户数据"选项开启

#### 重装过程注意事项：
- ⏱️ 耗时：1-4小时（取决于网速和硬件）
- 🌐 保持稳定网络连接
- 🔌 连接电源适配器
- 📱 准备Apple ID密码

### 方法二：Time Machine完整恢复

#### 前提条件：
- 有完整的Time Machine备份
- 备份包含问题出现前的状态

#### 恢复步骤：
1. **进入Recovery模式**（同上）
2. **选择"从Time Machine备份恢复"**
3. **连接备份磁盘**
4. **选择恢复点**
   - 选择问题出现前的最近备份
   - 确认备份完整性
5. **开始恢复**
   - 耗时：2-6小时
   - 不要中断过程

### 方法三：迁移助理恢复

#### 适用场景：
- 从另一台Mac恢复
- 从Time Machine备份迁移特定数据

#### 操作步骤：
```bash
# 打开迁移助理
open "/System/Library/CoreServices/Migration Assistant.app"
```

1. **选择传输方式**
   - 从Mac、Time Machine备份或启动磁盘
   - 从Windows PC
   - 从另一个磁盘

2. **选择传输内容**
   - ✅ 用户账户和设置
   - ✅ 应用程序
   - ✅ 文件和文件夹
   - ⚠️ 网络和其他设置（可选）

---

## 🎬 DaVinci Resolve恢复

### 重装后完整恢复流程

#### 1. 重新安装DaVinci Resolve
```bash
# 下载最新版本
open "https://www.blackmagicdesign.com/products/davinciresolve/"

# 或使用已有安装包
open "/Applications/DaVinci Resolve/DaVinci Resolve.app"
```

#### 2. 许可证激活
- 打开DaVinci Resolve
- 菜单：Help → License
- 输入许可证密钥
- 在线激活

#### 3. 恢复项目库连接
```bash
# 检查项目数据完整性
ls -la "/Users/<USER>/Documents/2024_剪辑工程/Valley/Resolve Project Library/"
ls -la "/Users/<USER>/Documents/2024_剪辑工程/Valley/Resolve Project Library/Resolve Projects/Users/<USER>/Projects/"

# 统计项目数量
find "/Users/<USER>/Documents/2024_剪辑工程/Valley/Resolve Project Library/Resolve Projects/Users/<USER>/Projects/" -name "*.resolve" | wc -l
```

#### 4. 数据库配置恢复
```bash
# 恢复数据库配置文件
PREFS_DIR="/Users/<USER>/Library/Preferences/Blackmagic Design/DaVinci Resolve"

# 创建标准数据库列表
cat > "$PREFS_DIR/dblist.conf" << 'EOF'
Local Database:/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/Resolve Disk Database::::DISK
Valley:/Users/<USER>/Documents/2024_剪辑工程/Valley/Resolve Project Library:*:::DISK
2024_Projects:/Users/<USER>/Documents/2024_剪辑工程/2024_达芬奇工程::::DISK
Valley_2024:/Users/<USER>/Documents/2023_剪辑工程 3/Valley/Resolve Project Library::::DISK
2024:/Users/<USER>/Documents/2023_剪辑工程 3/2023_达芬奇工程::::DISK
test:/Users/<USER>/Downloads/Resolve Project Library::::DISK
EOF

# 设置活动数据库
cat > "$PREFS_DIR/activedb.conf" << 'EOF'
disk*:Valley
network:2025_ProjectschachadeMac-mini.local
EOF
```

---

## 🗄️ 数据库重建方法

### 用户级Local Database重建（推荐）

#### 创建标准结构：
```bash
#!/bin/bash
# 用户级数据库重建脚本

USER_DB="/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/Resolve Disk Database"

echo "=== 用户级Local Database重建 ==="

# 1. 创建目录结构
mkdir -p "$USER_DB/Resolve Projects/Users/<USER>/Projects"
mkdir -p "$USER_DB/Resolve Projects/Users/<USER>/ProjectMetadataCache"
mkdir -p "$USER_DB/Resolve Projects/Users/<USER>/Configs"
mkdir -p "$USER_DB/Resolve Projects/Settings"

# 2. 创建数据库文件
touch "$USER_DB/Resolve Projects/Users/<USER>/User.db"

# 3. 设置权限
chmod -R 755 "$USER_DB"
chmod 644 "$USER_DB/Resolve Projects/Users/<USER>/User.db"

echo "✅ 用户级数据库重建完成"
ls -la "$USER_DB/Resolve Projects/Users/<USER>/"
```

### 系统级Local Database重建

#### 需要管理员权限：
```bash
#!/bin/bash
# 系统级数据库重建脚本（需要sudo）

SYSTEM_DB="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Resolve Disk Database"

echo "=== 系统级Local Database重建 ==="

# 1. 创建目录结构
sudo mkdir -p "$SYSTEM_DB/Resolve Projects/Users/<USER>/Projects"
sudo mkdir -p "$SYSTEM_DB/Resolve Projects/Users/<USER>/ProjectMetadataCache"
sudo mkdir -p "$SYSTEM_DB/Resolve Projects/Users/<USER>/Configs"
sudo mkdir -p "$SYSTEM_DB/Resolve Projects/Settings"

# 2. 创建数据库文件
sudo touch "$SYSTEM_DB/Resolve Projects/Users/<USER>/User.db"

# 3. 设置权限和所有权
sudo chmod -R 755 "$SYSTEM_DB"
sudo chmod 644 "$SYSTEM_DB/Resolve Projects/Users/<USER>/User.db"
sudo chown -R root:admin "$SYSTEM_DB"

echo "✅ 系统级数据库重建完成"
sudo ls -la "$SYSTEM_DB/Resolve Projects/Users/<USER>/"
```

---

## 🔧 故障排除

### 常见问题及解决方案

#### 问题1：权限错误
```bash
# 症状：Permission denied 或 Failed to write to database folder
# 解决方案：
sudo chown -R $USER:staff "/Users/<USER>/Documents/"*剪辑工程*
chmod -R 755 "/Users/<USER>/Documents/"*剪辑工程*
```

#### 问题2：数据库连接失败
```bash
# 症状：Cannot connect to database
# 解决方案：
# 1. 重置数据库配置
rm "/Users/<USER>/Library/Preferences/Blackmagic Design/DaVinci Resolve/config.dat"

# 2. 清理缓存
rm -rf "/Users/<USER>/Library/Caches/Blackmagic Design/DaVinci Resolve/"

# 3. 重启DaVinci Resolve
```

#### 问题3：项目库为空
```bash
# 症状：项目库显示但无项目
# 解决方案：
# 1. 检查项目文件完整性
find "/Users/<USER>/Documents/2024_剪辑工程/Valley/Resolve Project Library/" -name "*.resolve" -exec ls -la {} \;

# 2. 重建数据库索引
# 在DaVinci Resolve中：右键项目库 → Refresh
```

#### 问题4：macOS重装后应用程序无法启动
```bash
# 症状：应用程序损坏或无法验证
# 解决方案：
sudo xattr -rd com.apple.quarantine "/Applications/DaVinci Resolve/"
sudo codesign --force --deep --sign - "/Applications/DaVinci Resolve/DaVinci Resolve.app"
```

### 系统诊断命令集合

#### 完整系统检查：
```bash
#!/bin/bash
# DaVinci Resolve 系统诊断脚本

echo "=== DaVinci Resolve 系统诊断 ==="

# 1. 检查DaVinci Resolve进程
echo "1. 进程检查:"
ps aux | grep -i davinci | grep -v grep

# 2. 检查数据库路径
echo -e "\n2. 数据库路径检查:"
echo "用户级路径:"
ls -la "/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/" 2>/dev/null || echo "❌ 不存在"
echo "系统级路径:"
ls -la "/Library/Application Support/Blackmagic Design/DaVinci Resolve/" 2>/dev/null || echo "❌ 不存在"

# 3. 检查项目数据
echo -e "\n3. 项目数据检查:"
find "/Users/<USER>/Documents/" -name "*剪辑工程*" -type d 2>/dev/null | while read dir; do
    echo "📁 $dir"
    find "$dir" -name "*.resolve" 2>/dev/null | wc -l | xargs echo "   项目数量:"
done

# 4. 检查配置文件
echo -e "\n4. 配置文件检查:"
PREFS_DIR="/Users/<USER>/Library/Preferences/Blackmagic Design/DaVinci Resolve"
echo "配置目录: $PREFS_DIR"
ls -la "$PREFS_DIR/" 2>/dev/null || echo "❌ 配置目录不存在"

# 5. 检查磁盘空间
echo -e "\n5. 磁盘空间检查:"
df -h /Users /Library

# 6. 检查系统版本
echo -e "\n6. 系统信息:"
sw_vers
echo "DaVinci Resolve版本:"
defaults read "/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Info.plist" CFBundleShortVersionString 2>/dev/null || echo "未安装"

echo -e "\n✅ 诊断完成"
```

---

## 🛡️ 预防措施

### 定期备份策略

#### 1. 自动Time Machine备份
```bash
# 设置每小时备份
sudo tmutil enable
sudo tmutil setdestination /Volumes/BackupDisk
```

#### 2. 项目数据同步
```bash
# 使用rsync同步到外部存储
rsync -av --delete "/Users/<USER>/Documents/2024_剪辑工程/" "/Volumes/ExternalDrive/DaVinci_Projects_Backup/"
```

#### 3. 云存储备份
```bash
# 上传重要项目到云存储
# 注意：DaVinci项目文件较大，选择合适的云服务
```

### 系统维护建议

#### 定期清理：
```bash
# 清理DaVinci缓存（每月）
rm -rf "/Users/<USER>/Library/Caches/Blackmagic Design/DaVinci Resolve/"

# 清理系统缓存
sudo periodic daily weekly monthly

# 修复磁盘权限
sudo diskutil repairPermissions /
```

#### 监控系统健康：
```bash
# 检查磁盘健康
diskutil verifyVolume /

# 检查内存使用
vm_stat

# 检查CPU使用
top -l 1 | grep "CPU usage"
```

---

## 📞 紧急恢复联系方式

### Blackmagic Design官方支持
- 官方论坛: https://forum.blackmagicdesign.com/
- 技术支持: <EMAIL>
- 电话支持: 查看官网各地区联系方式

### 社区资源
- DaVinci Resolve用户群组
- 专业视频制作论坛
- YouTube教程频道

---

**⚠️ 重要提醒：在执行任何重装或恢复操作前，请确保已完成完整的数据备份！**

## 🚨 紧急恢复流程

### 数据丢失紧急处理

#### 立即执行步骤：
1. **停止使用系统** - 避免数据覆盖
2. **不要重启** - 保持当前状态
3. **立即备份** - 使用外部工具

#### 数据恢复工具：
```bash
# 使用PhotoRec恢复删除文件
brew install testdisk
photorec

# 使用Disk Drill（商业软件）
open "https://www.cleverfiles.com/disk-drill-mac.html"

# 使用Time Machine本地快照
tmutil listlocalsnapshots /
tmutil restore -v /path/to/restore
```

### 系统无法启动处理

#### 单用户模式修复：
```bash
# 启动时按住 Command + S
# 进入单用户模式后执行：

# 检查文件系统
/sbin/fsck -fy

# 挂载根分区为可写
/sbin/mount -uw /

# 修复权限
/usr/sbin/diskutil repairPermissions /

# 重建启动缓存
/usr/bin/update_dyld_shared_cache -force

# 重启
reboot
```

#### 目标磁盘模式：
1. **关机状态下按住 T 键启动**
2. **连接到另一台Mac**
3. **作为外部磁盘访问数据**
4. **复制重要文件到安全位置**

---

## 🔬 高级故障排除

### DaVinci Resolve崩溃分析

#### 崩溃日志位置：
```bash
# 系统崩溃报告
~/Library/Logs/DiagnosticReports/DaVinci*

# Console日志
/var/log/system.log

# DaVinci特定日志
~/Library/Logs/Blackmagic Design/DaVinci Resolve/
```

#### 崩溃日志分析脚本：
```bash
#!/bin/bash
# DaVinci崩溃日志分析

echo "=== DaVinci Resolve 崩溃分析 ==="

# 1. 查找最近的崩溃报告
echo "1. 最近崩溃报告:"
find ~/Library/Logs/DiagnosticReports/ -name "*DaVinci*" -mtime -7 -exec ls -la {} \;

# 2. 分析崩溃原因
echo -e "\n2. 常见崩溃原因分析:"
if ls ~/Library/Logs/DiagnosticReports/*DaVinci* 1> /dev/null 2>&1; then
    grep -i "exception\|error\|crash" ~/Library/Logs/DiagnosticReports/*DaVinci* | head -10
fi

# 3. 检查系统资源
echo -e "\n3. 系统资源检查:"
echo "内存使用:"
vm_stat | grep "Pages free\|Pages active\|Pages inactive"
echo "磁盘空间:"
df -h / | tail -1

# 4. 检查GPU状态
echo -e "\n4. GPU状态:"
system_profiler SPDisplaysDataType | grep -A5 "Chipset Model"
```

### 数据库损坏修复

#### SQLite数据库修复：
```bash
#!/bin/bash
# DaVinci数据库修复脚本

DB_PATH="/Users/<USER>/Documents/2024_剪辑工程/Valley/Resolve Project Library/Resolve Projects/Users/<USER>/User.db"

if [ -f "$DB_PATH" ]; then
    echo "=== 数据库完整性检查 ==="

    # 1. 备份原数据库
    cp "$DB_PATH" "$DB_PATH.backup.$(date +%Y%m%d_%H%M%S)"

    # 2. 检查数据库完整性
    sqlite3 "$DB_PATH" "PRAGMA integrity_check;"

    # 3. 修复数据库（如果损坏）
    if [ $? -ne 0 ]; then
        echo "数据库损坏，尝试修复..."
        sqlite3 "$DB_PATH" ".recover" | sqlite3 "$DB_PATH.recovered"

        # 验证修复结果
        if sqlite3 "$DB_PATH.recovered" "PRAGMA integrity_check;" | grep -q "ok"; then
            echo "✅ 数据库修复成功"
            mv "$DB_PATH.recovered" "$DB_PATH"
        else
            echo "❌ 数据库修复失败，请使用备份"
        fi
    else
        echo "✅ 数据库完整性正常"
    fi
else
    echo "❌ 数据库文件不存在: $DB_PATH"
fi
```

### 网络数据库问题

#### PostgreSQL数据库修复：
```bash
# 如果使用PostgreSQL数据库
# 检查PostgreSQL服务状态
brew services list | grep postgresql

# 重启PostgreSQL服务
brew services restart postgresql

# 连接测试
psql -h localhost -U postgres -d resolve

# 数据库修复
psql -h localhost -U postgres -c "REINDEX DATABASE resolve;"
```

---

## 🔄 自动化脚本集合

### 一键备份脚本

#### 完整自动备份：
```bash
#!/bin/bash
# DaVinci_Auto_Backup.sh - 一键完整备份脚本

set -e  # 遇到错误立即退出

# 配置变量
BACKUP_ROOT="/Volumes/BackupDrive/DaVinci_Backups"
DATE_STAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_ROOT/Backup_$DATE_STAMP"
LOG_FILE="$BACKUP_DIR/backup.log"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "=== DaVinci Resolve 自动备份开始 ==="

# 1. 检查磁盘空间
AVAILABLE_SPACE=$(df -g "$BACKUP_ROOT" | tail -1 | awk '{print $4}')
if [ "$AVAILABLE_SPACE" -lt 50 ]; then
    log "❌ 警告：备份磁盘空间不足50GB，当前可用：${AVAILABLE_SPACE}GB"
    exit 1
fi

# 2. 停止DaVinci Resolve（如果运行中）
if pgrep -f "DaVinci Resolve" > /dev/null; then
    log "⏸️ 检测到DaVinci Resolve运行中，建议手动关闭后继续"
    read -p "按Enter继续，或Ctrl+C取消..."
fi

# 3. 备份项目数据
log "📁 备份项目数据..."
rsync -av --progress "/Users/<USER>/Documents/2024_剪辑工程/" "$BACKUP_DIR/Projects_2024/" 2>&1 | tee -a "$LOG_FILE"
rsync -av --progress "/Users/<USER>/Documents/2023_剪辑工程 3/" "$BACKUP_DIR/Projects_2023/" 2>&1 | tee -a "$LOG_FILE"

# 4. 备份配置文件
log "⚙️ 备份配置文件..."
cp -R "/Users/<USER>/Library/Preferences/Blackmagic Design" "$BACKUP_DIR/User_Preferences/" 2>&1 | tee -a "$LOG_FILE"
cp -R "/Users/<USER>/Library/Application Support/Blackmagic Design" "$BACKUP_DIR/User_AppSupport/" 2>&1 | tee -a "$LOG_FILE"

# 5. 备份系统级配置
log "🔧 备份系统配置..."
sudo cp -R "/Library/Application Support/Blackmagic Design" "$BACKUP_DIR/System_AppSupport/" 2>&1 | tee -a "$LOG_FILE"

# 6. 创建备份清单
log "📋 创建备份清单..."
find "$BACKUP_DIR" -type f -exec ls -lah {} \; > "$BACKUP_DIR/file_list.txt"

# 7. 计算备份大小
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
log "✅ 备份完成！总大小：$BACKUP_SIZE"

# 8. 创建快速恢复脚本
cat > "$BACKUP_DIR/quick_restore.sh" << 'EOF'
#!/bin/bash
# 快速恢复脚本
echo "=== DaVinci Resolve 快速恢复 ==="
BACKUP_DIR="$(dirname "$0")"

# 恢复项目数据
echo "恢复项目数据..."
rsync -av "$BACKUP_DIR/Projects_2024/" "/Users/<USER>/Documents/2024_剪辑工程/"
rsync -av "$BACKUP_DIR/Projects_2023/" "/Users/<USER>/Documents/2023_剪辑工程 3/"

# 恢复配置
echo "恢复配置文件..."
cp -R "$BACKUP_DIR/User_Preferences/" "/Users/<USER>/Library/Preferences/Blackmagic Design"
cp -R "$BACKUP_DIR/User_AppSupport/" "/Users/<USER>/Library/Application Support/Blackmagic Design"

echo "✅ 快速恢复完成！请重启DaVinci Resolve"
EOF

chmod +x "$BACKUP_DIR/quick_restore.sh"

# 9. 清理旧备份（保留最近10个）
log "🧹 清理旧备份..."
ls -t "$BACKUP_ROOT" | tail -n +11 | xargs -I {} rm -rf "$BACKUP_ROOT/{}"

log "🎉 自动备份流程完成！"
log "📍 备份位置：$BACKUP_DIR"
log "🔄 恢复脚本：$BACKUP_DIR/quick_restore.sh"

# 打开备份目录
open "$BACKUP_DIR"
```

### 系统健康监控脚本

#### 实时监控：
```bash
#!/bin/bash
# DaVinci_Health_Monitor.sh - 系统健康监控

# 配置
CHECK_INTERVAL=300  # 5分钟检查一次
LOG_FILE="/Users/<USER>/Desktop/davinci_health.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

while true; do
    log "=== 系统健康检查 ==="

    # 1. 检查DaVinci进程
    if pgrep -f "DaVinci Resolve" > /dev/null; then
        DAVINCI_PID=$(pgrep -f "DaVinci Resolve")
        DAVINCI_CPU=$(ps -p $DAVINCI_PID -o %cpu= | xargs)
        DAVINCI_MEM=$(ps -p $DAVINCI_PID -o %mem= | xargs)
        log "✅ DaVinci运行中 PID:$DAVINCI_PID CPU:${DAVINCI_CPU}% MEM:${DAVINCI_MEM}%"

        # CPU使用率过高警告
        if (( $(echo "$DAVINCI_CPU > 80" | bc -l) )); then
            log "⚠️ 警告：DaVinci CPU使用率过高：${DAVINCI_CPU}%"
        fi
    else
        log "ℹ️ DaVinci未运行"
    fi

    # 2. 检查磁盘空间
    DISK_USAGE=$(df -h / | tail -1 | awk '{print $5}' | sed 's/%//')
    log "💾 磁盘使用率：${DISK_USAGE}%"

    if [ "$DISK_USAGE" -gt 90 ]; then
        log "🚨 严重警告：磁盘空间不足！使用率：${DISK_USAGE}%"
    elif [ "$DISK_USAGE" -gt 80 ]; then
        log "⚠️ 警告：磁盘空间紧张，使用率：${DISK_USAGE}%"
    fi

    # 3. 检查内存使用
    MEMORY_PRESSURE=$(memory_pressure | grep "System-wide memory free percentage" | awk '{print $5}' | sed 's/%//')
    log "🧠 系统内存空闲：${MEMORY_PRESSURE}%"

    if [ "$MEMORY_PRESSURE" -lt 10 ]; then
        log "🚨 严重警告：内存不足！空闲：${MEMORY_PRESSURE}%"
    fi

    # 4. 检查数据库连接
    DB_PATHS=(
        "/Users/<USER>/Documents/2024_剪辑工程/Valley/Resolve Project Library/Resolve Projects/Users/<USER>/User.db"
        "/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/Resolve Disk Database/Resolve Projects/Users/<USER>/User.db"
    )

    for DB_PATH in "${DB_PATHS[@]}"; do
        if [ -f "$DB_PATH" ]; then
            if sqlite3 "$DB_PATH" "PRAGMA integrity_check;" | grep -q "ok"; then
                log "✅ 数据库完整性正常：$(basename $(dirname $(dirname $(dirname "$DB_PATH"))))"
            else
                log "❌ 数据库损坏：$DB_PATH"
            fi
        fi
    done

    log "--- 检查完成，${CHECK_INTERVAL}秒后再次检查 ---"
    sleep $CHECK_INTERVAL
done
```

---

## 📱 移动设备集成

### iPad/iPhone项目同步

#### AirDrop传输脚本：
```bash
#!/bin/bash
# 准备移动设备传输的项目文件

PROJECT_NAME="$1"
if [ -z "$PROJECT_NAME" ]; then
    echo "用法: $0 <项目名称>"
    exit 1
fi

# 查找项目文件
PROJECT_FILE=$(find "/Users/<USER>/Documents" -name "*$PROJECT_NAME*.drp" -o -name "*$PROJECT_NAME*.resolve" | head -1)

if [ -n "$PROJECT_FILE" ]; then
    # 创建传输包
    TRANSFER_DIR="/Users/<USER>/Desktop/DaVinci_Mobile_Transfer"
    mkdir -p "$TRANSFER_DIR"

    # 复制项目文件和相关媒体
    cp "$PROJECT_FILE" "$TRANSFER_DIR/"

    # 打开传输目录
    open "$TRANSFER_DIR"
    echo "✅ 项目已准备好传输：$TRANSFER_DIR"
else
    echo "❌ 未找到项目：$PROJECT_NAME"
fi
```

---

## 🌐 网络和云存储集成

### 云备份自动化

#### iCloud同步脚本：
```bash
#!/bin/bash
# iCloud自动同步DaVinci项目

ICLOUD_DIR="/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/DaVinci_Projects"
LOCAL_PROJECTS="/Users/<USER>/Documents/2024_剪辑工程"

# 创建iCloud目录
mkdir -p "$ICLOUD_DIR"

# 同步项目文件（仅.drp和.resolve文件）
find "$LOCAL_PROJECTS" -name "*.drp" -o -name "*.resolve" | while read file; do
    relative_path=${file#$LOCAL_PROJECTS/}
    target_dir="$ICLOUD_DIR/$(dirname "$relative_path")"
    mkdir -p "$target_dir"
    cp "$file" "$target_dir/"
done

echo "✅ 项目文件已同步到iCloud"
```

#### Dropbox备份：
```bash
#!/bin/bash
# Dropbox自动备份

DROPBOX_DIR="/Users/<USER>/Dropbox/DaVinci_Backup"
mkdir -p "$DROPBOX_DIR"

# 使用rclone同步（需要先安装配置rclone）
if command -v rclone &> /dev/null; then
    rclone sync "/Users/<USER>/Documents/2024_剪辑工程" "dropbox:DaVinci_Projects" --progress
    echo "✅ 已同步到Dropbox"
else
    echo "❌ 请先安装rclone: brew install rclone"
fi
```

---

## 🔐 安全和权限管理

### 权限修复脚本

#### 完整权限重置：
```bash
#!/bin/bash
# 权限修复脚本

echo "=== DaVinci Resolve 权限修复 ==="

# 1. 修复用户目录权限
echo "1. 修复用户目录权限..."
sudo chown -R $USER:staff "/Users/<USER>/Documents/"
chmod -R 755 "/Users/<USER>/Documents/"

# 2. 修复Library权限
echo "2. 修复Library权限..."
sudo chown -R $USER:staff "/Users/<USER>/Library/Preferences/Blackmagic Design"
sudo chown -R $USER:staff "/Users/<USER>/Library/Application Support/Blackmagic Design"
chmod -R 755 "/Users/<USER>/Library/Preferences/Blackmagic Design"
chmod -R 755 "/Users/<USER>/Library/Application Support/Blackmagic Design"

# 3. 修复系统级权限
echo "3. 修复系统级权限..."
sudo chown -R root:admin "/Library/Application Support/Blackmagic Design"
sudo chmod -R 755 "/Library/Application Support/Blackmagic Design"

# 4. 修复应用程序权限
echo "4. 修复应用程序权限..."
sudo chown -R root:admin "/Applications/DaVinci Resolve"
sudo chmod -R 755 "/Applications/DaVinci Resolve"

# 5. 清除扩展属性
echo "5. 清除扩展属性..."
sudo xattr -rc "/Applications/DaVinci Resolve"
sudo xattr -rc "/Users/<USER>/Library/Preferences/Blackmagic Design"

echo "✅ 权限修复完成"
```

### 安全备份验证

#### 备份完整性检查：
```bash
#!/bin/bash
# 备份完整性验证脚本

BACKUP_DIR="$1"
if [ -z "$BACKUP_DIR" ]; then
    echo "用法: $0 <备份目录路径>"
    exit 1
fi

echo "=== 备份完整性验证 ==="

# 1. 检查备份目录结构
echo "1. 检查目录结构..."
REQUIRED_DIRS=("Projects_2024" "Projects_2023" "User_Preferences" "User_AppSupport")
for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$BACKUP_DIR/$dir" ]; then
        echo "✅ $dir"
    else
        echo "❌ $dir 缺失"
    fi
done

# 2. 验证项目文件数量
echo -e "\n2. 验证项目文件..."
ORIGINAL_COUNT=$(find "/Users/<USER>/Documents/2024_剪辑工程" -name "*.resolve" | wc -l)
BACKUP_COUNT=$(find "$BACKUP_DIR/Projects_2024" -name "*.resolve" | wc -l)
echo "原始项目数量: $ORIGINAL_COUNT"
echo "备份项目数量: $BACKUP_COUNT"

if [ "$ORIGINAL_COUNT" -eq "$BACKUP_COUNT" ]; then
    echo "✅ 项目文件数量匹配"
else
    echo "⚠️ 项目文件数量不匹配"
fi

# 3. 检查文件完整性（MD5校验）
echo -e "\n3. 文件完整性校验..."
find "$BACKUP_DIR" -type f -name "*.resolve" | head -5 | while read file; do
    if [ -f "$file" ]; then
        md5 "$file" | awk '{print $4, $2}' >> "$BACKUP_DIR/checksums.md5"
    fi
done

echo "✅ 完整性验证完成，校验文件：$BACKUP_DIR/checksums.md5"
```

---

*文档版本: v2.0 | 创建日期: 2025-07-31 | 最后更新: 2025-07-31 | 适用系统: macOS 10.15+*

**🎯 使用建议：**
1. **紧急情况**：直接跳转到"紧急恢复流程"章节
2. **预防性维护**：使用"自动化脚本集合"中的监控脚本
3. **定期备份**：运行一键备份脚本，建议每周执行
4. **系统重装**：按照"macOS重装方法"章节的详细步骤执行
5. **故障排除**：使用诊断脚本快速定位问题

**⚠️ 安全提醒：所有涉及sudo的操作都需要管理员权限，请确保理解每个命令的作用后再执行！**
