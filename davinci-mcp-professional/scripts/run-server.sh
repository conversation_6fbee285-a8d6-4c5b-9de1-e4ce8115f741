#!/bin/bash
# Wrapper script to run the DaVinci Resolve MCP Server with the virtual environment

# Source environment variables if not already set
if [ -z "$RESOLVE_SCRIPT_API" ]; then
  source "/Users/<USER>/.zshrc"
fi

# Activate virtual environment and run server
"/Users/<USER>/davinci-resolve-mcp-20250326/scripts/venv/bin/python" "/Users/<USER>/davinci-resolve-mcp-20250326/scripts/resolve_mcp_server.py" "$@"
