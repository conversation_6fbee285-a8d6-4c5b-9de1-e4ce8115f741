{"mcpServers": {"MiniMax": {"command": "uvx", "args": ["minimax-mcp", "-y"], "env": {"MINIMAX_API_KEY": "insert-your-api-key-here", "MINIMAX_MCP_BASE_PATH": "local-output-dir-path, such as /User/xxx/Desktop", "MINIMAX_API_HOST": "api host, https://api.minimax.io | https://api.minimaxi.com", "MINIMAX_API_RESOURCE_MODE": "optional, [url|local], url is default, audio/image/video are downloaded locally or provided in URL format"}}}}