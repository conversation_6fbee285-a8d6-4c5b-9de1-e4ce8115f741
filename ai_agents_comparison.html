<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 2025年开源AI Agent大对比 - DaVinci MCP兼容性分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .filter-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-tab {
            padding: 10px 20px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .filter-tab:hover, .filter-tab.active {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .agent-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        
        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .agent-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800);
        }
        
        .agent-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .agent-icon {
            font-size: 2.5rem;
            margin-right: 15px;
        }
        
        .agent-title {
            flex: 1;
        }
        
        .agent-title h3 {
            font-size: 1.5rem;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .agent-title .stars {
            color: #f39c12;
            font-size: 0.9rem;
        }
        
        .agent-badges {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .badge.free { background: #e8f5e8; color: #2e7d32; }
        .badge.paid { background: #fff3e0; color: #f57c00; }
        .badge.local { background: #e3f2fd; color: #1976d2; }
        .badge.cloud { background: #fce4ec; color: #c2185b; }
        .badge.mcp-yes { background: #e8f5e8; color: #2e7d32; }
        .badge.mcp-no { background: #ffebee; color: #d32f2f; }
        .badge.mcp-partial { background: #fff8e1; color: #f57c00; }
        
        .agent-description {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #555;
        }
        
        .agent-features {
            margin-bottom: 20px;
        }
        
        .agent-features h4 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 1rem;
        }
        
        .features-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .feature-tag {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            color: #495057;
            border: 1px solid #e9ecef;
        }
        
        .agent-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }
        
        .davinci-compatibility {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 15px;
        }
        
        .davinci-compatibility h4 {
            margin-bottom: 8px;
            font-size: 1rem;
        }
        
        .compatibility-score {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .score-bar {
            flex: 1;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .score-fill {
            height: 100%;
            background: white;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .agent-links {
            display: flex;
            gap: 10px;
        }
        
        .agent-link {
            flex: 1;
            padding: 10px;
            text-align: center;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .link-github {
            background: #24292e;
            color: white;
        }
        
        .link-demo {
            background: #007bff;
            color: white;
        }
        
        .link-docs {
            background: #28a745;
            color: white;
        }
        
        .agent-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .agents-grid {
                grid-template-columns: 1fr;
            }
            
            .agent-stats {
                grid-template-columns: 1fr;
            }
            
            .agent-links {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 2025年开源AI Agent大对比</h1>
            <p>专注DaVinci MCP兼容性 | 本地执行能力 | 费用分析</p>
        </div>
        
        <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all">全部</button>
            <button class="filter-tab" data-filter="free">免费</button>
            <button class="filter-tab" data-filter="local">本地执行</button>
            <button class="filter-tab" data-filter="mcp">MCP支持</button>
            <button class="filter-tab" data-filter="coding">编程助手</button>
        </div>
        
        <div class="agents-grid">
            <!-- Claude Desktop -->
            <div class="agent-card" data-category="paid mcp">
                <div class="agent-header">
                    <div class="agent-icon">🧠</div>
                    <div class="agent-title">
                        <h3>Claude Desktop</h3>
                        <div class="stars">⭐ Anthropic官方</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge free">免费版</span>
                    <span class="badge paid">Pro $20/月</span>
                    <span class="badge cloud">云端</span>
                    <span class="badge mcp-yes">原生MCP</span>
                </div>

                <div class="agent-description">
                    Anthropic官方桌面应用，原生支持MCP协议，是目前最稳定的MCP客户端，完美兼容您的DaVinci MCP Professional。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">原生MCP支持</span>
                        <span class="feature-tag">Claude 3.5 Sonnet</span>
                        <span class="feature-tag">桌面应用</span>
                        <span class="feature-tag">文件上传</span>
                        <span class="feature-tag">代码执行</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">$0-20</div>
                        <div class="stat-label">月费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 100%"></div>
                        </div>
                        <span>100% - 完美兼容</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://claude.ai/download" class="agent-link link-demo">下载</a>
                    <a href="https://docs.anthropic.com/claude/docs" class="agent-link link-docs">文档</a>
                </div>
            </div>

            <!-- Open Interpreter -->
            <div class="agent-card" data-category="free local coding">
                <div class="agent-header">
                    <div class="agent-icon">🔓</div>
                    <div class="agent-title">
                        <h3>Open Interpreter</h3>
                        <div class="stars">⭐ 60.1k stars</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge free">完全免费</span>
                    <span class="badge local">本地执行</span>
                    <span class="badge mcp-no">无MCP</span>
                </div>

                <div class="agent-description">
                    自然语言计算机接口，让LLM在本地执行代码（Python、JavaScript、Shell等）。类似ChatGPT Code Interpreter但完全本地运行。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">本地代码执行</span>
                        <span class="feature-tag">多语言支持</span>
                        <span class="feature-tag">文件操作</span>
                        <span class="feature-tag">数据分析</span>
                        <span class="feature-tag">浏览器控制</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">免费</div>
                        <div class="stat-label">费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">100%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 30%"></div>
                        </div>
                        <span>30% - 需要自定义开发</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://github.com/openinterpreter/open-interpreter" class="agent-link link-github">GitHub</a>
                    <a href="https://docs.openinterpreter.com/" class="agent-link link-docs">文档</a>
                </div>
            </div>

            <!-- Windsurf -->
            <div class="agent-card" data-category="free paid mcp coding">
                <div class="agent-header">
                    <div class="agent-icon">🏄</div>
                    <div class="agent-title">
                        <h3>Windsurf</h3>
                        <div class="stars">⭐ Codeium出品</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge free">免费版</span>
                    <span class="badge paid">Pro $10/月</span>
                    <span class="badge cloud">云端</span>
                    <span class="badge mcp-yes">MCP支持</span>
                </div>

                <div class="agent-description">
                    Codeium推出的AI IDE，性价比最高的MCP支持方案，提供免费版本和低价Pro版本。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">AI编程助手</span>
                        <span class="feature-tag">代码补全</span>
                        <span class="feature-tag">MCP集成</span>
                        <span class="feature-tag">多语言支持</span>
                        <span class="feature-tag">免费版本</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">$0-10</div>
                        <div class="stat-label">月费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 90%"></div>
                        </div>
                        <span>90% - 良好兼容</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://codeium.com/windsurf" class="agent-link link-demo">官网</a>
                    <a href="https://codeium.com/windsurf/docs" class="agent-link link-docs">文档</a>
                </div>
            </div>

            <!-- Cursor -->
            <div class="agent-card" data-category="paid mcp coding">
                <div class="agent-header">
                    <div class="agent-icon">🎯</div>
                    <div class="agent-title">
                        <h3>Cursor</h3>
                        <div class="stars">⭐ 热门AI IDE</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge paid">$20/月</span>
                    <span class="badge cloud">云端</span>
                    <span class="badge mcp-yes">MCP支持</span>
                </div>

                <div class="agent-description">
                    AI驱动的代码编辑器，基于VS Code构建，支持MCP协议，提供强大的代码生成和编辑能力。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">AI代码生成</span>
                        <span class="feature-tag">智能补全</span>
                        <span class="feature-tag">代码重构</span>
                        <span class="feature-tag">MCP集成</span>
                        <span class="feature-tag">多模型支持</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">$20</div>
                        <div class="stat-label">月费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 95%"></div>
                        </div>
                        <span>95% - 优秀兼容</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://cursor.sh/" class="agent-link link-demo">官网</a>
                    <a href="https://cursor.directory/mcp" class="agent-link link-docs">MCP文档</a>
                </div>
            </div>

            <!-- AutoGPT -->
            <div class="agent-card" data-category="free local">
                <div class="agent-header">
                    <div class="agent-icon">🚀</div>
                    <div class="agent-title">
                        <h3>AutoGPT</h3>
                        <div class="stars">⭐ 177k stars</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge free">完全免费</span>
                    <span class="badge local">本地执行</span>
                    <span class="badge mcp-no">无MCP</span>
                </div>

                <div class="agent-description">
                    最著名的自主AI代理，可以自动分解任务、执行计划、学习和改进，目标是让AI对所有人都可用。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">自主任务执行</span>
                        <span class="feature-tag">目标分解</span>
                        <span class="feature-tag">记忆管理</span>
                        <span class="feature-tag">文件操作</span>
                        <span class="feature-tag">网络搜索</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">免费</div>
                        <div class="stat-label">费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">90%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 20%"></div>
                        </div>
                        <span>20% - 需要大量开发</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://github.com/Significant-Gravitas/AutoGPT" class="agent-link link-github">GitHub</a>
                    <a href="https://agpt.co/" class="agent-link link-demo">官网</a>
                </div>
            </div>

            <!-- CrewAI -->
            <div class="agent-card" data-category="free local">
                <div class="agent-header">
                    <div class="agent-icon">👥</div>
                    <div class="agent-title">
                        <h3>CrewAI</h3>
                        <div class="stars">⭐ 34.7k stars</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge free">完全免费</span>
                    <span class="badge local">本地执行</span>
                    <span class="badge mcp-no">无MCP</span>
                </div>

                <div class="agent-description">
                    多代理协作框架，让多个AI代理像团队一样协作完成复杂任务，每个代理都有特定的角色和目标。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">多代理协作</span>
                        <span class="feature-tag">角色分工</span>
                        <span class="feature-tag">任务编排</span>
                        <span class="feature-tag">工具集成</span>
                        <span class="feature-tag">流程管理</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">免费</div>
                        <div class="stat-label">费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">100%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 50%"></div>
                        </div>
                        <span>50% - 可以集成</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://github.com/joaomdmoura/crewAI" class="agent-link link-github">GitHub</a>
                    <a href="https://crewai.com/" class="agent-link link-docs">官网</a>
                </div>
            </div>

            <!-- Goose Agent -->
            <div class="agent-card" data-category="free local coding">
                <div class="agent-header">
                    <div class="agent-icon">🪿</div>
                    <div class="agent-title">
                        <h3>Goose Agent</h3>
                        <div class="stars">⭐ Block出品</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge free">完全免费</span>
                    <span class="badge local">本地执行</span>
                    <span class="badge mcp-yes">MCP支持</span>
                </div>

                <div class="agent-description">
                    Block公司开源的本地AI代理，专注工程任务自动化，支持MCP协议，可以运行代码、操作文件、与系统交互。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">工程任务自动化</span>
                        <span class="feature-tag">MCP协议支持</span>
                        <span class="feature-tag">代码执行</span>
                        <span class="feature-tag">系统交互</span>
                        <span class="feature-tag">Docker集成</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">免费</div>
                        <div class="stat-label">费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">100%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 85%"></div>
                        </div>
                        <span>85% - 优秀兼容</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://block.github.io/goose/" class="agent-link link-demo">官网</a>
                    <a href="https://github.com/block/goose" class="agent-link link-github">GitHub</a>
                </div>
            </div>

            <!-- MCP Agent -->
            <div class="agent-card" data-category="free mcp coding">
                <div class="agent-header">
                    <div class="agent-icon">🔗</div>
                    <div class="agent-title">
                        <h3>MCP Agent</h3>
                        <div class="stars">⭐ LastMile AI</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge free">开源免费</span>
                    <span class="badge local">本地+云端</span>
                    <span class="badge mcp-yes">原生MCP</span>
                </div>

                <div class="agent-description">
                    专门为MCP协议设计的代理框架，提供简单的工作流模式，支持代理间通信和协作，完美集成MCP生态。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">原生MCP设计</span>
                        <span class="feature-tag">代理间通信</span>
                        <span class="feature-tag">工作流模式</span>
                        <span class="feature-tag">Gmail集成</span>
                        <span class="feature-tag">多协议支持</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">免费</div>
                        <div class="stat-label">费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">70%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 95%"></div>
                        </div>
                        <span>95% - 优秀兼容</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://github.com/lastmile-ai/mcp-agent" class="agent-link link-github">GitHub</a>
                    <a href="https://arxiv.org/abs/2505.02279" class="agent-link link-docs">论文</a>
                </div>
            </div>

            <!-- Augment -->
            <div class="agent-card" data-category="paid mcp coding">
                <div class="agent-header">
                    <div class="agent-icon">🚀</div>
                    <div class="agent-title">
                        <h3>Augment</h3>
                        <div class="stars">⭐ ISO 42001认证</div>
                    </div>
                </div>

                <div class="agent-badges">
                    <span class="badge paid">企业级</span>
                    <span class="badge cloud">云端</span>
                    <span class="badge mcp-yes">MCP支持</span>
                </div>

                <div class="agent-description">
                    首个获得ISO/IEC 42001认证的AI编程助手，专为真实软件开发设计，支持MCP协议和100+外部工具集成。
                </div>

                <div class="agent-features">
                    <h4>核心功能</h4>
                    <div class="features-list">
                        <span class="feature-tag">ISO 42001认证</span>
                        <span class="feature-tag">企业级安全</span>
                        <span class="feature-tag">MCP协议</span>
                        <span class="feature-tag">终端集成</span>
                        <span class="feature-tag">100+工具</span>
                    </div>
                </div>

                <div class="agent-stats">
                    <div class="stat-item">
                        <div class="stat-value">企业定价</div>
                        <div class="stat-label">费用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">20%</div>
                        <div class="stat-label">本地执行</div>
                    </div>
                </div>

                <div class="davinci-compatibility">
                    <h4>🎬 DaVinci兼容性</h4>
                    <div class="compatibility-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: 98%"></div>
                        </div>
                        <span>98% - 近乎完美</span>
                    </div>
                </div>

                <div class="agent-links">
                    <a href="https://www.augmentcode.com/" class="agent-link link-demo">官网</a>
                    <a href="https://www.augmentcode.com/blog" class="agent-link link-docs">博客</a>
                </div>
            </div>
        </div>

        <!-- 对比表格和推荐部分 -->
        <div style="background: rgba(255,255,255,0.95); border-radius: 20px; padding: 30px; margin-top: 40px; backdrop-filter: blur(10px); box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
            <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50; font-size: 2rem;">📊 详细对比表</h2>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 15px; text-align: left; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #2c3e50;">Agent</th>
                            <th style="padding: 15px; text-align: left; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #2c3e50;">费用</th>
                            <th style="padding: 15px; text-align: left; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #2c3e50;">本地执行</th>
                            <th style="padding: 15px; text-align: left; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #2c3e50;">MCP支持</th>
                            <th style="padding: 15px; text-align: left; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #2c3e50;">DaVinci兼容性</th>
                            <th style="padding: 15px; text-align: left; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #2c3e50;">推荐指数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>Claude Desktop</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">$0-20/月</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">❌</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅ 原生</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟢 100%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>Windsurf</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">$0-10/月</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">❌</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅ 支持</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟢 90%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>Cursor</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">$20/月</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">❌</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅ 支持</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟢 95%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>Open Interpreter</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">免费</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">❌</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟡 30%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>AutoGPT</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">免费</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">❌</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🔴 20%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>CrewAI</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">免费</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">❌</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟡 50%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>Goose Agent</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">免费</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅ 支持</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟢 85%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>MCP Agent</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">免费</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⚠️ 部分</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅ 原生</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟢 95%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr style="transition: background 0.3s;">
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;"><strong>Augment</strong></td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">企业定价</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⚠️ 部分</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">✅ 支持</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">🟢 98%</td>
                            <td style="padding: 15px; border-bottom: 1px solid #e9ecef;">⭐⭐⭐⭐⭐</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 推荐部分 -->
        <div style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 30px; border-radius: 20px; margin-top: 40px; text-align: center;">
            <h2 style="margin-bottom: 20px; font-size: 2rem;">🎯 针对DaVinci远程操控的推荐</h2>
            <p style="font-size: 1.1rem; line-height: 1.6; margin-bottom: 15px;">基于您已有的DaVinci MCP Professional（83个工具），以下是最佳选择：</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px;">
                <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 15px; backdrop-filter: blur(10px);">
                    <h3 style="margin-bottom: 10px; font-size: 1.3rem;">🥇 最佳选择：Claude Desktop</h3>
                    <p><strong>费用：</strong>免费版足够使用</p>
                    <p><strong>优势：</strong>原生MCP支持，与您的DaVinci MCP完美兼容，无需任何配置</p>
                    <p><strong>立即行动：</strong>下载Claude Desktop → 配置MCP → 开始远程操控DaVinci</p>
                </div>

                <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 15px; backdrop-filter: blur(10px);">
                    <h3 style="margin-bottom: 10px; font-size: 1.3rem;">🥈 性价比之选：Windsurf</h3>
                    <p><strong>费用：</strong>免费版 + Pro仅$10/月</p>
                    <p><strong>优势：</strong>最便宜的MCP支持，完整IDE环境</p>
                    <p><strong>适合：</strong>预算有限但需要专业开发环境的用户</p>
                </div>

                <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 15px; backdrop-filter: blur(10px);">
                    <h3 style="margin-bottom: 10px; font-size: 1.3rem;">🥉 本地MCP之选：Goose Agent</h3>
                    <p><strong>费用：</strong>完全免费</p>
                    <p><strong>优势：</strong>Block出品，本地执行+MCP支持，85%兼容性</p>
                    <p><strong>适合：</strong>需要本地运行且支持MCP的用户</p>
                </div>

                <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 15px; backdrop-filter: blur(10px);">
                    <h3 style="margin-bottom: 10px; font-size: 1.3rem;">🏆 专业MCP之选：MCP Agent</h3>
                    <p><strong>费用：</strong>开源免费</p>
                    <p><strong>优势：</strong>专为MCP设计，95%兼容性，代理间通信</p>
                    <p><strong>适合：</strong>深度使用MCP协议的专业用户</p>
                </div>

                <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 15px; backdrop-filter: blur(10px);">
                    <h3 style="margin-bottom: 10px; font-size: 1.3rem;">💼 企业级之选：Augment</h3>
                    <p><strong>费用：</strong>企业定价</p>
                    <p><strong>优势：</strong>ISO认证，98%兼容性，企业级安全</p>
                    <p><strong>适合：</strong>对安全性和合规性有严格要求的企业</p>
                </div>
            </div>

            <p style="margin-top: 30px; font-size: 1.2rem;">
                <strong>💡 建议：</strong>立即下载Claude Desktop开始使用，它能直接调用您现有的83个DaVinci工具！
            </p>
        </div>
    </div>

    <script>
        // 过滤功能
        const filterTabs = document.querySelectorAll('.filter-tab');
        const agentCards = document.querySelectorAll('.agent-card');

        filterTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有active类
                filterTabs.forEach(t => t.classList.remove('active'));
                // 添加active类到当前tab
                tab.classList.add('active');

                const filter = tab.dataset.filter;

                agentCards.forEach(card => {
                    if (filter === 'all') {
                        card.style.display = 'block';
                    } else {
                        const categories = card.dataset.category.split(' ');
                        if (categories.includes(filter)) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    }
                });
            });
        });

        // 动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 初始化动画
        agentCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // 表格行悬停效果
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.background = '#f8f9fa';
            });
            row.addEventListener('mouseleave', () => {
                row.style.background = '';
            });
        });
    </script>
</body>
</html>
