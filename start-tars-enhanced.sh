#!/bin/bash
cd "$(dirname "$0")"

echo "🚀 启动增强版 TARS Agent Web界面..."
echo "📁 工作目录: $(pwd)"
echo "🌐 访问地址: http://localhost:8888"
echo "🔧 增强功能: DOM 错误处理、重试机制、调试模式"
echo ""

# 设置环境变量以增强错误处理
export NODE_ENV=development
export TARS_DEBUG=true
export TARS_DOM_ERROR_HANDLING=true

# 创建错误日志目录
mkdir -p ./logs

# 启动TARS并在后台运行，同时记录日志
echo "📝 启动日志将保存到 ./logs/tars-$(date +%Y%m%d-%H%M%S).log"
agent-tars web 2>&1 | tee "./logs/tars-$(date +%Y%m%d-%H%M%S).log" &
TARS_PID=$!

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务是否正常启动
if kill -0 $TARS_PID 2>/dev/null; then
    echo "✅ TARS Agent 启动成功 (PID: $TARS_PID)"
else
    echo "❌ TARS Agent 启动失败，请检查日志"
    exit 1
fi

# 尝试打开浏览器
if command -v open &> /dev/null; then
    # macOS
    open http://localhost:8888
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open http://localhost:8888
else
    echo "请手动打开浏览器访问: http://localhost:8888"
fi

echo ""
echo "🛠️  故障排除提示:"
echo "   - 如果遇到 DOM 错误，请刷新页面"
echo "   - 查看实时日志: tail -f ./logs/tars-*.log"
echo "   - 调试模式已启用，错误信息更详细"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
wait $TARS_PID
