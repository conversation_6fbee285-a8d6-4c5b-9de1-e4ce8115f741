// ==UserScript==
// @name         TARS WebUI DOM Error Fix
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  修复 TARS WebUI 中的 removeChild DOM 错误
// <AUTHOR>
// @match        http://localhost:8888/*
// @match        http://127.0.0.1:8888/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🔧 TARS DOM 错误修复脚本已加载');
    
    // 1. 安全的 removeChild 函数
    function safeRemoveChild(parent, child) {
        try {
            if (parent && child && parent.contains(child)) {
                parent.removeChild(child);
                return true;
            }
        } catch (error) {
            console.warn('DOM removeChild 操作失败，尝试备用方法:', error);
            try {
                if (child && child.remove) {
                    child.remove();
                    return true;
                }
            } catch (fallbackError) {
                console.error('DOM 节点移除完全失败:', fallbackError);
            }
        }
        return false;
    }
    
    // 2. 重写原生 removeChild 方法
    const originalRemoveChild = Node.prototype.removeChild;
    Node.prototype.removeChild = function(child) {
        try {
            if (this.contains(child)) {
                return originalRemoveChild.call(this, child);
            } else {
                console.warn('尝试移除不存在的子节点，已跳过操作');
                return child;
            }
        } catch (error) {
            console.warn('removeChild 错误已被拦截:', error);
            // 尝试使用现代方法
            if (child && child.remove) {
                child.remove();
                return child;
            }
            throw error;
        }
    };
    
    // 3. 全局错误监听
    window.addEventListener('error', function(event) {
        if (event.error && event.error.name === 'NotFoundError' && 
            event.error.message.includes('removeChild')) {
            console.error('🚨 检测到 removeChild 错误:', {
                message: event.error.message,
                stack: event.error.stack,
                timestamp: new Date().toISOString(),
                url: event.filename,
                line: event.lineno
            });
            
            // 阻止错误冒泡
            event.preventDefault();
            return false;
        }
    });
    
    // 4. React 错误边界（如果页面使用 React）
    if (window.React) {
        console.log('检测到 React，添加错误边界处理');
        
        // 监听 React 错误
        window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.message && 
                event.reason.message.includes('removeChild')) {
                console.warn('React Promise 错误已被处理:', event.reason);
                event.preventDefault();
            }
        });
    }
    
    // 5. 定期清理可能的问题节点
    function cleanupOrphanedNodes() {
        try {
            // 查找可能的孤立节点
            const allElements = document.querySelectorAll('*');
            let cleanedCount = 0;
            
            allElements.forEach(element => {
                // 检查是否有无效的父子关系
                if (element.parentNode && !element.parentNode.contains(element)) {
                    console.warn('发现孤立节点，尝试清理:', element);
                    try {
                        element.remove();
                        cleanedCount++;
                    } catch (e) {
                        // 忽略清理失败
                    }
                }
            });
            
            if (cleanedCount > 0) {
                console.log(`✅ 清理了 ${cleanedCount} 个孤立节点`);
            }
        } catch (error) {
            console.warn('节点清理过程中出现错误:', error);
        }
    }
    
    // 每30秒执行一次清理
    setInterval(cleanupOrphanedNodes, 30000);
    
    // 6. 页面加载完成后的初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ TARS DOM 错误修复脚本初始化完成');
            cleanupOrphanedNodes();
        });
    } else {
        console.log('✅ TARS DOM 错误修复脚本初始化完成');
        cleanupOrphanedNodes();
    }
    
    // 7. 提供手动修复函数
    window.fixTARSDOM = function() {
        console.log('🔧 手动执行 DOM 修复...');
        cleanupOrphanedNodes();
        
        // 强制重新渲染页面的某些部分
        const containers = document.querySelectorAll('[class*="container"], [class*="wrapper"], [id*="root"]');
        containers.forEach(container => {
            if (container) {
                const display = container.style.display;
                container.style.display = 'none';
                container.offsetHeight; // 触发重排
                container.style.display = display;
            }
        });
        
        console.log('✅ DOM 修复完成');
    };
    
    console.log('🎯 使用方法: 在控制台运行 fixTARSDOM() 来手动修复 DOM 问题');
    
})();
