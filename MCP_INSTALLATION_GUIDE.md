# TARS Agent MCP 服务器安装指南

## 概述

本指南记录了为 TARS Agent 安装三个 MCP (Model Context Protocol) 服务器的完整过程：

1. **MiniMax-MCP**: 提供文本转语音、图像生成、视频生成功能
2. **DesktopCommanderMCP**: 提供桌面自动化和文件系统操作功能  
3. **DaVinci MCP Professional**: 提供 DaVinci Resolve 视频编辑集成功能

## 安装状态

✅ **已完成安装的服务器:**
- MiniMax-MCP (通过 pip3 安装)
- DesktopCommanderMCP (通过 npm 全局安装)
- DaVinci MCP Professional (通过源码安装)

## 系统要求

- Python 3.12.9 ✅
- Node.js v22.17.1 ✅  
- npm 10.9.2 ✅
- uvx 0.8.0 ✅

## 安装的包和依赖

### MiniMax-MCP
```bash
pip3 install minimax-mcp
```
**依赖包**: mcp, fastapi, uvicorn, httpx, pydantic, sounddevice, soundfile 等

### DesktopCommanderMCP  
```bash
npm install -g @wonderwhy-er/desktop-commander
```
**功能**: 终端控制、文件系统搜索、代码编辑、进程管理

### DaVinci MCP Professional
```bash
git clone https://github.com/Positronikal/davinci-mcp-professional.git
cd davinci-mcp-professional
pip3 install -e .
```
**依赖包**: mcp, anyio, httpx, pydantic, click, colorama

## TARS 配置更新

已更新 `agent-tars.config.json` 文件，添加了 `mcpServers` 配置段：

```json
{
  "mcpServers": {
    "minimax": {
      "command": "uvx",
      "args": ["minimax-mcp"],
      "env": {
        "MINIMAX_API_KEY": "",
        "MINIMAX_API_HOST": "https://api.minimax.chat",
        "MINIMAX_MCP_BASE_PATH": ""
      }
    },
    "desktop-commander": {
      "command": "npx",
      "args": ["-y", "@wonderwhy-er/desktop-commander"]
    },
    "davinci-mcp": {
      "command": "python3",
      "args": ["/Users/<USER>/TARS-Agent/davinci-mcp-professional/mcp_server.py"],
      "env": {
        "DAVINCI_RESOLVE_PATH": "/Applications/DaVinci Resolve/DaVinci Resolve.app"
      }
    }
  }
}
```

## 配置说明

### MiniMax-MCP 配置
- **MINIMAX_API_KEY**: 需要设置 MiniMax API 密钥才能使用
- **MINIMAX_API_HOST**: API 服务器地址，默认为官方地址
- **MINIMAX_MCP_BASE_PATH**: 可选的基础路径配置

### Desktop Commander 配置
- 无需额外配置，开箱即用
- 提供终端命令执行、文件操作、代码搜索等功能

### DaVinci MCP Professional 配置  
- **DAVINCI_RESOLVE_PATH**: DaVinci Resolve 应用程序路径
- 需要安装 DaVinci Resolve 软件才能使用

## 使用方法

1. **启动 TARS**: 使用更新后的配置文件启动 TARS Agent
2. **MCP 服务器自动启动**: TARS 会根据配置自动启动各个 MCP 服务器
3. **功能调用**: 通过 TARS 的自然语言接口调用各种 MCP 功能

## 功能特性

### MiniMax-MCP 功能
- 文本转语音 (TTS)
- 图像生成
- 视频生成
- 多模态内容创建

### Desktop Commander 功能
- 终端命令执行和进程管理
- 文件系统操作 (读写、搜索、移动)
- 代码编辑和搜索
- 系统自动化任务

### DaVinci MCP Professional 功能
- DaVinci Resolve 项目管理
- 视频编辑自动化
- 媒体文件处理
- 渲染和导出控制

## 故障排除

### 常见问题
1. **MiniMax-MCP 启动失败**: 检查 MINIMAX_API_KEY 是否设置
2. **Desktop Commander 权限问题**: 确保有足够的系统权限
3. **DaVinci MCP 连接失败**: 确保 DaVinci Resolve 已安装且路径正确

### 日志查看
- TARS 日志级别已设置为 "debug"
- 可通过 TARS 控制台查看 MCP 服务器连接状态

## 备份信息

- 原始配置文件已备份为: `agent-tars.config.json.backup`
- 可随时恢复到原始配置

## 下一步

1. 获取 MiniMax API 密钥并配置
2. 测试各个 MCP 服务器功能
3. 根据需要调整配置参数
4. 开始使用增强的 TARS Agent 功能

---

**安装完成时间**: 2025-01-30
**安装者**: Augment Agent  
**TARS 版本**: 使用 doubao-1-5-thinking-vision-pro-250428 模型
