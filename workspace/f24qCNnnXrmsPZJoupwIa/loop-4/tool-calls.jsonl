[{"toolCallId": "call_zb9hvyly24f5h24yo2ilqvzx", "name": "final_answer", "args": {"format": "detailed", "isDeepResearch": true, "title": "MCP服务添加指南（以Model Context Protocol为例）"}, "result": {"success": true, "message": "Final answer generated"}, "executionTime": 28258}, {"toolCallId": "call_k4eqsh6cgwga567omux6ezzi", "name": "browser_get_markdown", "args": {"page": 1}, "result": {"content": "# GitHub - MiniMax-AI/MiniMax-MCP: Official MiniMax Model Context Protocol (MCP) server that enables interaction with powerful Text to Speech, image generation and video generation APIs.\n\nOfficial MiniMax Model Context Protocol (MCP) server that enables interaction with powerful Text to Speech and video/image generation APIs. This server allows MCP clients like [<PERSON>](https://www.anthropic.com/claude), [<PERSON>urs<PERSON>](https://www.cursor.so/), [Windsurf](https://codeium.com/windsurf), [OpenAI Agents](https://github.com/openai/openai-agents-python) and others to generate speech, clone voices, generate video, generate image and more.\n\n## Documentation\n\n*   [中文文档](https://github.com/MiniMax-AI/MiniMax-MCP/blob/main/README-CN.md)\n*   [MiniMax-MCP-JS](https://github.com/MiniMax-AI/MiniMax-MCP-JS) - Official JavaScript implementation of MiniMax MCP\n\n## Quickstart with MCP Client\n\n1.  Get your API key from [MiniMax](https://www.minimax.io/platform/user-center/basic-information/interface-key).\n2.  Install `uv` (Python package manager), install with `curl -LsSf https://astral.sh/uv/install.sh | sh` or see the `uv` [repo](https://github.com/astral-sh/uv) for additional install methods.\n3.  **Important**: The API host and key vary by region and must match; otherwise, you'll encounter an `Invalid API key` error.\n\n| Region | Global | Mainland |\n| --- | --- | --- |\n| MINIMAX\\_API\\_KEY | go get from [MiniMax Global](https://www.minimax.io/platform/user-center/basic-information/interface-key) | go get from [MiniMax](https://platform.minimaxi.com/user-center/basic-information/interface-key) |\n| MINIMAX\\_API\\_HOST | [https://api.minimax.io](https://api.minimax.io/) | [https://api.minimaxi.com](https://api.minimaxi.com/) |\n\n### Claude Desktop\n\nGo to `Claude > Settings > Developer > Edit Config > claude_desktop_config.json` to include the following:\n\n```\n{\n  \"mcpServers\": {\n    \"MiniMax\": {\n      \"command\": \"uvx\",\n      \"args\": [\n        \"minimax-mcp\",\n        \"-y\"\n      ],\n      \"env\": {\n        \"MINIMAX_API_KEY\": \"insert-your-api-key-here\",\n        \"MINIMAX_MCP_BASE_PATH\": \"local-output-dir-path, such as /User/xxx/Desktop\",\n        \"MINIMAX_API_HOST\": \"api host, https://api.minimax.io | https://api.minimaxi.com\",\n        \"MINIMAX_API_RESOURCE_MODE\": \"optional, [url|local], url is default, audio/image/video are downloaded locally or provided in URL format\"\n      }\n    }\n  }\n}\n\n```\n\n⚠️ Warning: The API key needs to match the host. If an error \"API Error: invalid api key\" occurs, please check your api host:\n\n*   Global Host：`https://api.minimax.io`\n*   Mainland Host：`https://api.minimaxi.com`\n\nIf you're using Windows, you will have to enable \"Developer Mode\" in Claude Desktop to use the MCP server. Click \"Help\" in the hamburger menu in the top left and select \"Enable Developer Mode\".\n\n### Cursor\n\nGo to `Cursor -> Preferences -> Cursor Settings -> MCP -> Add new global MCP Server` to add above config.\n\nThat's it. Your MCP client can now interact with MiniMax through these tools:\n\n## Transport\n\nWe support two transport types: stdio and sse.\n\n| stdio | SSE |\n| --- | --- |\n| Run locally | Can be deployed locally or in the cloud |\n| Communication through `stdout` | Communication through `network` |\n| Input: Supports processing `local files` or valid `URL` resources | Input: When deployed in the cloud, it is recommended to use `URL` for input |\n\n## Available Tools\n\n| tool | description |\n| --- | --- |\n| `text_to_audio` | Convert text to audio with a given voice |\n| `list_voices` | List all voices available |\n| `voice_clone` | Clone a voice using provided audio files |\n| `generate_video` | Generate a video from a prompt |\n| `text_to_image` | Generate a image from a prompt |\n| `query_video_generation` | Query the result of video generation task |\n| `music_generation` | Generate a music track from a prompt and lyrics |\n| `voice_design` | Generate a voice from a prompt using preview text |\n\n## Release Notes\n\n### July 2, 2025\n\n#### 🆕 What's New\n\n*   **Voice Design**: New `voice_design` tool - create custom voices from descriptive prompts with preview audio\n*   **Video Enhancement**: Added `MiniMax-Hailuo-02` model with ultra-clear quality and duration/resolution controls\n*   **Music Generation**: Enhanced `music_generation` tool powered by `music-1.5` model\n\n#### 📈 Enhanced Tools\n\n*   `voice_design` - Generate personalized voices from text descriptions\n*   `generate_video` - Now supports MiniMax-Hailuo-02 with 6s/10s duration and 768P/1080P resolution options\n*   `music_generation` - High-quality music creation with music-1.5 model\n\n## FAQ\n\n### 1\\. invalid api key\n\nPlease ensure your API key and API host are regionally aligned\n\n| Region | Global | Mainland |\n| --- | --- | --- |\n| MINIMAX\\_API\\_KEY | go get from [MiniMax Global](https://www.minimax.io/platform/user-center/basic-information/interface-key) | go get from [MiniMax](https://platform.minimaxi.com/user-center/basic-information/interface-key) |\n| MINIMAX\\_API\\_HOST | [https://api.minimax.io](https://api.minimax.io/) | [https://api.minimaxi.com](https://api.minimaxi.com/) |\n\n### 2\\. spawn uvx ENOENT\n\nPlease confirm its absolute path by running this command in your terminal:\n\nOnce you obtain the absolute path (e.g., /usr/local/bin/uvx), update your configuration to use that path (e.g., \"command\": \"/usr/local/bin/uvx\").\n\n### 3\\. How to use `generate_video` in async-mode\n\nDefine completion rules before starting: [](https://camo.githubusercontent.com/64f42a66a81d074a7b1e0a28fe9b6549d26b2e072e2f12fb89fa147a80ba9e92/68747470733a2f2f7075626c69632d63646e2d766964656f2d646174612d616c67656e672e6f73732d636e2d77756c616e63686162752e616c6979756e63732e636f6d2f637572736f725f72756c65322e706e673f782d6f73732d70726f636573733d696d6167652f726573697a652c705f35302f666f726d61742c77656270)Alternatively, these rules can be configured in your IDE settings (e.g., Cursor):[](https://camo.githubusercontent.com/8e0e6f9412be0a8af801024e0832b0c41468062724abf6dd6e557caf381ab953/68747470733a2f2f7075626c69632d63646e2d766964656f2d646174612d616c67656e672e6f73732d636e2d77756c616e63686162752e616c6979756e63732e636f6d2f637572736f725f766964656f5f72756c652e706e673f782d6f73732d70726f636573733d696d6167652f726573697a652c705f35302f666f726d61742c77656270)\n\n## Example usage\n\n⚠️ Warning: Using these tools may incur costs.\n\n### 1\\. broadcast a segment of the evening news\n\n### 2\\. clone a voice\n\n### 3\\. generate a video\n\n### 4\\. generate images", "pagination": {"currentPage": 1, "totalPages": 1, "hasMorePages": false}, "title": "GitHub - MiniMax-AI/MiniMax-MCP: Official MiniMax Model Context Protocol (MCP) server that enables interaction with powerful Text to Speech, image generation and video generation APIs."}, "executionTime": 23}, {"toolCallId": "call_egric0qojmifb11pnee8b46l", "name": "browser_get_clickable_elements", "args": {}, "result": [{"type": "text", "text": "[0]<a></a>\n[1]<img></img>\n[2]<a></a>\n[3]<img></img>\n[4]<label></label>\n[5]<a>Log In</a>\n[6]<img></img>\n[7]<a>Overview</a>\n[8]<a>What’s New</a>\n[9]<a>Edit</a>\n[10]<a>Cut</a>\n[11]<a>Color</a>\n[12]<a>Fusion</a>\n[13]<a>Fairlight</a>\n[14]<a>Collaboration</a>\n[15]<a>Keyboard</a>\n[16]<a>Panels</a>\n[17]<a>Consoles</a>\n[18]<a>Studio</a>\n[19]<a>Training</a>\n[20]<a>Tech Specs</a>\n[21]<img></img>\n[]DaVinci"}], "executionTime": 108}]