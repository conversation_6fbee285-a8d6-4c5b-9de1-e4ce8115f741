#!/usr/bin/env python3
"""
测试DaVinci MCP Professional安装的脚本
"""

import os
import sys
import json

def test_davinci_mcp_installation():
    """测试DaVinci MCP Professional安装"""
    print("🔍 测试DaVinci MCP Professional安装...")
    
    # 检查配置文件
    config_path = "agent-tars.config.json"
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    # 读取配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查MCP服务器配置
    if 'mcpServers' not in config:
        print("❌ 配置文件中没有mcpServers配置")
        return False
    
    if 'davinci-resolve' not in config['mcpServers']:
        print("❌ 配置文件中没有davinci-resolve服务器配置")
        return False
    
    davinci_config = config['mcpServers']['davinci-resolve']
    print("✅ 找到DaVinci Resolve MCP服务器配置")
    
    # 检查命令路径
    main_py_path = davinci_config['args'][0]
    if not os.path.exists(main_py_path):
        print(f"❌ 主程序文件不存在: {main_py_path}")
        return False
    
    print(f"✅ 主程序文件存在: {main_py_path}")
    
    # 检查工作目录
    cwd = davinci_config['cwd']
    if not os.path.exists(cwd):
        print(f"❌ 工作目录不存在: {cwd}")
        return False
    
    print(f"✅ 工作目录存在: {cwd}")
    
    # 检查环境变量路径
    env = davinci_config['env']
    resolve_api_path = env['RESOLVE_SCRIPT_API']
    resolve_lib_path = env['RESOLVE_SCRIPT_LIB']
    
    if os.path.exists(resolve_api_path):
        print(f"✅ DaVinci Resolve API路径存在: {resolve_api_path}")
    else:
        print(f"⚠️  DaVinci Resolve API路径不存在: {resolve_api_path}")
    
    if os.path.exists(resolve_lib_path):
        print(f"✅ DaVinci Resolve库文件存在: {resolve_lib_path}")
    else:
        print(f"⚠️  DaVinci Resolve库文件不存在: {resolve_lib_path}")
    
    # 测试Python模块导入
    try:
        sys.path.insert(0, os.path.join(cwd, 'src'))
        import main
        print("✅ DaVinci MCP Professional模块导入成功")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    
    print("\n🎉 DaVinci MCP Professional安装测试完成！")
    print("\n📋 配置摘要:")
    print(f"   - 服务器名称: davinci-resolve")
    print(f"   - 主程序路径: {main_py_path}")
    print(f"   - 工作目录: {cwd}")
    print(f"   - API路径: {resolve_api_path}")
    print(f"   - 库文件路径: {resolve_lib_path}")
    
    return True

if __name__ == "__main__":
    success = test_davinci_mcp_installation()
    sys.exit(0 if success else 1)
