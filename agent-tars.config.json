{"model": {"provider": "volcengine", "id": "doubao-1-5-thinking-vision-pro-250428", "apiKey": "9dd1697b-8636-4964-bfa8-2d5ebad73f06", "stream": true, "thinking": {"type": "enabled"}}, "browser": {"control": "hybrid", "waitForStable": true, "retryOnError": true, "errorHandling": {"domOperationTimeout": 5000, "maxRetries": 3, "ignoreNotFoundErrors": true}}, "workspace": {"workingDirectory": "/Users/<USER>"}, "planner": {"enable": true}, "snapshot": {"enable": true, "snapshotPath": "./snapshots"}, "logLevel": "debug", "port": 8888, "toolCallEngine": "native", "mcpServers": {"minimax": {"command": "uvx", "args": ["minimax-mcp"], "cwd": "/Users/<USER>", "env": {"MINIMAX_API_KEY": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "MINIMAX_API_HOST": "https://api.minimax.chat", "MINIMAX_MCP_BASE_PATH": "/Users/<USER>/Movies/Minimax"}}}}