[{"id": "5a02a6c5-8af7-44b2-b57b-6fc498911fa8", "type": "user_message", "timestamp": 1753894826526, "content": "你好，帮我看看桌面有什么文件"}, {"id": "18ce5bbb-6f69-48e6-9be5-ed7534234bea", "type": "plan_start", "timestamp": 1753894826528, "sessionId": "1753894826526-hq06eos"}, {"id": "1239ea8a-f9ee-4f21-bbf0-c9d24a2bc070", "type": "agent_run_start", "timestamp": 1753894826526, "sessionId": "1753894826526-hq06eos", "runOptions": {"input": "你好，帮我看看桌面有什么文件", "stream": true}, "provider": "volcengine", "model": "doubao-1-5-thinking-vision-pro-250428"}, {"id": "981e5920-c6c1-4e10-91f6-5342c5086036", "type": "system", "timestamp": 1753894827157, "level": "error", "message": "Error in agent execution: Error: 400 Invalid function format: 'type' Request id: 0217538948271454ef961016ad51c6e7b26c7ae29c8126e75111d", "details": {"error": "Error: 400 Invalid function format: 'type' Request id: 0217538948271454ef961016ad51c6e7b26c7ae29c8126e75111d", "provider": "volcengine"}}]