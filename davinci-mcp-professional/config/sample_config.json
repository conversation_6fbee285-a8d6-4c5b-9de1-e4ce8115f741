{"project_name": "MCP Automation Demo", "timeline_name": "Demo Timeline", "render_preset": "YouTube 1080p", "media_files": ["/Users/<USER>/sample_video.mp4", "/Users/<USER>/interview.mov", "/Users/<USER>/background.png", "/Users/<USER>/music.wav"], "color_settings": {"primary_correction": {"gamma_red": 0.05, "gamma_green": 0.02, "gamma_blue": -0.03}, "secondary_correction": {"lift_master": -0.05, "gamma_master": 0.02, "gain_master": 0.1}}, "project_settings": {"timelineFrameRate": "24", "timelineResolutionWidth": 1920, "timelineResolutionHeight": 1080}, "render_settings": {"destination": "/Users/<USER>/Output", "format": "mp4", "preset": "YouTube 1080p", "use_in_out_range": false}, "organize_bins": {"Video": [".mp4", ".mov", ".mxf", ".avi"], "Audio": [".wav", ".mp3", ".aac", ".m4a"], "Images": [".png", ".jpg", ".jpeg", ".tiff", ".tif", ".exr"], "Graphics": [".psd", ".ai", ".eps"]}}