
+------------------------------------------------+
|                                                |
|    █████  ██████  ███████ ███    ██ ████████   |
|   ██   ██ ██      ██      ████   ██    ██      |
|   ███████ ██   ██ █████   ██ ██  ██    ██      |
|   ██   ██ ██   ██ ██      ██  ██ ██    ██      |
|   ██   ██ ███████ ███████ ██   ████    ██      |
|                                                |
|   ████████  █████  ██████   ███████            |
|      ██    ██   ██ ██   ██  ██                 |
|      ██    ███████ ██████   ███████            |
|      ██    ██   ██ ██   ██       ██            |
|      ██    ██   ██ ██   ██  ███████            |
|                                                |
|                                                |
|   An open-source Multimodal AI Agent v0.2.10   |
|                                                |
|   https://agent-tars.com                       |
|                                                |
+------------------------------------------------+


+------------------------------------------------------------------+
|                                                                  |
|   🎉 Agent TARS is available at: http://localhost:8888           |
|                                                                  |
|   📁 Workspace: ./workspace                                      |
|                                                                  |
|   🤖 Model: volcengine | doubao-1-5-thinking-vision-pro-250428   |
|                                                                  |
+------------------------------------------------------------------+

EventStream EventStream initialized with options: { maxEvents: 1000, autoTrim: true }
EventStream Subscribed to events (total subscribers: 1)
Core Log level set to: DEBUG
EventStream EventStream initialized with options: { maxEvents: 1000, autoTrim: true }
Core [Agent] AgentTARS initialized | Default model provider: volcengine | Default model: doubao-1-5-thinking-vision-pro-250428 | Tools: 0 | Max iterations: 100
Core [Agent] Early model resolution successful | Provider: volcengine | Model: doubao-1-5-thinking-vision-pro-250428
Core:AgentTARS 🤖 AgentTARS initialized | Working directory: /Users/<USER>/TARS-Agent/workspace
Core:AgentTARS:PlanManager PlanManager initialized with max steps: 3
EventStream Subscribed to events (total subscribers: 1)
Core:AgentTARS AgentSnapshot initialized with path: /Users/<USER>/TARS-Agent/workspace/7PJ4zS_inmAsx9X_wZzxg
Core:AgentTARS Agent Config {
  "workspace": {
    "workingDirectory": "/Users/<USER>/TARS-Agent/workspace",
    "isolateSessions": false
  },
  "search": {
    "provider": "browser_search",
    "count": 10,
    "browserSearch": {
      "engine": "google",
      "needVisitedUrls": false
    }
  },
  "browser": {
    "type": "local",
    "headless": false,
    "control": "hybrid",
    "waitForStable": true,
    "retryOnError": true,
    "errorHandling": {
      "domOperationTimeout": 5000,
      "maxRetries": 3,
      "ignoreNotFoundErrors": true
    }
  },
  "mcpImpl": "in-memory",
  "mcpServers": {
    "desktop-commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander"
      ]
    },
    "minimax": {
      "command": "uvx",
      "args": [
        "minimax-mcp"
      ],
      "env": {
        "MINIMAX_API_KEY": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "MINIMAX_API_HOST": "https://api.minimax.chat",
        "MINIMAX_MCP_BASE_PATH": "/Users/<USER>/Movies/Minimax"
      }
    },
    "davinci-mcp": {
      "command": "python3",
      "args": [
        "/Users/<USER>/TARS-Agent/davinci-mcp-professional/mcp_server.py"
      ],
      "env": {
        "DAVINCI_RESOLVE_PATH": "/Applications/DaVinci Resolve/DaVinci Resolve.app"
      }
    }
  },
  "maxIterations": 100,
  "maxTokens": 8192,
  "model": {
    "provider": "volcengine",
    "id": "doubao-1-5-thinking-vision-pro-250428",
    "apiKey": "9dd1697b-8636-4964-bfa8-2d5ebad73f06",
    "stream": true,
    "thinking": {
      "type": "enabled"
    }
  },
  "planner": {
    "enable": true
  },
  "snapshot": {
    "enable": true,
    "snapshotPath": "./snapshots"
  },
  "logLevel": 0,
  "port": 8888,
  "toolCallEngine": "native",
  "--": [],
  "server": {
    "port": 8888,
    "storage": {
      "type": "sqlite"
    }
  },
  "ui": {
    "staticPath": "/Users/<USER>/.nvm/versions/node/v22.17.1/lib/node_modules/@agent-tars/cli/static"
  },
  "name": "AgentTARS",
  "instructions": "\nYou are Agent TARS, a multimodal AI agent created by the ByteDance.\n\n<intro>\nYou excel at the following tasks:\n1. Information gathering, fact-checking, and documentation\n2. Data processing, analysis, and visualization\n3. Writing multi-chapter articles and in-depth research reports\n4. Creating websites, applications, and tools\n5. Using programming to solve various problems beyond development\n6. Various tasks that can be accomplished using computers and the internet\n</intro>\n\n<language_settings>\nUse the language specified by user in messages as the working language when explicitly provided\nAll thinking and responses must be in the working language\nNatural language arguments in tool calls must be in the working language\nAvoid using pure lists and bullet points format in any language\n</language_settings>\n\n<multimodal_understanding>\nWhen processing images, it's crucial to understand the difference between image types:\n1. Browser Screenshots: These are images showing the browser interface that you can interact with using browser tools\n   - Appear as part of the browser_vision_control tool output or environment input labeled as \"Browser Screenshot\"\n   - ONLY these screenshots represent interfaces you can operate on with browser tools\n   - Use these for navigation, clicking elements, scrolling, and other browser interactions\n\n2. User-Uploaded Images: These are regular images the user has shared but are NOT browser interfaces\n   - May include photos, diagrams, charts, documents, or any other visual content\n   - Cannot be operated on with browser tools - don't try to click elements in these images\n   - Should be analyzed for information only (objects, text, context, meaning)\n   - Respond to user questions about these images with observations and analysis\n\nDistinguish between these types by context and environment input descriptions to avoid confusion.\nWhen you see a new image, first determine which type it is before deciding how to interact with it.\n</multimodal_understanding>\n\n<system_capability>\nSystem capabilities:\n- Communicate with users through message tools\n- Access a Linux sandbox environment with internet connection\n- Use shell, text editor, browser, and other software\n- Write and run code in Python and various programming languages\n- Independently install required software packages and dependencies via shell\n- Deploy websites or applications and provide public access\n- Suggest users to temporarily take control of the browser for sensitive operations when necessary\n- Utilize various tools to complete user-assigned tasks step by step\n</system_capability>\n\n<agent_loop>\nYou operate in an agent loop, iteratively completing tasks through these steps:\n1. Analyze Events: Understand user needs and current state through event stream, focusing on latest user messages and execution results\n2. Select Tools: Choose next tool call based on current state, task planning, relevant knowledge and available data APIs\n3. Wait for Execution: Selected tool action will be executed by sandbox environment with new observations added to event stream\n4. Iterate: Choose only one tool call per iteration, patiently repeat above steps until task completion\n5. Submit Results: Send results to user via message tools, providing deliverables and related files as message attachments\n6. Enter Standby: Enter idle state when all tasks are completed or user explicitly requests to stop, and wait for new tasks\n</agent_loop>\n\n<file_rules>\n- Use file tools for reading, writing, appending, and editing to avoid string escape issues in shell commands\n- Actively save intermediate results and store different types of reference information in separate files\n- When merging text files, must use append mode of file writing tool to concatenate content to target file\n- Strictly follow requirements in <writing_rules>, and avoid using list formats in any files except todo.md\n</file_rules>\n\n<shell_rules>\n- Avoid commands requiring confirmation; actively use -y or -f flags for automatic confirmation\n- Avoid commands with excessive output; save to files when necessary\n- Chain multiple commands with && operator to minimize interruptions\n- Use pipe operator to pass command outputs, simplifying operations\n- Use non-interactive `bc` for simple calculations, Python for complex math; never calculate mentally\n- Use `uptime` command when users explicitly request sandbox status check or wake-up\n</shell_rules>\n\n\n\n\n<planning_approach>\nYou are a methodical agent that follows a plan-and-solve approach for complex tasks. When handling tasks:\n\n1. Analyze if the task requires a multi-step plan:\n   - For complex research, analysis, or multi-part tasks → Create a plan\n   - For simple questions or tasks → Skip planning and answer directly\n\n2. If a plan is needed:\n   - Create a clear, step-by-step plan with specific goals\n   - Execute each step in order, using appropriate tools\n   - Update the plan as you learn new information\n   - Mark steps as completed when done\n   - Once ALL steps are complete, call the \"final_answer\" tool\n\n3. During execution:\n   - Adapt your plan as needed based on new findings\n   - Be willing to simplify the plan if the task turns out simpler than expected\n   - Always complete your plan before providing final answers\n</planning_approach>\n\n<planning_constraints>\nIMPORTANT CONSTRAINTS:\n- Create AT MOST 3 key steps in your plan\n- Focus on information gathering and research steps\n- Call the \"final_answer\" tool once ALL plan steps are complete\n- For simple questions, you can skip planning entirely\n</planning_constraints>\n \n\n \n<browser_rules>\nYou have access to various browser tools to interact with web pages and extract information.\n\nYou have a hybrid browser control strategy with two complementary tool sets:\n\n1. Vision-based control (`browser_vision_control`): \n   - Use for visual interaction with web elements when you need precise clicking on specific UI elements\n   - Best for complex UI interactions where DOM selection is difficult\n   - Provides abilities like click, type, scroll, drag, and hotkeys based on visual understanding\n\n2. DOM-based utilities (all tools starting with `browser_`):\n   - `browser_navigate`, `browser_back`, `browser_forward`, `browser_refresh`: Use for page navigation\n   - `browser_get_markdown`: Use to extract and read the structured content of the page\n   - `browser_click`, `browser_type`, etc.: Use for DOM-based element interactions\n   - `browser_get_url`, `browser_get_title`: Use to check current page status\n\nUSAGE GUIDELINES:\n- Choose the most appropriate tool for each task\n- For content extraction, prefer `browser_get_markdown`\n- For clicks on visually distinct elements, use `browser_vision_control`\n- For form filling and structured data input, use DOM-based tools\n\nINFORMATION GATHERING WORKFLOW:\n- When the user requests information gathering, summarization, or content extraction:\n  1. PRIORITIZE using `browser_get_markdown` to efficiently extract page content\n  2. Call `browser_get_markdown` after each significant navigation to capture content\n  3. Use this tool FREQUENTLY when assembling reports, summaries, or comparisons\n  4. Extract content from MULTIPLE pages when compiling comprehensive information\n  5. Always extract content BEFORE proceeding to another page to avoid losing information\n\n- Establish a consistent workflow pattern:\n  1. Navigate to relevant page (using vision or DOM tools)\n  2. Extract complete content with `browser_get_markdown`\n  3. If needed, use `browser_vision_control` to access more content (scroll, click \"more\" buttons)\n  4. Extract again with `browser_get_markdown` after revealing new content\n  5. Repeat until all necessary information is collected\n  6. Organize extracted content into a coherent structure before presenting to user\n\n- Must use browser tools to access and comprehend all URLs provided by users in messages\n- Must use browser tools to access URLs from search tool results\n- Actively explore valuable links for deeper information, either by clicking elements or accessing URLs directly\n- Browser tools only return elements in visible viewport by default\n- Due to technical limitations, not all interactive elements may be identified; use coordinates to interact with unlisted elements\n- Browser tools automatically attempt to extract page content, providing it in Markdown format if successful\n- Extracted Markdown includes text beyond viewport but omits links and images; completeness not guaranteed\n- If extracted Markdown is complete and sufficient for the task, no scrolling is needed; otherwise, must actively scroll to view the entire page\n- Use message tools to suggest user to take over the browser for sensitive operations or actions with side effects when necessary\n</browser_rules>\n\n<envirnoment>\nCurrent Working Directory: /Users/<USER>/TARS-Agent/workspace\n</envirnoment>\n\n    "
}
Core:AgentTARS Initializing AgentTARS ...
Core:AgentTARS:BrowserToolsManager Initialized with mode: hybrid
Core:AgentTARS 🖥️ Initializing GUI Agent for visual browser control
Core:AgentTARS ✅ GUI Agent initialized successfully
Core:AgentTARS 🔍 Initializing search tools with direct integration
Core:AgentTARS:SearchToolProvider Mapped provider browser_search to browser_search
Core:AgentTARS:SearchToolProvider Search tool provider initialized with browser_search
Core [Tool] Registered: web_search | Description: "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️

Search the web for information. For best results:
1) Use CONCISE queries (3-5 words ideal)
2) Include only ESSENTIAL keywords, not full questions
3) For complex topics, use multiple simple searches instead of one long query
4) Focus on specific terms that will appear on relevant pages"
Core:AgentTARS ✅ Search tools initialized successfully
Core:AgentTARS Using shared browser instance for MCP servers
Core:AgentTARS ✅ Connected to browser MCP server
Core:AgentTARS ✅ Connected to filesystem MCP server
Core:AgentTARS ✅ Connected to commands MCP server
Core:AgentTARS:BrowserToolsManager 🌐 Activating browser control mode: hybrid
Core:AgentTARS:BrowserToolsManager 🔍 Strategy: BrowserHybridStrategy
Core [Tool] Registered: browser_vision_control | Description: "A browser operation tool based on visual understanding, perform the next action to complete the task.

## Action Space

click(point='<point>x1 y1</point>')            - Click at the specified coordinates
left_double(point='<point>x1 y1</point>')      - Double-click at the specified coordinates
right_single(point='<point>x1 y1</point>')     - Right-click at the specified coordinates
drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') - Drag from start to end point
hotkey(key='ctrl c')                           - Press keyboard shortcut (use space to separate keys, lowercase)
type(content='xxx')                            - Type text content (use \', \", and \n for special characters)
scroll(point='<point>x1 y1</point>', direction='down or up or right or left') - Scroll in specified direction
wait()                                         - Wait 5 seconds and take a screenshot to check for changes

## Note
- Folow user lanuage in in `thought` part.
- Describe your thought in `step` part.
- Describe your action in `Step` part.
- Extract the data your see in `pageData` part.
- This tool is for operational tasks, not for collect information.
"
Core [Tool] Registered: browser_navigate | Description: "[browser] Navigate to a URL"
Core [Tool] Registered: browser_go_back | Description: "[browser] Go back to the previous page, or close tab if no history exists"
Core [Tool] Registered: browser_go_forward | Description: "[browser] Go forward to the next page"
Core [Tool] Registered: browser_refresh | Description: "[browser] Refresh the current page"
Core [Tool] Registered: browser_get_markdown | Description: "[browser] Get the content of the current page as markdown with pagination support"
Core [Tool] Registered: browser_screenshot | Description: "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call."
Core [Tool] Registered: browser_click | Description: "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_click
Core [Tool] Registered: browser_form_input_fill | Description: "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_form_input_fill
Core [Tool] Registered: browser_select | Description: "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_select
Core [Tool] Registered: browser_hover | Description: "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_hover
Core [Tool] Registered: browser_evaluate | Description: "[browser] Execute JavaScript in the browser console"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_evaluate
Core [Tool] Registered: browser_get_clickable_elements | Description: "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_get_clickable_elements
Core [Tool] Registered: browser_read_links | Description: "[browser] Get all links on the current page"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_read_links
Core [Tool] Registered: browser_scroll | Description: "[browser] Scroll the page"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_scroll
Core [Tool] Registered: browser_tab_list | Description: "[browser] Get the list of tabs"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_tab_list
Core [Tool] Registered: browser_new_tab | Description: "[browser] Open a new tab"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_new_tab
Core [Tool] Registered: browser_close_tab | Description: "[browser] Close the current tab"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_close_tab
Core [Tool] Registered: browser_switch_tab | Description: "[browser] Switch to a specific tab"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_switch_tab
Core [Tool] Registered: browser_press_key | Description: "[browser] Press a key on the keyboard"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_press_key
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered 20 MCP browser tools
Core:AgentTARS:BrowserToolsManager ✅ Registered 20 browser tools:
Core:AgentTARS:BrowserToolsManager   • browser_vision_control
Core:AgentTARS:BrowserToolsManager   • browser_navigate
Core:AgentTARS:BrowserToolsManager   • browser_go_back
Core:AgentTARS:BrowserToolsManager   • browser_go_forward
Core:AgentTARS:BrowserToolsManager   • browser_refresh
Core:AgentTARS:BrowserToolsManager   • browser_get_markdown
Core:AgentTARS:BrowserToolsManager   • browser_screenshot
Core:AgentTARS:BrowserToolsManager   • browser_click
Core:AgentTARS:BrowserToolsManager   • browser_form_input_fill
Core:AgentTARS:BrowserToolsManager   • browser_select
Core:AgentTARS:BrowserToolsManager   • browser_hover
Core:AgentTARS:BrowserToolsManager   • browser_evaluate
Core:AgentTARS:BrowserToolsManager   • browser_get_clickable_elements
Core:AgentTARS:BrowserToolsManager   • browser_read_links
Core:AgentTARS:BrowserToolsManager   • browser_scroll
Core:AgentTARS:BrowserToolsManager   • browser_tab_list
Core:AgentTARS:BrowserToolsManager   • browser_new_tab
Core:AgentTARS:BrowserToolsManager   • browser_close_tab
Core:AgentTARS:BrowserToolsManager   • browser_switch_tab
Core:AgentTARS:BrowserToolsManager   • browser_press_key
Core:AgentTARS ✅ Registered 20 browser tools using 'hybrid' strategy
Core [Tool] Registered: read_file | Description: "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories."
Core:AgentTARS Registered tool: read_file
Core [Tool] Registered: read_multiple_files | Description: "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories."
Core:AgentTARS Registered tool: read_multiple_files
Core [Tool] Registered: write_file | Description: "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories."
Core:AgentTARS Registered tool: write_file
Core [Tool] Registered: edit_file | Description: "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories."
Core:AgentTARS Registered tool: edit_file
Core [Tool] Registered: create_directory | Description: "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories."
Core:AgentTARS Registered tool: create_directory
Core [Tool] Registered: list_directory | Description: "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories."
Core:AgentTARS Registered tool: list_directory
Core [Tool] Registered: directory_tree | Description: "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories."
Core:AgentTARS Registered tool: directory_tree
Core [Tool] Registered: move_file | Description: "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories."
Core:AgentTARS Registered tool: move_file
Core [Tool] Registered: search_files | Description: "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories."
Core:AgentTARS Registered tool: search_files
Core [Tool] Registered: get_file_info | Description: "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories."
Core:AgentTARS Registered tool: get_file_info
Core [Tool] Registered: list_allowed_directories | Description: "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files."
Core:AgentTARS Registered tool: list_allowed_directories
Core:AgentTARS Registered 11 MCP tools from 'filesystem'
Core [Tool] Registered: run_command | Description: "[commands] Run a command on this darwin machine"
Core:AgentTARS Registered tool: run_command
Core [Tool] Registered: run_script | Description: "[commands] Run a script on this darwin machine"
Core:AgentTARS Registered tool: run_script
Core:AgentTARS Registered 2 MCP tools from 'commands'
Core:AgentTARS ✅ In-memory MCP initialization complete
Core [Tool] Registered: final_answer | Description: "Generate a focused report or answer after completing research or information gathering"
Core:AgentTARS Registered 1 planner tools
Core:AgentTARS ✅ AgentTARS initialization complete
Core:AgentTARS 

Core:AgentTARS ═════════════════════════
Core:AgentTARS 🧰 35 Tools Registered 🧰
Core:AgentTARS ═════════════════════════
Core:AgentTARS 
📦 general (3):
Core:AgentTARS   • browser_vision_control
Core:AgentTARS   • final_answer
Core:AgentTARS   • web_search
Core:AgentTARS 
📦 browser (19):
Core:AgentTARS   • browser_click
Core:AgentTARS   • browser_close_tab
Core:AgentTARS   • browser_evaluate
Core:AgentTARS   • browser_form_input_fill
Core:AgentTARS   • browser_get_clickable_elements
Core:AgentTARS   • browser_get_markdown
Core:AgentTARS   • browser_go_back
Core:AgentTARS   • browser_go_forward
Core:AgentTARS   • browser_hover
Core:AgentTARS   • browser_navigate
Core:AgentTARS   • browser_new_tab
Core:AgentTARS   • browser_press_key
Core:AgentTARS   • browser_read_links
Core:AgentTARS   • browser_refresh
Core:AgentTARS   • browser_screenshot
Core:AgentTARS   • browser_scroll
Core:AgentTARS   • browser_select
Core:AgentTARS   • browser_switch_tab
Core:AgentTARS   • browser_tab_list
Core:AgentTARS 
📦 filesystem (11):
Core:AgentTARS   • create_directory
Core:AgentTARS   • directory_tree
Core:AgentTARS   • edit_file
Core:AgentTARS   • get_file_info
Core:AgentTARS   • list_allowed_directories
Core:AgentTARS   • list_directory
Core:AgentTARS   • move_file
Core:AgentTARS   • read_file
Core:AgentTARS   • read_multiple_files
Core:AgentTARS   • search_files
Core:AgentTARS   • write_file
Core:AgentTARS 
📦 commands (2):
Core:AgentTARS   • run_command
Core:AgentTARS   • run_script
Core:AgentTARS 
═════════════════════════
Core:AgentTARS ✨ Total: 35 tools ready to use
Core:AgentTARS ═════════════════════════

Core:AgentTARS 🔌 Connecting to MCP server: desktop-commander
Core:AgentTARS Initializing MCP client v2 for desktop-commander
Loading server.ts
Setting up request handlers...
[desktop-commander] Enhanced FilteredStdioServerTransport initialized
Core:AgentTARS MCP client v2 initialized successfully for desktop-commander, found 21 tools
Core:AgentTARS ✅ Connected to MCP server desktop-commander with 21 tools
Loading server.ts
Setting up request handlers...
[desktop-commander] Enhanced FilteredStdioServerTransport initialized
Core:AgentTARS MCP client v2 initialized successfully for desktop-commander, found 21 tools
Core [Tool] Registered: get_config | Description: "[desktop-commander] 
                        Get the complete server configuration as JSON. Config includes fields for:
                        - blockedCommands (array of blocked shell commands)
                        - defaultShell (shell to use for commands)
                        - allowedDirectories (paths the server can access)
                        - fileReadLineLimit (max lines for read_file, default 1000)
                        - fileWriteLineLimit (max lines per write_file call, default 50)
                        - telemetryEnabled (boolean for telemetry opt-in/out)
                        - currentClient (information about the currently connected MCP client)
                        - clientHistory (history of all clients that have connected)
                        - version (version of the DesktopCommander)
                        - systemInfo (operating system and environment details)
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: set_config_value | Description: "[desktop-commander] 
                        Set a specific configuration value by key.
                        
                        WARNING: Should be used in a separate chat from file operations and 
                        command execution to prevent security issues.
                        
                        Config keys include:
                        - blockedCommands (array)
                        - defaultShell (string)
                        - allowedDirectories (array of paths)
                        - fileReadLineLimit (number, max lines for read_file)
                        - fileWriteLineLimit (number, max lines per write_file call)
                        - telemetryEnabled (boolean)
                        
                        IMPORTANT: Setting allowedDirectories to an empty array ([]) allows full access 
                        to the entire file system, regardless of the operating system.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: read_file | Description: "[desktop-commander] 
                        Read the contents of a file from the file system or a URL with optional offset and length parameters.
                        
                        Prefer this over 'execute_command' with cat/type for viewing files.
                        
                        Supports partial file reading with:
                        - 'offset' (start line, default: 0)
                          * Positive: Start from line N (0-based indexing)
                          * Negative: Read last N lines from end (tail behavior)
                        - 'length' (max lines to read, default: configurable via 'fileReadLineLimit' setting, initially 1000)
                          * Used with positive offsets for range reading
                          * Ignored when offset is negative (reads all requested tail lines)
                        
                        Examples:
                        - offset: 0, length: 10     → First 10 lines
                        - offset: 100, length: 5    → Lines 100-104
                        - offset: -20               → Last 20 lines  
                        - offset: -5, length: 10    → Last 5 lines (length ignored)
                        
                        Performance optimizations:
                        - Large files with negative offsets use reverse reading for efficiency
                        - Large files with deep positive offsets use byte estimation
                        - Small files use fast readline streaming
                        
                        When reading from the file system, only works within allowed directories.
                        Can fetch content from URLs when isUrl parameter is set to true
                        (URLs are always read in full regardless of offset/length).
                        
                        Handles text files normally and image files are returned as viewable images.
                        Recognized image types: PNG, JPEG, GIF, WebP.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: read_multiple_files | Description: "[desktop-commander] 
                        Read the contents of multiple files simultaneously.
                        
                        Each file's content is returned with its path as a reference.
                        Handles text files normally and renders images as viewable content.
                        Recognized image types: PNG, JPEG, GIF, WebP.
                        
                        Failed reads for individual files won't stop the entire operation.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: write_file | Description: "[desktop-commander] 
                        Write or append to file contents. 

                        CHUNKING IS STANDARD PRACTICE: Always write files in chunks of 25-30 lines maximum.
                        This is the normal, recommended way to write files - not an emergency measure.

                        STANDARD PROCESS FOR ANY FILE:
                        1. FIRST → write_file(filePath, firstChunk, {mode: 'rewrite'})  [≤30 lines]
                        2. THEN → write_file(filePath, secondChunk, {mode: 'append'})   [≤30 lines]
                        3. CONTINUE → write_file(filePath, nextChunk, {mode: 'append'}) [≤30 lines]

                        ALWAYS CHUNK PROACTIVELY - don't wait for performance warnings!

                        WHEN TO CHUNK (always be proactive):
                        1. Any file expected to be longer than 25-30 lines
                        2. When writing multiple files in sequence
                        3. When creating documentation, code files, or configuration files
                        
                        HANDLING CONTINUATION ("Continue" prompts):
                        If user asks to "Continue" after an incomplete operation:
                        1. Read the file to see what was successfully written
                        2. Continue writing ONLY the remaining content using {mode: 'append'}
                        3. Keep chunks to 25-30 lines each
                        
                        Files over 50 lines will generate performance notes but are still written successfully.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: create_directory | Description: "[desktop-commander] 
                        Create a new directory or ensure a directory exists.
                        
                        Can create multiple nested directories in one operation.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: list_directory | Description: "[desktop-commander] 
                        Get a detailed listing of all files and directories in a specified path.
                        
                        Use this instead of 'execute_command' with ls/dir commands.
                        Results distinguish between files and directories with [FILE] and [DIR] prefixes.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: move_file | Description: "[desktop-commander] 
                        Move or rename files and directories.
                        
                        Can move files between directories and rename them in a single operation.
                        Both source and destination must be within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: search_files | Description: "[desktop-commander] 
                        Finds files by name using a case-insensitive substring matching.
                        
                        Use this instead of 'execute_command' with find/dir/ls for locating files.
                        Searches through all subdirectories from the starting path.
                        
                        Has a default timeout of 30 seconds which can be customized using the timeoutMs parameter.
                        Only searches within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: search_code | Description: "[desktop-commander] 
                        Search for text/code patterns within file contents using ripgrep.
                        
                        Use this instead of 'execute_command' with grep/find for searching code content.
                        Fast and powerful search similar to VS Code search functionality.
                        
                        Supports regular expressions, file pattern filtering, and context lines.
                        Has a default timeout of 30 seconds which can be customized.
                        Only searches within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: get_file_info | Description: "[desktop-commander] 
                        Retrieve detailed metadata about a file or directory including:
                        - size
                        - creation time
                        - last modified time 
                        - permissions
                        - type
                        - lineCount (for text files)
                        - lastLine (zero-indexed number of last line, for text files)
                        - appendPosition (line number for appending, for text files)
                        
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: edit_block | Description: "[desktop-commander] 
                        Apply surgical text replacements to files.
                        
                        BEST PRACTICE: Make multiple small, focused edits rather than one large edit.
                        Each edit_block call should change only what needs to be changed - include just enough 
                        context to uniquely identify the text being modified.
                        
                        Takes:
                        - file_path: Path to the file to edit
                        - old_string: Text to replace
                        - new_string: Replacement text
                        - expected_replacements: Optional parameter for number of replacements
                        
                        By default, replaces only ONE occurrence of the search text.
                        To replace multiple occurrences, provide the expected_replacements parameter with
                        the exact number of matches expected.
                        
                        UNIQUENESS REQUIREMENT: When expected_replacements=1 (default), include the minimal
                        amount of context necessary (typically 1-3 lines) before and after the change point,
                        with exact whitespace and indentation.
                        
                        When editing multiple sections, make separate edit_block calls for each distinct change
                        rather than one large replacement.
                        
                        When a close but non-exact match is found, a character-level diff is shown in the format:
                        common_prefix{-removed-}{+added+}common_suffix to help you identify what's different.
                        
                        Similar to write_file, there is a configurable line limit (fileWriteLineLimit) that warns
                        if the edited file exceeds this limit. If this happens, consider breaking your edits into
                        smaller, more focused changes.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: start_process | Description: "[desktop-commander] 
                        Start a new terminal process with intelligent state detection.
                        
                        PRIMARY TOOL FOR FILE ANALYSIS AND DATA PROCESSING
                        This is the ONLY correct tool for analyzing local files (CSV, JSON, logs, etc.).
                        The analysis tool CANNOT access local files and WILL FAIL - always use processes for file-based work.
                        
                        CRITICAL RULE: For ANY local file work, ALWAYS use this tool + interact_with_process, NEVER use analysis/REPL tool.
                        
                        Running on macOS. Default shell: zsh.
        
MACOS-SPECIFIC NOTES:
- Package manager: brew (Homebrew) is commonly used
- Python 3 might be 'python3' command, not 'python'
- Some GNU tools have different names (e.g., gsed instead of sed)
- System Integrity Protection (SIP) may block certain operations
- Use 'open' command to open files/applications from terminal
                        
                        REQUIRED WORKFLOW FOR LOCAL FILES:
                        1. start_process("python3 -i") - Start Python REPL for data analysis
                        2. interact_with_process(pid, "import pandas as pd, numpy as np")
                        3. interact_with_process(pid, "df = pd.read_csv('/absolute/path/file.csv')")
                        4. interact_with_process(pid, "print(df.describe())")
                        5. Continue analysis with pandas, matplotlib, seaborn, etc.
                        
                        COMMON FILE ANALYSIS PATTERNS:
                        • start_process("python3 -i") → Python REPL for data analysis (RECOMMENDED)
                        • start_process("node -i") → Node.js for JSON processing  
                        • start_process("cut -d',' -f1 file.csv | sort | uniq -c") → Quick CSV analysis
                        • start_process("wc -l /path/file.csv") → Line counting
                        • start_process("head -10 /path/file.csv") → File preview
                        
                        BINARY FILE SUPPORT:
                        For PDF, Excel, Word, archives, databases, and other binary formats, use process tools with appropriate libraries or command-line utilities.
                        
                        INTERACTIVE PROCESSES FOR DATA ANALYSIS:
                        1. start_process("python3 -i") - Start Python REPL for data work
                        2. start_process("node -i") - Start Node.js REPL for JSON/JS
                        3. start_process("bash") - Start interactive bash shell
                        4. Use interact_with_process() to send commands
                        5. Use read_process_output() to get responses
                        
                        SMART DETECTION:
                        - Detects REPL prompts (>>>, >, $, etc.)
                        - Identifies when process is waiting for input
                        - Recognizes process completion vs timeout
                        - Early exit prevents unnecessary waiting
                        
                        STATES DETECTED:
                        Process waiting for input (shows prompt)
                        Process finished execution  
                        Process running (use read_process_output)
                        
                        ALWAYS USE FOR: Local file analysis, CSV processing, data exploration, system commands
                        NEVER USE ANALYSIS TOOL FOR: Local file access (analysis tool is browser-only and WILL FAIL)
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: read_process_output | Description: "[desktop-commander] 
                        Read output from a running process with intelligent completion detection.
                        
                        Automatically detects when process is ready for more input instead of timing out.
                        
                        SMART FEATURES:
                        - Early exit when REPL shows prompt (>>>, >, etc.)
                        - Detects process completion vs still running
                        - Prevents hanging on interactive prompts
                        - Clear status messages about process state
                        
                        REPL USAGE:
                        - Stops immediately when REPL prompt detected
                        - Shows clear status: waiting for input vs finished
                        - Shorter timeouts needed due to smart detection
                        - Works with Python, Node.js, R, Julia, etc.
                        
                        DETECTION STATES:
                        Process waiting for input (ready for interact_with_process)
                        Process finished execution
                        Timeout reached (may still be running)
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: interact_with_process | Description: "[desktop-commander] 
                        Send input to a running process and automatically receive the response.
                        
                        CRITICAL: THIS IS THE PRIMARY TOOL FOR ALL LOCAL FILE ANALYSIS
                        For ANY local file analysis (CSV, JSON, data processing), ALWAYS use this instead of the analysis tool.
                        The analysis tool CANNOT access local files and WILL FAIL - use processes for ALL file-based work.
                        
                        FILE ANALYSIS PRIORITY ORDER (MANDATORY):
                        1. ALWAYS FIRST: Use this tool (start_process + interact_with_process) for local data analysis
                        2. ALTERNATIVE: Use command-line tools (cut, awk, grep) for quick processing  
                        3. NEVER EVER: Use analysis tool for local file access (IT WILL FAIL)
                        
                        REQUIRED INTERACTIVE WORKFLOW FOR FILE ANALYSIS:
                        1. Start REPL: start_process("python3 -i")
                        2. Load libraries: interact_with_process(pid, "import pandas as pd, numpy as np")
                        3. Read file: interact_with_process(pid, "df = pd.read_csv('/absolute/path/file.csv')")
                        4. Analyze: interact_with_process(pid, "print(df.describe())")
                        5. Continue: interact_with_process(pid, "df.groupby('column').size()")
                        
                        BINARY FILE PROCESSING WORKFLOWS:
                        Use appropriate Python libraries (PyPDF2, pandas, docx2txt, etc.) or command-line tools for binary file analysis.
                        
                        SMART DETECTION:
                        - Automatically waits for REPL prompt (>>>, >, etc.)
                        - Detects errors and completion states
                        - Early exit prevents timeout delays
                        - Clean output formatting (removes prompts)
                        
                        SUPPORTED REPLs:
                        - Python: python3 -i (RECOMMENDED for data analysis)
                        - Node.js: node -i  
                        - R: R
                        - Julia: julia
                        - Shell: bash, zsh
                        - Database: mysql, postgres
                        
                        PARAMETERS:
                        - pid: Process ID from start_process
                        - input: Code/command to execute
                        - timeout_ms: Max wait (default: 8000ms)
                        - wait_for_prompt: Auto-wait for response (default: true)
                        
                        Returns execution result with status indicators.
                        
                        ALWAYS USE FOR: CSV analysis, JSON processing, file statistics, data visualization prep, ANY local file work
                        NEVER USE ANALYSIS TOOL FOR: Local file access (it cannot read files from disk and WILL FAIL)
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: force_terminate | Description: "[desktop-commander] 
                        Force terminate a running terminal session.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: list_sessions | Description: "[desktop-commander] 
                        List all active terminal sessions.
                        
                        Shows session status including:
                        - PID: Process identifier  
                        - Blocked: Whether session is waiting for input
                        - Runtime: How long the session has been running
                        
                        DEBUGGING REPLs:
                        - "Blocked: true" often means REPL is waiting for input
                        - Use this to verify sessions are running before sending input
                        - Long runtime with blocked status may indicate stuck process
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: list_processes | Description: "[desktop-commander] 
                        List all running processes.
                        
                        Returns process information including PID, command name, CPU usage, and memory usage.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: kill_process | Description: "[desktop-commander] 
                        Terminate a running process by PID.
                        
                        Use with caution as this will forcefully terminate the specified process.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: get_usage_stats | Description: "[desktop-commander] 
                        Get usage statistics for debugging and analysis.
                        
                        Returns summary of tool usage, success/failure rates, and performance metrics.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: give_feedback_to_desktop_commander | Description: "[desktop-commander] 
                        Open feedback form in browser to provide feedback about Desktop Commander.
                        
                        IMPORTANT: This tool simply opens the feedback form - no pre-filling available.
                        The user will fill out the form manually in their browser.
                        
                        WORKFLOW:
                        1. When user agrees to give feedback, just call this tool immediately
                        2. No need to ask questions or collect information
                        3. Tool opens form with only usage statistics pre-filled automatically:
                           - tool_call_count: Number of commands they've made
                           - days_using: How many days they've used Desktop Commander
                           - platform: Their operating system (Mac/Windows/Linux)
                           - client_id: Analytics identifier
                        
                        All survey questions will be answered directly in the form:
                        - Job title and technical comfort level
                        - Company URL for industry context
                        - Other AI tools they use
                        - Desktop Commander's biggest advantage
                        - How they typically use it
                        - Recommendation likelihood (0-10)
                        - User study participation interest
                        - Email and any additional feedback
                        
                        EXAMPLE INTERACTION:
                        User: "sure, I'll give feedback"
                        Claude: "Perfect! Let me open the feedback form for you."
                        [calls tool immediately]
                        
                        No parameters are needed - just call the tool to open the form.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core:AgentTARS ✅ Connected to MCP server desktop-commander with 21 tools
Core:AgentTARS 🔌 Connecting to MCP server: minimax
Core:AgentTARS Initializing MCP client v2 for minimax
Core:AgentTARS MCP client v2 initialized successfully for minimax, found 9 tools
Core:AgentTARS ✅ Connected to MCP server minimax with 9 tools
Core:AgentTARS MCP client v2 initialized successfully for minimax, found 9 tools
Core [Tool] Registered: text_to_audio | Description: "[minimax] Convert text to audio with a given voice and save the output audio file to a given directory.
    Directory is optional, if not provided, the output file will be saved to $HOME/Desktop.
    Voice id is optional, if not provided, the default voice will be used.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

    Args:
        text (str): The text to convert to speech.
        voice_id (str, optional): The id of the voice to use. For example, "male-qn-qingse"/"audiobook_female_1"/"cute_boy"/"Charming_Lady"...
        model (string, optional): The model to use.
        speed (float, optional): Speed of the generated audio. Controls the speed of the generated speech. Values range from 0.5 to 2.0, with 1.0 being the default speed. 
        vol (float, optional): Volume of the generated audio. Controls the volume of the generated speech. Values range from 0 to 10, with 1 being the default volume.
        pitch (int, optional): Pitch of the generated audio. Controls the speed of the generated speech. Values range from -12 to 12, with 0 being the default speed.
        emotion (str, optional): Emotion of the generated audio. Controls the emotion of the generated speech. Values range ["happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"], with "happy" being the default emotion.
        sample_rate (int, optional): Sample rate of the generated audio. Controls the sample rate of the generated speech. Values range [8000,16000,22050,24000,32000,44100] with 32000 being the default sample rate.
        bitrate (int, optional): Bitrate of the generated audio. Controls the bitrate of the generated speech. Values range [32000,64000,128000,256000] with 128000 being the default bitrate.
        channel (int, optional): Channel of the generated audio. Controls the channel of the generated speech. Values range [1, 2] with 1 being the default channel.
        format (str, optional): Format of the generated audio. Controls the format of the generated speech. Values range ["pcm", "mp3","flac"] with "mp3" being the default format.
        language_boost (str, optional): Language boost of the generated audio. Controls the language boost of the generated speech. Values range ['Chinese', 'Chinese,Yue', 'English', 'Arabic', 'Russian', 'Spanish', 'French', 'Portuguese', 'German', 'Turkish', 'Dutch', 'Ukrainian', 'Vietnamese', 'Indonesian', 'Japanese', 'Italian', 'Korean', 'Thai', 'Polish', 'Romanian', 'Greek', 'Czech', 'Finnish', 'Hindi', 'auto'] with "auto" being the default language boost.
        output_directory (str): The directory to save the audio to.

    Returns:
        Text content with the path to the output file and name of the voice used.
    "
Core [Tool] Registered: list_voices | Description: "[minimax] List all voices available.

    Args:
        voice_type (str, optional): The type of voices to list. Values range ["all", "system", "voice_cloning"], with "all" being the default.
    Returns:
        Text content with the list of voices.
    "
Core [Tool] Registered: voice_clone | Description: "[minimax] Clone a voice using provided audio files. The new voice will be charged upon first use.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        voice_id (str): The id of the voice to use.
        file (str): The path to the audio file to clone or a URL to the audio file.
        text (str, optional): The text to use for the demo audio.
        is_url (bool, optional): Whether the file is a URL. Defaults to False.
        output_directory (str): The directory to save the demo audio to.
    Returns:
        Text content with the voice id of the cloned voice.
    "
Core [Tool] Registered: play_audio | Description: "[minimax] Play an audio file. Supports WAV and MP3 formats. Not supports video.

     Args:
        input_file_path (str): The path to the audio file to play.
        is_url (bool, optional): Whether the audio file is a URL.
    Returns:
        Text content with the path to the audio file.
    "
Core [Tool] Registered: generate_video | Description: "[minimax] Generate a video from a prompt.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        model (str, optional): The model to use. Values range ["T2V-01", "T2V-01-Director", "I2V-01", "I2V-01-Director", "I2V-01-live", "MiniMax-Hailuo-02"]. "Director" supports inserting instructions for camera movement control. "I2V" for image to video. "T2V" for text to video. "MiniMax-Hailuo-02" is the latest model with best effect, ultra-clear quality and precise response.
        prompt (str): The prompt to generate the video from. When use Director model, the prompt supports 15 Camera Movement Instructions (Enumerated Values)
            -Truck: [Truck left], [Truck right]
            -Pan: [Pan left], [Pan right]
            -Push: [Push in], [Pull out]
            -Pedestal: [Pedestal up], [Pedestal down]
            -Tilt: [Tilt up], [Tilt down]
            -Zoom: [Zoom in], [Zoom out]
            -Shake: [Shake]
            -Follow: [Tracking shot]
            -Static: [Static shot]
        first_frame_image (str): The first frame image. The model must be "I2V" Series.
        duration (int, optional): The duration of the video. The model must be "MiniMax-Hailuo-02". Values can be 6 and 10.
        resolution (str, optional): The resolution of the video. The model must be "MiniMax-Hailuo-02". Values range ["768P", "1080P"]
        output_directory (str): The directory to save the video to.
        async_mode (bool, optional): Whether to use async mode. Defaults to False. If True, the video generation task will be submitted asynchronously and the response will return a task_id. Should use `query_video_generation` tool to check the status of the task and get the result.
    Returns:
        Text content with the path to the output video file.
    "
Core [Tool] Registered: query_video_generation | Description: "[minimax] Query the status of a video generation task.

    Args:
        task_id (str): The task ID to query. Should be the task_id returned by `generate_video` tool if `async_mode` is True.
        output_directory (str): The directory to save the video to.
    Returns:
        Text content with the status of the task.
    "
Core [Tool] Registered: text_to_image | Description: "[minimax] Generate a image from a prompt.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        model (str, optional): The model to use. Values range ["image-01"], with "image-01" being the default.
        prompt (str): The prompt to generate the image from.
        aspect_ratio (str, optional): The aspect ratio of the image. Values range ["1:1", "16:9","4:3", "3:2", "2:3", "3:4", "9:16", "21:9"], with "1:1" being the default.
        n (int, optional): The number of images to generate. Values range [1, 9], with 1 being the default.
        prompt_optimizer (bool, optional): Whether to optimize the prompt. Values range [True, False], with True being the default.
        output_directory (str): The directory to save the image to.
    Returns:
        Text content with the path to the output image file.
    "
Core [Tool] Registered: music_generation | Description: "[minimax] Create a music generation task using AI models. Generate music from prompt and lyrics.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

    Args:
        prompt (str): Music creation inspiration describing style, mood, scene, etc.
            Example: "Pop music, sad, suitable for rainy nights". Character range: [10, 300]
        lyrics (str): Song lyrics for music generation.
            Use newline (\n) to separate each line of lyrics. Supports lyric structure tags [Intro][Verse][Chorus][Bridge][Outro] 
            to enhance musicality. Character range: [10, 600] (each Chinese character, punctuation, and letter counts as 1 character)
        stream (bool, optional): Whether to enable streaming mode. Defaults to False
        sample_rate (int, optional): Sample rate of generated music. Values: [16000, 24000, 32000, 44100]
        bitrate (int, optional): Bitrate of generated music. Values: [32000, 64000, 128000, 256000]
        format (str, optional): Format of generated music. Values: ["mp3", "wav", "pcm"]. Defaults to "mp3"
        output_directory (str, optional): Directory to save the generated music file
        
    Note: Currently supports generating music up to 1 minute in length.

    Returns:
        Text content with the path to the generated music file or generation status.
    "
Core [Tool] Registered: voice_design | Description: "[minimax] Generate a voice based on description prompts.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        prompt (str): The prompt to generate the voice from.
        preview_text (str): The text to preview the voice.
        voice_id (str, optional): The id of the voice to use. For example, "male-qn-qingse"/"audiobook_female_1"/"cute_boy"/"Charming_Lady"...
        output_directory (str, optional): The directory to save the voice to.
    Returns:
        Text content with the path to the output voice file.
    "
Core:AgentTARS ✅ Connected to MCP server minimax with 9 tools
Core:AgentTARS 🔌 Connecting to MCP server: davinci-mcp
Core:AgentTARS Initializing MCP client v2 for davinci-mcp
Core:AgentTARS MCP client v2 initialized successfully for davinci-mcp, found 13 tools
Core [Tool] Registered: get_version | Description: "[davinci-mcp] Get DaVinci Resolve version information"
Core [Tool] Registered: get_current_page | Description: "[davinci-mcp] Get the current page open in DaVinci Resolve (Edit, Color, Fusion, etc.)"
Core [Tool] Registered: switch_page | Description: "[davinci-mcp] Switch to a specific page in DaVinci Resolve"
Core [Tool] Registered: list_projects | Description: "[davinci-mcp] List all available projects in the current database"
Core [Tool] Registered: get_current_project | Description: "[davinci-mcp] Get the name of the currently open project"
Core [Tool] Registered: open_project | Description: "[davinci-mcp] Open a project by name"
Core [Tool] Registered: create_project | Description: "[davinci-mcp] Create a new project with the given name"
Core [Tool] Registered: list_timelines | Description: "[davinci-mcp] List all timelines in the current project"
Core [Tool] Registered: get_current_timeline | Description: "[davinci-mcp] Get the name of the current timeline"
Core [Tool] Registered: create_timeline | Description: "[davinci-mcp] Create a new timeline with the given name"
Core [Tool] Registered: switch_timeline | Description: "[davinci-mcp] Switch to a timeline by name"
Core [Tool] Registered: list_media_clips | Description: "[davinci-mcp] List all clips in the media pool"
Core [Tool] Registered: import_media | Description: "[davinci-mcp] Import a media file into the media pool"
Core:AgentTARS ✅ Connected to MCP server davinci-mcp with 13 tools
EventStream Subscribed to events (total subscribers: 2)
EventStream Subscribed to events (total subscribers: 3)
Core:AgentTARS MCP client v2 initialized successfully for davinci-mcp, found 13 tools
Core:AgentTARS ✅ Connected to MCP server davinci-mcp with 13 tools
EventStream Subscribed to events (total subscribers: 2)
EventStream Subscribed to events (total subscribers: 3)
[AgentSnapshot] AgentSnapshot.run called with options object
[AgentSnapshot] Hooked into agent: 7PJ4zS_inmAsx9X_wZzxg
[AgentSnapshot] Executing agent with streaming mode
ExecutionController Agent execution started with status: executing
Core:AgentTARS Initializing AgentTARS ...
Core:AgentTARS:BrowserToolsManager Initialized with mode: hybrid
Core:AgentTARS 🖥️ Initializing GUI Agent for visual browser control
Core:AgentTARS ✅ GUI Agent initialized successfully
Core:AgentTARS 🔍 Initializing search tools with direct integration
Core:AgentTARS:SearchToolProvider Mapped provider browser_search to browser_search
Core:AgentTARS:SearchToolProvider Search tool provider initialized with browser_search
Core [Tool] Registered: web_search | Description: "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️

Search the web for information. For best results:
1) Use CONCISE queries (3-5 words ideal)
2) Include only ESSENTIAL keywords, not full questions
3) For complex topics, use multiple simple searches instead of one long query
4) Focus on specific terms that will appear on relevant pages"
Core:AgentTARS ✅ Search tools initialized successfully
Core:AgentTARS Using shared browser instance for MCP servers
Core:AgentTARS ✅ Connected to browser MCP server
Core:AgentTARS ✅ Connected to filesystem MCP server
Core:AgentTARS ✅ Connected to commands MCP server
Core:AgentTARS:BrowserToolsManager 🌐 Activating browser control mode: hybrid
Core:AgentTARS:BrowserToolsManager 🔍 Strategy: BrowserHybridStrategy
Core [Tool] Registered: browser_vision_control | Description: "A browser operation tool based on visual understanding, perform the next action to complete the task.

## Action Space

click(point='<point>x1 y1</point>')            - Click at the specified coordinates
left_double(point='<point>x1 y1</point>')      - Double-click at the specified coordinates
right_single(point='<point>x1 y1</point>')     - Right-click at the specified coordinates
drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') - Drag from start to end point
hotkey(key='ctrl c')                           - Press keyboard shortcut (use space to separate keys, lowercase)
type(content='xxx')                            - Type text content (use \', \", and \n for special characters)
scroll(point='<point>x1 y1</point>', direction='down or up or right or left') - Scroll in specified direction
wait()                                         - Wait 5 seconds and take a screenshot to check for changes

## Note
- Folow user lanuage in in `thought` part.
- Describe your thought in `step` part.
- Describe your action in `Step` part.
- Extract the data your see in `pageData` part.
- This tool is for operational tasks, not for collect information.
"
Core [Tool] Registered: browser_navigate | Description: "[browser] Navigate to a URL"
Core [Tool] Registered: browser_go_back | Description: "[browser] Go back to the previous page, or close tab if no history exists"
Core [Tool] Registered: browser_go_forward | Description: "[browser] Go forward to the next page"
Core [Tool] Registered: browser_refresh | Description: "[browser] Refresh the current page"
Core [Tool] Registered: browser_get_markdown | Description: "[browser] Get the content of the current page as markdown with pagination support"
Core [Tool] Registered: browser_screenshot | Description: "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call."
Core [Tool] Registered: browser_click | Description: "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_click
Core [Tool] Registered: browser_form_input_fill | Description: "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_form_input_fill
Core [Tool] Registered: browser_select | Description: "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_select
Core [Tool] Registered: browser_hover | Description: "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_hover
Core [Tool] Registered: browser_evaluate | Description: "[browser] Execute JavaScript in the browser console"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_evaluate
Core [Tool] Registered: browser_get_clickable_elements | Description: "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_get_clickable_elements
Core [Tool] Registered: browser_read_links | Description: "[browser] Get all links on the current page"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_read_links
Core [Tool] Registered: browser_scroll | Description: "[browser] Scroll the page"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_scroll
Core [Tool] Registered: browser_tab_list | Description: "[browser] Get the list of tabs"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_tab_list
Core [Tool] Registered: browser_new_tab | Description: "[browser] Open a new tab"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_new_tab
Core [Tool] Registered: browser_close_tab | Description: "[browser] Close the current tab"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_close_tab
Core [Tool] Registered: browser_switch_tab | Description: "[browser] Switch to a specific tab"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_switch_tab
Core [Tool] Registered: browser_press_key | Description: "[browser] Press a key on the keyboard"
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered browser tool: browser_press_key
Core:AgentTARS:BrowserToolsManager:BrowserHybridStrategy Registered 20 MCP browser tools
Core:AgentTARS:BrowserToolsManager ✅ Registered 20 browser tools:
Core:AgentTARS:BrowserToolsManager   • browser_vision_control
Core:AgentTARS:BrowserToolsManager   • browser_navigate
Core:AgentTARS:BrowserToolsManager   • browser_go_back
Core:AgentTARS:BrowserToolsManager   • browser_go_forward
Core:AgentTARS:BrowserToolsManager   • browser_refresh
Core:AgentTARS:BrowserToolsManager   • browser_get_markdown
Core:AgentTARS:BrowserToolsManager   • browser_screenshot
Core:AgentTARS:BrowserToolsManager   • browser_click
Core:AgentTARS:BrowserToolsManager   • browser_form_input_fill
Core:AgentTARS:BrowserToolsManager   • browser_select
Core:AgentTARS:BrowserToolsManager   • browser_hover
Core:AgentTARS:BrowserToolsManager   • browser_evaluate
Core:AgentTARS:BrowserToolsManager   • browser_get_clickable_elements
Core:AgentTARS:BrowserToolsManager   • browser_read_links
Core:AgentTARS:BrowserToolsManager   • browser_scroll
Core:AgentTARS:BrowserToolsManager   • browser_tab_list
Core:AgentTARS:BrowserToolsManager   • browser_new_tab
Core:AgentTARS:BrowserToolsManager   • browser_close_tab
Core:AgentTARS:BrowserToolsManager   • browser_switch_tab
Core:AgentTARS:BrowserToolsManager   • browser_press_key
Core:AgentTARS ✅ Registered 20 browser tools using 'hybrid' strategy
Core [Tool] Registered: read_file | Description: "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories."
Core:AgentTARS Registered tool: read_file
Core [Tool] Registered: read_multiple_files | Description: "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories."
Core:AgentTARS Registered tool: read_multiple_files
Core [Tool] Registered: write_file | Description: "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories."
Core:AgentTARS Registered tool: write_file
Core [Tool] Registered: edit_file | Description: "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories."
Core:AgentTARS Registered tool: edit_file
Core [Tool] Registered: create_directory | Description: "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories."
Core:AgentTARS Registered tool: create_directory
Core [Tool] Registered: list_directory | Description: "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories."
Core:AgentTARS Registered tool: list_directory
Core [Tool] Registered: directory_tree | Description: "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories."
Core:AgentTARS Registered tool: directory_tree
Core [Tool] Registered: move_file | Description: "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories."
Core:AgentTARS Registered tool: move_file
Core [Tool] Registered: search_files | Description: "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories."
Core:AgentTARS Registered tool: search_files
Core [Tool] Registered: get_file_info | Description: "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories."
Core:AgentTARS Registered tool: get_file_info
Core [Tool] Registered: list_allowed_directories | Description: "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files."
Core:AgentTARS Registered tool: list_allowed_directories
Core:AgentTARS Registered 11 MCP tools from 'filesystem'
Core [Tool] Registered: run_command | Description: "[commands] Run a command on this darwin machine"
Core:AgentTARS Registered tool: run_command
Core [Tool] Registered: run_script | Description: "[commands] Run a script on this darwin machine"
Core:AgentTARS Registered tool: run_script
Core:AgentTARS Registered 2 MCP tools from 'commands'
Core:AgentTARS ✅ In-memory MCP initialization complete
Core [Tool] Registered: final_answer | Description: "Generate a focused report or answer after completing research or information gathering"
Core:AgentTARS Registered 1 planner tools
Core:AgentTARS ✅ AgentTARS initialization complete
Core:AgentTARS 

Core:AgentTARS ═════════════════════════
Core:AgentTARS 🧰 70 Tools Registered 🧰
Core:AgentTARS ═════════════════════════
Core:AgentTARS 
📦 general (3):
Core:AgentTARS   • browser_vision_control
Core:AgentTARS   • final_answer
Core:AgentTARS   • web_search
Core:AgentTARS 
📦 browser (19):
Core:AgentTARS   • browser_click
Core:AgentTARS   • browser_close_tab
Core:AgentTARS   • browser_evaluate
Core:AgentTARS   • browser_form_input_fill
Core:AgentTARS   • browser_get_clickable_elements
Core:AgentTARS   • browser_get_markdown
Core:AgentTARS   • browser_go_back
Core:AgentTARS   • browser_go_forward
Core:AgentTARS   • browser_hover
Core:AgentTARS   • browser_navigate
Core:AgentTARS   • browser_new_tab
Core:AgentTARS   • browser_press_key
Core:AgentTARS   • browser_read_links
Core:AgentTARS   • browser_refresh
Core:AgentTARS   • browser_screenshot
Core:AgentTARS   • browser_scroll
Core:AgentTARS   • browser_select
Core:AgentTARS   • browser_switch_tab
Core:AgentTARS   • browser_tab_list
Core:AgentTARS 
📦 filesystem (11):
Core:AgentTARS   • create_directory
Core:AgentTARS   • directory_tree
Core:AgentTARS   • edit_file
Core:AgentTARS   • get_file_info
Core:AgentTARS   • list_allowed_directories
Core:AgentTARS   • list_directory
Core:AgentTARS   • move_file
Core:AgentTARS   • read_file
Core:AgentTARS   • read_multiple_files
Core:AgentTARS   • search_files
Core:AgentTARS   • write_file
Core:AgentTARS 
📦 commands (2):
Core:AgentTARS   • run_command
Core:AgentTARS   • run_script
Core:AgentTARS 
📦 desktop-commander (13):
Core:AgentTARS   • edit_block
Core:AgentTARS   • force_terminate
Core:AgentTARS   • get_config
Core:AgentTARS   • get_usage_stats
Core:AgentTARS   • give_feedback_to_desktop_commander
Core:AgentTARS   • interact_with_process
Core:AgentTARS   • kill_process
Core:AgentTARS   • list_processes
Core:AgentTARS   • list_sessions
Core:AgentTARS   • read_process_output
Core:AgentTARS   • search_code
Core:AgentTARS   • set_config_value
Core:AgentTARS   • start_process
Core:AgentTARS 
📦 minimax (9):
Core:AgentTARS   • generate_video
Core:AgentTARS   • list_voices
Core:AgentTARS   • music_generation
Core:AgentTARS   • play_audio
Core:AgentTARS   • query_video_generation
Core:AgentTARS   • text_to_audio
Core:AgentTARS   • text_to_image
Core:AgentTARS   • voice_clone
Core:AgentTARS   • voice_design
Core:AgentTARS 
📦 davinci-mcp (13):
Core:AgentTARS   • create_project
Core:AgentTARS   • create_timeline
Core:AgentTARS   • get_current_page
Core:AgentTARS   • get_current_project
Core:AgentTARS   • get_current_timeline
Core:AgentTARS   • get_version
Core:AgentTARS   • import_media
Core:AgentTARS   • list_media_clips
Core:AgentTARS   • list_projects
Core:AgentTARS   • list_timelines
Core:AgentTARS   • open_project
Core:AgentTARS   • switch_page
Core:AgentTARS   • switch_timeline
Core:AgentTARS 
═════════════════════════
Core:AgentTARS ✨ Total: 70 tools ready to use
Core:AgentTARS ═════════════════════════

Core:AgentTARS 🔌 Connecting to MCP server: desktop-commander
Core:AgentTARS Initializing MCP client v2 for desktop-commander
Loading server.ts
Setting up request handlers...
[desktop-commander] Enhanced FilteredStdioServerTransport initialized
Core:AgentTARS MCP client v2 initialized successfully for desktop-commander, found 21 tools
Core [Tool] Registered: get_config | Description: "[desktop-commander] 
                        Get the complete server configuration as JSON. Config includes fields for:
                        - blockedCommands (array of blocked shell commands)
                        - defaultShell (shell to use for commands)
                        - allowedDirectories (paths the server can access)
                        - fileReadLineLimit (max lines for read_file, default 1000)
                        - fileWriteLineLimit (max lines per write_file call, default 50)
                        - telemetryEnabled (boolean for telemetry opt-in/out)
                        - currentClient (information about the currently connected MCP client)
                        - clientHistory (history of all clients that have connected)
                        - version (version of the DesktopCommander)
                        - systemInfo (operating system and environment details)
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: set_config_value | Description: "[desktop-commander] 
                        Set a specific configuration value by key.
                        
                        WARNING: Should be used in a separate chat from file operations and 
                        command execution to prevent security issues.
                        
                        Config keys include:
                        - blockedCommands (array)
                        - defaultShell (string)
                        - allowedDirectories (array of paths)
                        - fileReadLineLimit (number, max lines for read_file)
                        - fileWriteLineLimit (number, max lines per write_file call)
                        - telemetryEnabled (boolean)
                        
                        IMPORTANT: Setting allowedDirectories to an empty array ([]) allows full access 
                        to the entire file system, regardless of the operating system.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: read_file | Description: "[desktop-commander] 
                        Read the contents of a file from the file system or a URL with optional offset and length parameters.
                        
                        Prefer this over 'execute_command' with cat/type for viewing files.
                        
                        Supports partial file reading with:
                        - 'offset' (start line, default: 0)
                          * Positive: Start from line N (0-based indexing)
                          * Negative: Read last N lines from end (tail behavior)
                        - 'length' (max lines to read, default: configurable via 'fileReadLineLimit' setting, initially 1000)
                          * Used with positive offsets for range reading
                          * Ignored when offset is negative (reads all requested tail lines)
                        
                        Examples:
                        - offset: 0, length: 10     → First 10 lines
                        - offset: 100, length: 5    → Lines 100-104
                        - offset: -20               → Last 20 lines  
                        - offset: -5, length: 10    → Last 5 lines (length ignored)
                        
                        Performance optimizations:
                        - Large files with negative offsets use reverse reading for efficiency
                        - Large files with deep positive offsets use byte estimation
                        - Small files use fast readline streaming
                        
                        When reading from the file system, only works within allowed directories.
                        Can fetch content from URLs when isUrl parameter is set to true
                        (URLs are always read in full regardless of offset/length).
                        
                        Handles text files normally and image files are returned as viewable images.
                        Recognized image types: PNG, JPEG, GIF, WebP.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: read_multiple_files | Description: "[desktop-commander] 
                        Read the contents of multiple files simultaneously.
                        
                        Each file's content is returned with its path as a reference.
                        Handles text files normally and renders images as viewable content.
                        Recognized image types: PNG, JPEG, GIF, WebP.
                        
                        Failed reads for individual files won't stop the entire operation.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: write_file | Description: "[desktop-commander] 
                        Write or append to file contents. 

                        CHUNKING IS STANDARD PRACTICE: Always write files in chunks of 25-30 lines maximum.
                        This is the normal, recommended way to write files - not an emergency measure.

                        STANDARD PROCESS FOR ANY FILE:
                        1. FIRST → write_file(filePath, firstChunk, {mode: 'rewrite'})  [≤30 lines]
                        2. THEN → write_file(filePath, secondChunk, {mode: 'append'})   [≤30 lines]
                        3. CONTINUE → write_file(filePath, nextChunk, {mode: 'append'}) [≤30 lines]

                        ALWAYS CHUNK PROACTIVELY - don't wait for performance warnings!

                        WHEN TO CHUNK (always be proactive):
                        1. Any file expected to be longer than 25-30 lines
                        2. When writing multiple files in sequence
                        3. When creating documentation, code files, or configuration files
                        
                        HANDLING CONTINUATION ("Continue" prompts):
                        If user asks to "Continue" after an incomplete operation:
                        1. Read the file to see what was successfully written
                        2. Continue writing ONLY the remaining content using {mode: 'append'}
                        3. Keep chunks to 25-30 lines each
                        
                        Files over 50 lines will generate performance notes but are still written successfully.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: create_directory | Description: "[desktop-commander] 
                        Create a new directory or ensure a directory exists.
                        
                        Can create multiple nested directories in one operation.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: list_directory | Description: "[desktop-commander] 
                        Get a detailed listing of all files and directories in a specified path.
                        
                        Use this instead of 'execute_command' with ls/dir commands.
                        Results distinguish between files and directories with [FILE] and [DIR] prefixes.
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: move_file | Description: "[desktop-commander] 
                        Move or rename files and directories.
                        
                        Can move files between directories and rename them in a single operation.
                        Both source and destination must be within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: search_files | Description: "[desktop-commander] 
                        Finds files by name using a case-insensitive substring matching.
                        
                        Use this instead of 'execute_command' with find/dir/ls for locating files.
                        Searches through all subdirectories from the starting path.
                        
                        Has a default timeout of 30 seconds which can be customized using the timeoutMs parameter.
                        Only searches within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: search_code | Description: "[desktop-commander] 
                        Search for text/code patterns within file contents using ripgrep.
                        
                        Use this instead of 'execute_command' with grep/find for searching code content.
                        Fast and powerful search similar to VS Code search functionality.
                        
                        Supports regular expressions, file pattern filtering, and context lines.
                        Has a default timeout of 30 seconds which can be customized.
                        Only searches within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: get_file_info | Description: "[desktop-commander] 
                        Retrieve detailed metadata about a file or directory including:
                        - size
                        - creation time
                        - last modified time 
                        - permissions
                        - type
                        - lineCount (for text files)
                        - lastLine (zero-indexed number of last line, for text files)
                        - appendPosition (line number for appending, for text files)
                        
                        Only works within allowed directories.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: edit_block | Description: "[desktop-commander] 
                        Apply surgical text replacements to files.
                        
                        BEST PRACTICE: Make multiple small, focused edits rather than one large edit.
                        Each edit_block call should change only what needs to be changed - include just enough 
                        context to uniquely identify the text being modified.
                        
                        Takes:
                        - file_path: Path to the file to edit
                        - old_string: Text to replace
                        - new_string: Replacement text
                        - expected_replacements: Optional parameter for number of replacements
                        
                        By default, replaces only ONE occurrence of the search text.
                        To replace multiple occurrences, provide the expected_replacements parameter with
                        the exact number of matches expected.
                        
                        UNIQUENESS REQUIREMENT: When expected_replacements=1 (default), include the minimal
                        amount of context necessary (typically 1-3 lines) before and after the change point,
                        with exact whitespace and indentation.
                        
                        When editing multiple sections, make separate edit_block calls for each distinct change
                        rather than one large replacement.
                        
                        When a close but non-exact match is found, a character-level diff is shown in the format:
                        common_prefix{-removed-}{+added+}common_suffix to help you identify what's different.
                        
                        Similar to write_file, there is a configurable line limit (fileWriteLineLimit) that warns
                        if the edited file exceeds this limit. If this happens, consider breaking your edits into
                        smaller, more focused changes.
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: start_process | Description: "[desktop-commander] 
                        Start a new terminal process with intelligent state detection.
                        
                        PRIMARY TOOL FOR FILE ANALYSIS AND DATA PROCESSING
                        This is the ONLY correct tool for analyzing local files (CSV, JSON, logs, etc.).
                        The analysis tool CANNOT access local files and WILL FAIL - always use processes for file-based work.
                        
                        CRITICAL RULE: For ANY local file work, ALWAYS use this tool + interact_with_process, NEVER use analysis/REPL tool.
                        
                        Running on macOS. Default shell: zsh.
        
MACOS-SPECIFIC NOTES:
- Package manager: brew (Homebrew) is commonly used
- Python 3 might be 'python3' command, not 'python'
- Some GNU tools have different names (e.g., gsed instead of sed)
- System Integrity Protection (SIP) may block certain operations
- Use 'open' command to open files/applications from terminal
                        
                        REQUIRED WORKFLOW FOR LOCAL FILES:
                        1. start_process("python3 -i") - Start Python REPL for data analysis
                        2. interact_with_process(pid, "import pandas as pd, numpy as np")
                        3. interact_with_process(pid, "df = pd.read_csv('/absolute/path/file.csv')")
                        4. interact_with_process(pid, "print(df.describe())")
                        5. Continue analysis with pandas, matplotlib, seaborn, etc.
                        
                        COMMON FILE ANALYSIS PATTERNS:
                        • start_process("python3 -i") → Python REPL for data analysis (RECOMMENDED)
                        • start_process("node -i") → Node.js for JSON processing  
                        • start_process("cut -d',' -f1 file.csv | sort | uniq -c") → Quick CSV analysis
                        • start_process("wc -l /path/file.csv") → Line counting
                        • start_process("head -10 /path/file.csv") → File preview
                        
                        BINARY FILE SUPPORT:
                        For PDF, Excel, Word, archives, databases, and other binary formats, use process tools with appropriate libraries or command-line utilities.
                        
                        INTERACTIVE PROCESSES FOR DATA ANALYSIS:
                        1. start_process("python3 -i") - Start Python REPL for data work
                        2. start_process("node -i") - Start Node.js REPL for JSON/JS
                        3. start_process("bash") - Start interactive bash shell
                        4. Use interact_with_process() to send commands
                        5. Use read_process_output() to get responses
                        
                        SMART DETECTION:
                        - Detects REPL prompts (>>>, >, $, etc.)
                        - Identifies when process is waiting for input
                        - Recognizes process completion vs timeout
                        - Early exit prevents unnecessary waiting
                        
                        STATES DETECTED:
                        Process waiting for input (shows prompt)
                        Process finished execution  
                        Process running (use read_process_output)
                        
                        ALWAYS USE FOR: Local file analysis, CSV processing, data exploration, system commands
                        NEVER USE ANALYSIS TOOL FOR: Local file access (analysis tool is browser-only and WILL FAIL)
                        
                        IMPORTANT: Always use absolute paths for reliability. Paths are automatically normalized regardless of slash direction. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: read_process_output | Description: "[desktop-commander] 
                        Read output from a running process with intelligent completion detection.
                        
                        Automatically detects when process is ready for more input instead of timing out.
                        
                        SMART FEATURES:
                        - Early exit when REPL shows prompt (>>>, >, etc.)
                        - Detects process completion vs still running
                        - Prevents hanging on interactive prompts
                        - Clear status messages about process state
                        
                        REPL USAGE:
                        - Stops immediately when REPL prompt detected
                        - Shows clear status: waiting for input vs finished
                        - Shorter timeouts needed due to smart detection
                        - Works with Python, Node.js, R, Julia, etc.
                        
                        DETECTION STATES:
                        Process waiting for input (ready for interact_with_process)
                        Process finished execution
                        Timeout reached (may still be running)
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: interact_with_process | Description: "[desktop-commander] 
                        Send input to a running process and automatically receive the response.
                        
                        CRITICAL: THIS IS THE PRIMARY TOOL FOR ALL LOCAL FILE ANALYSIS
                        For ANY local file analysis (CSV, JSON, data processing), ALWAYS use this instead of the analysis tool.
                        The analysis tool CANNOT access local files and WILL FAIL - use processes for ALL file-based work.
                        
                        FILE ANALYSIS PRIORITY ORDER (MANDATORY):
                        1. ALWAYS FIRST: Use this tool (start_process + interact_with_process) for local data analysis
                        2. ALTERNATIVE: Use command-line tools (cut, awk, grep) for quick processing  
                        3. NEVER EVER: Use analysis tool for local file access (IT WILL FAIL)
                        
                        REQUIRED INTERACTIVE WORKFLOW FOR FILE ANALYSIS:
                        1. Start REPL: start_process("python3 -i")
                        2. Load libraries: interact_with_process(pid, "import pandas as pd, numpy as np")
                        3. Read file: interact_with_process(pid, "df = pd.read_csv('/absolute/path/file.csv')")
                        4. Analyze: interact_with_process(pid, "print(df.describe())")
                        5. Continue: interact_with_process(pid, "df.groupby('column').size()")
                        
                        BINARY FILE PROCESSING WORKFLOWS:
                        Use appropriate Python libraries (PyPDF2, pandas, docx2txt, etc.) or command-line tools for binary file analysis.
                        
                        SMART DETECTION:
                        - Automatically waits for REPL prompt (>>>, >, etc.)
                        - Detects errors and completion states
                        - Early exit prevents timeout delays
                        - Clean output formatting (removes prompts)
                        
                        SUPPORTED REPLs:
                        - Python: python3 -i (RECOMMENDED for data analysis)
                        - Node.js: node -i  
                        - R: R
                        - Julia: julia
                        - Shell: bash, zsh
                        - Database: mysql, postgres
                        
                        PARAMETERS:
                        - pid: Process ID from start_process
                        - input: Code/command to execute
                        - timeout_ms: Max wait (default: 8000ms)
                        - wait_for_prompt: Auto-wait for response (default: true)
                        
                        Returns execution result with status indicators.
                        
                        ALWAYS USE FOR: CSV analysis, JSON processing, file statistics, data visualization prep, ANY local file work
                        NEVER USE ANALYSIS TOOL FOR: Local file access (it cannot read files from disk and WILL FAIL)
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: force_terminate | Description: "[desktop-commander] 
                        Force terminate a running terminal session.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: list_sessions | Description: "[desktop-commander] 
                        List all active terminal sessions.
                        
                        Shows session status including:
                        - PID: Process identifier  
                        - Blocked: Whether session is waiting for input
                        - Runtime: How long the session has been running
                        
                        DEBUGGING REPLs:
                        - "Blocked: true" often means REPL is waiting for input
                        - Use this to verify sessions are running before sending input
                        - Long runtime with blocked status may indicate stuck process
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: list_processes | Description: "[desktop-commander] 
                        List all running processes.
                        
                        Returns process information including PID, command name, CPU usage, and memory usage.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: kill_process | Description: "[desktop-commander] 
                        Terminate a running process by PID.
                        
                        Use with caution as this will forcefully terminate the specified process.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: get_usage_stats | Description: "[desktop-commander] 
                        Get usage statistics for debugging and analysis.
                        
                        Returns summary of tool usage, success/failure rates, and performance metrics.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core [Tool] Registered: give_feedback_to_desktop_commander | Description: "[desktop-commander] 
                        Open feedback form in browser to provide feedback about Desktop Commander.
                        
                        IMPORTANT: This tool simply opens the feedback form - no pre-filling available.
                        The user will fill out the form manually in their browser.
                        
                        WORKFLOW:
                        1. When user agrees to give feedback, just call this tool immediately
                        2. No need to ask questions or collect information
                        3. Tool opens form with only usage statistics pre-filled automatically:
                           - tool_call_count: Number of commands they've made
                           - days_using: How many days they've used Desktop Commander
                           - platform: Their operating system (Mac/Windows/Linux)
                           - client_id: Analytics identifier
                        
                        All survey questions will be answered directly in the form:
                        - Job title and technical comfort level
                        - Company URL for industry context
                        - Other AI tools they use
                        - Desktop Commander's biggest advantage
                        - How they typically use it
                        - Recommendation likelihood (0-10)
                        - User study participation interest
                        - Email and any additional feedback
                        
                        EXAMPLE INTERACTION:
                        User: "sure, I'll give feedback"
                        Claude: "Perfect! Let me open the feedback form for you."
                        [calls tool immediately]
                        
                        No parameters are needed - just call the tool to open the form.
                        
                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions."
Core:AgentTARS ✅ Connected to MCP server desktop-commander with 21 tools
Core:AgentTARS 🔌 Connecting to MCP server: minimax
Core:AgentTARS Initializing MCP client v2 for minimax
Core:AgentTARS MCP client v2 initialized successfully for minimax, found 9 tools
Core [Tool] Registered: text_to_audio | Description: "[minimax] Convert text to audio with a given voice and save the output audio file to a given directory.
    Directory is optional, if not provided, the output file will be saved to $HOME/Desktop.
    Voice id is optional, if not provided, the default voice will be used.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

    Args:
        text (str): The text to convert to speech.
        voice_id (str, optional): The id of the voice to use. For example, "male-qn-qingse"/"audiobook_female_1"/"cute_boy"/"Charming_Lady"...
        model (string, optional): The model to use.
        speed (float, optional): Speed of the generated audio. Controls the speed of the generated speech. Values range from 0.5 to 2.0, with 1.0 being the default speed. 
        vol (float, optional): Volume of the generated audio. Controls the volume of the generated speech. Values range from 0 to 10, with 1 being the default volume.
        pitch (int, optional): Pitch of the generated audio. Controls the speed of the generated speech. Values range from -12 to 12, with 0 being the default speed.
        emotion (str, optional): Emotion of the generated audio. Controls the emotion of the generated speech. Values range ["happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"], with "happy" being the default emotion.
        sample_rate (int, optional): Sample rate of the generated audio. Controls the sample rate of the generated speech. Values range [8000,16000,22050,24000,32000,44100] with 32000 being the default sample rate.
        bitrate (int, optional): Bitrate of the generated audio. Controls the bitrate of the generated speech. Values range [32000,64000,128000,256000] with 128000 being the default bitrate.
        channel (int, optional): Channel of the generated audio. Controls the channel of the generated speech. Values range [1, 2] with 1 being the default channel.
        format (str, optional): Format of the generated audio. Controls the format of the generated speech. Values range ["pcm", "mp3","flac"] with "mp3" being the default format.
        language_boost (str, optional): Language boost of the generated audio. Controls the language boost of the generated speech. Values range ['Chinese', 'Chinese,Yue', 'English', 'Arabic', 'Russian', 'Spanish', 'French', 'Portuguese', 'German', 'Turkish', 'Dutch', 'Ukrainian', 'Vietnamese', 'Indonesian', 'Japanese', 'Italian', 'Korean', 'Thai', 'Polish', 'Romanian', 'Greek', 'Czech', 'Finnish', 'Hindi', 'auto'] with "auto" being the default language boost.
        output_directory (str): The directory to save the audio to.

    Returns:
        Text content with the path to the output file and name of the voice used.
    "
Core [Tool] Registered: list_voices | Description: "[minimax] List all voices available.

    Args:
        voice_type (str, optional): The type of voices to list. Values range ["all", "system", "voice_cloning"], with "all" being the default.
    Returns:
        Text content with the list of voices.
    "
Core [Tool] Registered: voice_clone | Description: "[minimax] Clone a voice using provided audio files. The new voice will be charged upon first use.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        voice_id (str): The id of the voice to use.
        file (str): The path to the audio file to clone or a URL to the audio file.
        text (str, optional): The text to use for the demo audio.
        is_url (bool, optional): Whether the file is a URL. Defaults to False.
        output_directory (str): The directory to save the demo audio to.
    Returns:
        Text content with the voice id of the cloned voice.
    "
Core [Tool] Registered: play_audio | Description: "[minimax] Play an audio file. Supports WAV and MP3 formats. Not supports video.

     Args:
        input_file_path (str): The path to the audio file to play.
        is_url (bool, optional): Whether the audio file is a URL.
    Returns:
        Text content with the path to the audio file.
    "
Core [Tool] Registered: generate_video | Description: "[minimax] Generate a video from a prompt.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        model (str, optional): The model to use. Values range ["T2V-01", "T2V-01-Director", "I2V-01", "I2V-01-Director", "I2V-01-live", "MiniMax-Hailuo-02"]. "Director" supports inserting instructions for camera movement control. "I2V" for image to video. "T2V" for text to video. "MiniMax-Hailuo-02" is the latest model with best effect, ultra-clear quality and precise response.
        prompt (str): The prompt to generate the video from. When use Director model, the prompt supports 15 Camera Movement Instructions (Enumerated Values)
            -Truck: [Truck left], [Truck right]
            -Pan: [Pan left], [Pan right]
            -Push: [Push in], [Pull out]
            -Pedestal: [Pedestal up], [Pedestal down]
            -Tilt: [Tilt up], [Tilt down]
            -Zoom: [Zoom in], [Zoom out]
            -Shake: [Shake]
            -Follow: [Tracking shot]
            -Static: [Static shot]
        first_frame_image (str): The first frame image. The model must be "I2V" Series.
        duration (int, optional): The duration of the video. The model must be "MiniMax-Hailuo-02". Values can be 6 and 10.
        resolution (str, optional): The resolution of the video. The model must be "MiniMax-Hailuo-02". Values range ["768P", "1080P"]
        output_directory (str): The directory to save the video to.
        async_mode (bool, optional): Whether to use async mode. Defaults to False. If True, the video generation task will be submitted asynchronously and the response will return a task_id. Should use `query_video_generation` tool to check the status of the task and get the result.
    Returns:
        Text content with the path to the output video file.
    "
Core [Tool] Registered: query_video_generation | Description: "[minimax] Query the status of a video generation task.

    Args:
        task_id (str): The task ID to query. Should be the task_id returned by `generate_video` tool if `async_mode` is True.
        output_directory (str): The directory to save the video to.
    Returns:
        Text content with the status of the task.
    "
Core [Tool] Registered: text_to_image | Description: "[minimax] Generate a image from a prompt.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        model (str, optional): The model to use. Values range ["image-01"], with "image-01" being the default.
        prompt (str): The prompt to generate the image from.
        aspect_ratio (str, optional): The aspect ratio of the image. Values range ["1:1", "16:9","4:3", "3:2", "2:3", "3:4", "9:16", "21:9"], with "1:1" being the default.
        n (int, optional): The number of images to generate. Values range [1, 9], with 1 being the default.
        prompt_optimizer (bool, optional): Whether to optimize the prompt. Values range [True, False], with True being the default.
        output_directory (str): The directory to save the image to.
    Returns:
        Text content with the path to the output image file.
    "
Core [Tool] Registered: music_generation | Description: "[minimax] Create a music generation task using AI models. Generate music from prompt and lyrics.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

    Args:
        prompt (str): Music creation inspiration describing style, mood, scene, etc.
            Example: "Pop music, sad, suitable for rainy nights". Character range: [10, 300]
        lyrics (str): Song lyrics for music generation.
            Use newline (\n) to separate each line of lyrics. Supports lyric structure tags [Intro][Verse][Chorus][Bridge][Outro] 
            to enhance musicality. Character range: [10, 600] (each Chinese character, punctuation, and letter counts as 1 character)
        stream (bool, optional): Whether to enable streaming mode. Defaults to False
        sample_rate (int, optional): Sample rate of generated music. Values: [16000, 24000, 32000, 44100]
        bitrate (int, optional): Bitrate of generated music. Values: [32000, 64000, 128000, 256000]
        format (str, optional): Format of generated music. Values: ["mp3", "wav", "pcm"]. Defaults to "mp3"
        output_directory (str, optional): Directory to save the generated music file
        
    Note: Currently supports generating music up to 1 minute in length.

    Returns:
        Text content with the path to the generated music file or generation status.
    "
Core [Tool] Registered: voice_design | Description: "[minimax] Generate a voice based on description prompts.

    COST WARNING: This tool makes an API call to Minimax which may incur costs. Only use when explicitly requested by the user.

     Args:
        prompt (str): The prompt to generate the voice from.
        preview_text (str): The text to preview the voice.
        voice_id (str, optional): The id of the voice to use. For example, "male-qn-qingse"/"audiobook_female_1"/"cute_boy"/"Charming_Lady"...
        output_directory (str, optional): The directory to save the voice to.
    Returns:
        Text content with the path to the output voice file.
    "
Core:AgentTARS ✅ Connected to MCP server minimax with 9 tools
Core:AgentTARS 🔌 Connecting to MCP server: davinci-mcp
Core:AgentTARS Initializing MCP client v2 for davinci-mcp
Core:AgentTARS MCP client v2 initialized successfully for davinci-mcp, found 13 tools
Core [Tool] Registered: get_version | Description: "[davinci-mcp] Get DaVinci Resolve version information"
Core [Tool] Registered: get_current_page | Description: "[davinci-mcp] Get the current page open in DaVinci Resolve (Edit, Color, Fusion, etc.)"
Core [Tool] Registered: switch_page | Description: "[davinci-mcp] Switch to a specific page in DaVinci Resolve"
Core [Tool] Registered: list_projects | Description: "[davinci-mcp] List all available projects in the current database"
Core [Tool] Registered: get_current_project | Description: "[davinci-mcp] Get the name of the currently open project"
Core [Tool] Registered: open_project | Description: "[davinci-mcp] Open a project by name"
Core [Tool] Registered: create_project | Description: "[davinci-mcp] Create a new project with the given name"
Core [Tool] Registered: list_timelines | Description: "[davinci-mcp] List all timelines in the current project"
Core [Tool] Registered: get_current_timeline | Description: "[davinci-mcp] Get the name of the current timeline"
Core [Tool] Registered: create_timeline | Description: "[davinci-mcp] Create a new timeline with the given name"
Core [Tool] Registered: switch_timeline | Description: "[davinci-mcp] Switch to a timeline by name"
Core [Tool] Registered: list_media_clips | Description: "[davinci-mcp] List all clips in the media pool"
Core [Tool] Registered: import_media | Description: "[davinci-mcp] Import a media file into the media pool"
Core:AgentTARS ✅ Connected to MCP server davinci-mcp with 13 tools
EventStream Event added: user_message (ba8d8d8c-c486-4545-bd55-4df4979af7ad)
AgentRunner [Session] Execution started | SessionId: "1753894653360-w2vev5r" | Provider: "volcengine" | Model: "doubao-1-5-thinking-vision-pro-250428" | Mode: streaming
AgentRunner Using tool call engine: native
EventStream Subscribed to events (total subscribers: 4)
LoopExecutor [Iteration] 1/100 started
[AgentSnapshot] Starting agent loop 1
EventStream Event added: plan_start (75270299-bb88-4cb1-89ed-0ebb72c5147c)
EventStream Event added: agent_run_start (0716260a-a75e-4638-b539-eab40f80cdcc)
EventStream Event added: plan_update (8c54bc30-5154-4649-a3d6-e036b19b661d)
Core:AgentTARS:PlanManager Initial plan created with 3 steps
LLMProcessor [Agent] Pre-iteration hook executed for iteration 1
LLMProcessor [Tools] Available: 70 | Names: web_search, browser_vision_control, browser_navigate, browser_go_back, browser_go_forward, browser_refresh, browser_get_markdown, browser_screenshot, browser_click, browser_form_input_fill, browser_select, browser_hover, browser_evaluate, browser_get_clickable_elements, browser_read_links, browser_scroll, browser_tab_list, browser_new_tab, browser_close_tab, browser_switch_tab, browser_press_key, read_file, read_multiple_files, write_file, edit_file, create_directory, list_directory, directory_tree, move_file, search_files, get_file_info, list_allowed_directories, run_command, run_script, final_answer, get_config, set_config_value, search_code, edit_block, start_process, read_process_output, interact_with_process, force_terminate, list_sessions, list_processes, kill_process, get_usage_stats, give_feedback_to_desktop_commander, text_to_audio, list_voices, voice_clone, play_audio, generate_video, query_video_generation, text_to_image, music_generation, voice_design, get_version, get_current_page, switch_page, list_projects, get_current_project, open_project, create_project, list_timelines, get_current_timeline, create_timeline, switch_timeline, list_media_clips, import_media
MessageHistory Created system message with prompt 8885 chars long
MessageHistory Message history built with 0 images (limit: 5, 0 images replaced with placeholders)
LLMProcessor [LLM] Requesting volcengine/doubao-1-5-thinking-vision-pro-250428
NativeEngine Preparing request for model: doubao-1-5-thinking-vision-pro-250428 with 70 tools
LLMProcessor [LLM] Sending streaming request to volcengine | SessionId: 1753894653360-w2vev5r
AgentRunner [Stream] Error in agent loop execution: Error: 400 Invalid function format: 'type' Request id: 02175389465595846ce2e88b6e4db78971e7df7e5714172aa8640
EventStream Event added: system (21b95e0c-7877-42ec-a549-ddeb15c0a63a)
[AgentSnapshot] Snapshot generation completed: /Users/<USER>/TARS-Agent/workspace/7PJ4zS_inmAsx9X_wZzxg
EventStream Unsubscribed from events (remaining subscribers: 3)
EventStream Event added: agent_run_end (d8e1348a-f9d8-4abd-8c5c-64c855917f4d)
ExecutionController Agent execution ended with status: idle
