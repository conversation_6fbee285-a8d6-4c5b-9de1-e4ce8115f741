{"mcpServers": {"davinci-resolve": {"name": "DaVinci Resolve MCP", "command": "${PROJECT_ROOT}/venv/bin/python", "args": ["${PROJECT_ROOT}/resolve_mcp_server.py"], "env": {"RESOLVE_SCRIPT_API": "/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting", "RESOLVE_SCRIPT_LIB": "/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so", "PYTHONPATH": "$PYTHONPATH:/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting/Modules/"}}}}