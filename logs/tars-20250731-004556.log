
+------------------------------------------------+
|                                                |
|    █████  ██████  ███████ ███    ██ ████████   |
|   ██   ██ ██      ██      ████   ██    ██      |
|   ███████ ██   ██ █████   ██ ██  ██    ██      |
|   ██   ██ ██   ██ ██      ██  ██ ██    ██      |
|   ██   ██ ███████ ███████ ██   ████    ██      |
|                                                |
|   ████████  █████  ██████   ███████            |
|      ██    ██   ██ ██   ██  ██                 |
|      ██    ███████ ██████   ███████            |
|      ██    ██   ██ ██   ██       ██            |
|      ██    ██   ██ ██   ██  ███████            |
|                                                |
|                                                |
|   An open-source Multimodal AI Agent v0.2.10   |
|                                                |
|   https://agent-tars.com                       |
|                                                |
+------------------------------------------------+

Failed to load JSON file: /Users/<USER>/TARS-Agent/agent-tars.config.json
node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::8888
    at Server.setupListenHandle [as _listen2] (node:net:1940:16)
    at listenInCluster (node:net:1997:12)
    at Server.listen (node:net:2102:7)
    at /Users/<USER>/.nvm/versions/node/v22.17.1/lib/node_modules/@agent-tars/cli/node_modules/@agent-tars/server/dist/index.js:21694:33
    at new Promise (<anonymous>)
    at AgentTARSServer.start (/Users/<USER>/.nvm/versions/node/v22.17.1/lib/node_modules/@agent-tars/cli/node_modules/@agent-tars/server/dist/index.js:21693:24)
    at async startInteractiveWebUI (/Users/<USER>/.nvm/versions/node/v22.17.1/lib/node_modules/@agent-tars/cli/dist/index.js:22596:24)
    at async CAC.<anonymous> (/Users/<USER>/.nvm/versions/node/v22.17.1/lib/node_modules/@agent-tars/cli/dist/index.js:22665:17)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1976:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::',
  port: 8888
}

Node.js v22.17.1
