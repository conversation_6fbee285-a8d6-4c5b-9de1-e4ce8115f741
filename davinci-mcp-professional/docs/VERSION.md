# DaVinci Resolve MCP Server

Current Version: 1.3.8

## Release Information

### 1.3.8 Changes
- **Cursor Integration**: Added comprehensive documentation for Cursor setup process
- **Entry Point**: Standardized on `main.py` as the proper entry point (replaces direct use of `resolve_mcp_server.py`)
- **Configuration Templates**: Updated example configuration files to use correct paths
- **Fixed**: Ensured consistent documentation for environment setup

### 1.3.7 Changes
- Improved installation experience:
  - New one-step installation script for macOS/Linux and Windows
  - Enhanced path resolution in scripts
  - More reliable DaVinci Resolve detection
  - Support for absolute paths in project and global configurations
  - Added comprehensive verification tools for troubleshooting
  - Improved error handling and feedback
  - Enhanced documentation with detailed installation guide
- Fixed configuration issues with project-level MCP configuration
- Updated documentation with detailed installation and troubleshooting steps

### 1.3.6 Changes
- Comprehensive Feature Additions:
  - Complete MediaPoolItem and Folder object functionality
  - Cache Management implementation
  - Timeline Item Properties implementation
  - Keyframe Control implementation
  - Color Preset Management implementation
  - LUT Export functionality
- Project directory restructuring
- Updated Implementation Progress Summary to reflect 100% completion of multiple feature sets
- Enhanced documentation and examples

### 1.3.5 Changes
- Updated Cursor integration with new templating system
- Added automatic Cursor MCP configuration generation
- Improved client-specific launcher scripts
- Fixed path handling in Cursor configuration
- Enhanced cross-platform compatibility
- Improved virtual environment detection and validation

### 1.3.4 Changes
- Improved template configuration for MCP clients
- Added clearer documentation for path configuration
- Fixed Cursor integration templates with correct Python path
- Simplified configuration process with better examples
- Enhanced README with prominent warnings about path replacement
- Removed environment variable requirements from configuration files

## About
DaVinci Resolve MCP Server connects DaVinci Resolve to AI assistants through the Model Context Protocol, allowing AI agents to control DaVinci Resolve directly through natural language.

For full changelog, see CHANGELOG.md 