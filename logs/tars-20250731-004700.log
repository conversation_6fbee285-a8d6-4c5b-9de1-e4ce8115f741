
+------------------------------------------------+
|                                                |
|    █████  ██████  ███████ ███    ██ ████████   |
|   ██   ██ ██      ██      ████   ██    ██      |
|   ███████ ██   ██ █████   ██ ██  ██    ██      |
|   ██   ██ ██   ██ ██      ██  ██ ██    ██      |
|   ██   ██ ███████ ███████ ██   ████    ██      |
|                                                |
|   ████████  █████  ██████   ███████            |
|      ██    ██   ██ ██   ██  ██                 |
|      ██    ███████ ██████   ███████            |
|      ██    ██   ██ ██   ██       ██            |
|      ██    ██   ██ ██   ██  ███████            |
|                                                |
|                                                |
|   An open-source Multimodal AI Agent v0.2.10   |
|                                                |
|   https://agent-tars.com                       |
|                                                |
+------------------------------------------------+

Failed to load JSON file: /Users/<USER>/TARS-Agent/agent-tars.config.json

+----------------------------------------------------------+
|                                                          |
|   🎉 Agent TARS is available at: http://localhost:8888   |
|                                                          |
|   📁 Workspace: Not specified                            |
|                                                          |
|   🤖 Model: Not specified                                |
|                                                          |
+----------------------------------------------------------+

AgentTARS Vision-based browser control (hybrid) is not supported with Unknown
AgentTARS Currently, vision-based browser control ("hyrid" / "visual-grounding") is only supported with Doubao 1.5 VL. Switching to "dom" mode.
AgentRunner [Stream] Error in agent loop execution: Error: The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).
