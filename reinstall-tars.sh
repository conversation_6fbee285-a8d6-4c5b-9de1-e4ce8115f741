#!/bin/bash

echo "🔄 TARS Agent 重新安装脚本"
echo "================================"

# 1. 备份当前配置
echo "📦 备份当前配置..."
cp agent-tars.config.json agent-tars.config.json.backup
echo "✅ 配置已备份到 agent-tars.config.json.backup"

# 2. 卸载当前版本
echo "🗑️  卸载当前版本..."
npm uninstall -g agent-tars

# 3. 清理 npm 缓存
echo "🧹 清理 npm 缓存..."
npm cache clean --force

# 4. 重新安装最新版本
echo "📥 重新安装最新版本..."
npm install -g agent-tars@latest

# 5. 验证安装
echo "✅ 验证安装..."
agent-tars --version

# 6. 恢复配置
echo "🔧 恢复配置..."
if [ -f "agent-tars.config.json.backup" ]; then
    cp agent-tars.config.json.backup agent-tars.config.json
    echo "✅ 配置已恢复"
fi

echo ""
echo "🎉 重新安装完成！"
echo "💡 建议使用 ./start-tars-enhanced.sh 启动以获得更好的错误处理"
