#!/bin/bash
# Video_Editing_Complete_Backup.sh
# 视频编辑软件完整备份脚本 - 一键操作
# 支持：DaVinci Resolve, Premiere Pro, Final Cut Pro, 系统设置

set -e  # 遇到错误立即退出

# ==================== 配置区域 ====================
BACKUP_ROOT="/Users/<USER>/Desktop/Video_Editing_Backups"
DATE_STAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_ROOT/Complete_Backup_$DATE_STAMP"
LOG_FILE="$BACKUP_DIR/backup.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ==================== 函数定义 ====================
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

success() {
    log "${GREEN}✅ $1${NC}"
}

warning() {
    log "${YELLOW}⚠️ $1${NC}"
}

error() {
    log "${RED}❌ $1${NC}"
}

info() {
    log "${BLUE}ℹ️ $1${NC}"
}

# 检查磁盘空间
check_disk_space() {
    local required_gb=20
    local available_gb=$(df -g "$HOME/Desktop" | tail -1 | awk '{print $4}')
    
    if [ "$available_gb" -lt "$required_gb" ]; then
        error "磁盘空间不足！需要至少${required_gb}GB，当前可用：${available_gb}GB"
        exit 1
    fi
    success "磁盘空间检查通过：${available_gb}GB可用"
}

# 创建备份目录结构
create_backup_structure() {
    mkdir -p "$BACKUP_DIR"/{DaVinci,Premiere,FinalCut,System,Scripts}
    success "备份目录结构创建完成：$BACKUP_DIR"
}

# 停止相关应用程序
stop_applications() {
    info "检查并停止相关应用程序..."
    
    local apps=("DaVinci Resolve" "Adobe Premiere Pro" "Final Cut Pro")
    local stopped_apps=()
    
    for app in "${apps[@]}"; do
        if pgrep -f "$app" > /dev/null; then
            warning "$app 正在运行，建议手动关闭以确保数据完整性"
            stopped_apps+=("$app")
        fi
    done
    
    if [ ${#stopped_apps[@]} -gt 0 ]; then
        warning "检测到以下应用程序正在运行："
        printf '%s\n' "${stopped_apps[@]}"
        read -p "按Enter继续备份，或Ctrl+C取消..."
    fi
}

# ==================== DaVinci Resolve 备份 ====================
backup_davinci() {
    info "开始备份 DaVinci Resolve..."
    
    local davinci_dir="$BACKUP_DIR/DaVinci"
    
    # 1. 备份项目数据
    info "备份DaVinci项目数据..."
    if [ -d "/Users/<USER>/Documents/2024_剪辑工程" ]; then
        rsync -av --progress "/Users/<USER>/Documents/2024_剪辑工程/" "$davinci_dir/Projects_2024/" 2>&1 | tee -a "$LOG_FILE"
        success "2024项目数据备份完成"
    fi
    
    if [ -d "/Users/<USER>/Documents/2023_剪辑工程 3" ]; then
        rsync -av --progress "/Users/<USER>/Documents/2023_剪辑工程 3/" "$davinci_dir/Projects_2023/" 2>&1 | tee -a "$LOG_FILE"
        success "2023项目数据备份完成"
    fi
    
    # 2. 备份用户配置
    info "备份DaVinci用户配置..."
    if [ -d "/Users/<USER>/Library/Preferences/Blackmagic Design" ]; then
        cp -R "/Users/<USER>/Library/Preferences/Blackmagic Design" "$davinci_dir/User_Preferences/" 2>/dev/null
        success "用户偏好设置备份完成"
    fi
    
    if [ -d "/Users/<USER>/Library/Application Support/Blackmagic Design" ]; then
        cp -R "/Users/<USER>/Library/Application Support/Blackmagic Design" "$davinci_dir/User_AppSupport/" 2>/dev/null
        success "用户应用支持文件备份完成"
    fi
    
    # 3. 备份系统级配置
    info "备份DaVinci系统配置..."
    if [ -d "/Library/Application Support/Blackmagic Design" ]; then
        sudo cp -R "/Library/Application Support/Blackmagic Design" "$davinci_dir/System_AppSupport/" 2>/dev/null || warning "系统级配置备份失败（可能需要管理员权限）"
    fi
    
    # 4. 备份LUT和插件
    info "备份LUT和插件..."
    if [ -d "/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/LUT" ]; then
        cp -R "/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/LUT" "$davinci_dir/LUTs/" 2>/dev/null
        success "LUT文件备份完成"
    fi
    
    # 5. 统计项目数量
    local project_count=$(find "$davinci_dir" -name "*.resolve" 2>/dev/null | wc -l)
    success "DaVinci Resolve备份完成！共备份 $project_count 个项目文件"
}

# ==================== Premiere Pro 备份 ====================
backup_premiere() {
    info "开始备份 Adobe Premiere Pro..."
    
    local premiere_dir="$BACKUP_DIR/Premiere"
    
    # 1. 备份项目文件
    info "备份Premiere项目文件..."
    find "/Users/<USER>/Documents" -name "*.prproj" -exec cp {} "$premiere_dir/Projects/" \; 2>/dev/null || mkdir -p "$premiere_dir/Projects"
    
    # 2. 备份用户配置
    info "备份Premiere用户配置..."
    if [ -d "/Users/<USER>/Library/Preferences/com.adobe.PremierePro" ]; then
        cp -R "/Users/<USER>/Library/Preferences/com.adobe.PremierePro" "$premiere_dir/User_Preferences/" 2>/dev/null
        success "Premiere用户偏好设置备份完成"
    fi
    
    # 3. 备份应用支持文件
    if [ -d "/Users/<USER>/Library/Application Support/Adobe/Premiere Pro" ]; then
        cp -R "/Users/<USER>/Library/Application Support/Adobe/Premiere Pro" "$premiere_dir/User_AppSupport/" 2>/dev/null
        success "Premiere应用支持文件备份完成"
    fi
    
    # 4. 备份预设和模板
    if [ -d "/Users/<USER>/Documents/Adobe/Premiere Pro" ]; then
        cp -R "/Users/<USER>/Documents/Adobe/Premiere Pro" "$premiere_dir/User_Presets/" 2>/dev/null
        success "Premiere预设和模板备份完成"
    fi
    
    # 5. 备份插件设置
    if [ -d "/Users/<USER>/Library/Application Support/Adobe/Common" ]; then
        cp -R "/Users/<USER>/Library/Application Support/Adobe/Common" "$premiere_dir/Common_Settings/" 2>/dev/null
        success "Adobe通用设置备份完成"
    fi
    
    local prproj_count=$(find "$premiere_dir" -name "*.prproj" 2>/dev/null | wc -l)
    success "Premiere Pro备份完成！共备份 $prproj_count 个项目文件"
}

# ==================== Final Cut Pro 备份 ====================
backup_finalcut() {
    info "开始备份 Final Cut Pro..."
    
    local finalcut_dir="$BACKUP_DIR/FinalCut"
    
    # 1. 备份Final Cut Pro库文件
    info "备份Final Cut Pro库文件..."
    find "/Users/<USER>" -name "*.fcpbundle" -exec cp -R {} "$finalcut_dir/Libraries/" \; 2>/dev/null || mkdir -p "$finalcut_dir/Libraries"
    
    # 2. 备份用户配置
    info "备份Final Cut Pro用户配置..."
    if [ -d "/Users/<USER>/Library/Preferences/com.apple.FinalCut" ]; then
        cp -R "/Users/<USER>/Library/Preferences/com.apple.FinalCut" "$finalcut_dir/User_Preferences/" 2>/dev/null
        success "Final Cut Pro用户偏好设置备份完成"
    fi
    
    # 3. 备份应用支持文件
    if [ -d "/Users/<USER>/Library/Application Support/Final Cut Pro" ]; then
        cp -R "/Users/<USER>/Library/Application Support/Final Cut Pro" "$finalcut_dir/User_AppSupport/" 2>/dev/null
        success "Final Cut Pro应用支持文件备份完成"
    fi
    
    # 4. 备份Motion模板和插件
    if [ -d "/Users/<USER>/Library/Application Support/Motion" ]; then
        cp -R "/Users/<USER>/Library/Application Support/Motion" "$finalcut_dir/Motion_Templates/" 2>/dev/null
        success "Motion模板备份完成"
    fi
    
    # 5. 备份Compressor设置
    if [ -d "/Users/<USER>/Library/Application Support/Compressor" ]; then
        cp -R "/Users/<USER>/Library/Application Support/Compressor" "$finalcut_dir/Compressor_Settings/" 2>/dev/null
        success "Compressor设置备份完成"
    fi
    
    local fcpbundle_count=$(find "$finalcut_dir" -name "*.fcpbundle" 2>/dev/null | wc -l)
    success "Final Cut Pro备份完成！共备份 $fcpbundle_count 个库文件"
}

# ==================== 系统设置备份 ====================
backup_system_settings() {
    info "开始备份系统设置..."
    
    local system_dir="$BACKUP_DIR/System"
    
    # 1. 备份系统偏好设置
    info "备份系统偏好设置..."
    mkdir -p "$system_dir/SystemPreferences"
    
    # 重要的系统偏好设置文件
    local pref_files=(
        "com.apple.dock.plist"
        "com.apple.finder.plist"
        "com.apple.menuextra.clock.plist"
        "com.apple.screensaver.plist"
        "com.apple.desktop.plist"
        "com.apple.systempreferences.plist"
        "com.apple.LaunchServices.plist"
        "com.apple.spaces.plist"
        "com.apple.symbolichotkeys.plist"
    )
    
    for pref in "${pref_files[@]}"; do
        if [ -f "/Users/<USER>/Library/Preferences/$pref" ]; then
            cp "/Users/<USER>/Library/Preferences/$pref" "$system_dir/SystemPreferences/" 2>/dev/null
        fi
    done
    success "系统偏好设置备份完成"
    
    # 2. 备份键盘快捷键
    info "备份键盘快捷键..."
    if [ -f "/Users/<USER>/Library/Preferences/com.apple.symbolichotkeys.plist" ]; then
        cp "/Users/<USER>/Library/Preferences/com.apple.symbolichotkeys.plist" "$system_dir/Keyboard_Shortcuts.plist" 2>/dev/null
        success "键盘快捷键备份完成"
    fi
    
    # 3. 备份网络设置
    info "备份网络设置..."
    if [ -d "/Library/Preferences/SystemConfiguration" ]; then
        sudo cp -R "/Library/Preferences/SystemConfiguration" "$system_dir/Network_Settings/" 2>/dev/null || warning "网络设置备份失败（需要管理员权限）"
    fi
    
    # 4. 备份SSH配置
    info "备份SSH配置..."
    if [ -d "/Users/<USER>/.ssh" ]; then
        cp -R "/Users/<USER>/.ssh" "$system_dir/SSH_Config/" 2>/dev/null
        success "SSH配置备份完成"
    fi
    
    # 5. 备份Shell配置
    info "备份Shell配置..."
    local shell_files=(".zshrc" ".bashrc" ".bash_profile" ".profile" ".vimrc")
    mkdir -p "$system_dir/Shell_Config"
    
    for file in "${shell_files[@]}"; do
        if [ -f "/Users/<USER>/$file" ]; then
            cp "/Users/<USER>/$file" "$system_dir/Shell_Config/" 2>/dev/null
        fi
    done
    success "Shell配置文件备份完成"
    
    # 6. 备份Homebrew列表
    info "备份Homebrew软件列表..."
    if command -v brew &> /dev/null; then
        brew list > "$system_dir/homebrew_packages.txt" 2>/dev/null
        brew list --cask > "$system_dir/homebrew_casks.txt" 2>/dev/null
        success "Homebrew软件列表备份完成"
    fi
    
    success "系统设置备份完成"
}

# ==================== 主执行流程 ====================
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    视频编辑软件完整备份脚本 v1.0"
    echo "    支持：DaVinci Resolve, Premiere Pro, Final Cut Pro"
    echo "    包含：项目文件、配置、系统设置"
    echo "=================================================="
    echo -e "${NC}"
    
    # 1. 预检查
    check_disk_space
    create_backup_structure
    stop_applications
    
    # 2. 开始备份
    info "开始完整备份流程..."
    
    backup_davinci
    backup_premiere  
    backup_finalcut
    backup_system_settings
    
    # 3. 生成备份报告
    info "生成备份报告..."
    
    cat > "$BACKUP_DIR/backup_report.txt" << EOF
视频编辑软件完整备份报告
========================
备份时间: $(date)
备份路径: $BACKUP_DIR
系统信息: $(sw_vers -productName) $(sw_vers -productVersion)

备份内容统计:
- DaVinci Resolve项目: $(find "$BACKUP_DIR/DaVinci" -name "*.resolve" 2>/dev/null | wc -l | xargs) 个
- Premiere Pro项目: $(find "$BACKUP_DIR/Premiere" -name "*.prproj" 2>/dev/null | wc -l | xargs) 个  
- Final Cut Pro库: $(find "$BACKUP_DIR/FinalCut" -name "*.fcpbundle" 2>/dev/null | wc -l | xargs) 个
- 备份总大小: $(du -sh "$BACKUP_DIR" | cut -f1)

备份目录结构:
$(tree "$BACKUP_DIR" 2>/dev/null || find "$BACKUP_DIR" -type d | head -20)
EOF
    
    # 4. 创建快速恢复脚本
    cat > "$BACKUP_DIR/Scripts/quick_restore.sh" << 'EOF'
#!/bin/bash
# 快速恢复脚本
echo "=== 视频编辑软件快速恢复 ==="
BACKUP_DIR="$(dirname "$(dirname "$0")")"

echo "恢复DaVinci Resolve..."
if [ -d "$BACKUP_DIR/DaVinci/Projects_2024" ]; then
    rsync -av "$BACKUP_DIR/DaVinci/Projects_2024/" "/Users/<USER>/Documents/2024_剪辑工程/"
fi
if [ -d "$BACKUP_DIR/DaVinci/User_Preferences" ]; then
    cp -R "$BACKUP_DIR/DaVinci/User_Preferences/" "/Users/<USER>/Library/Preferences/Blackmagic Design"
fi

echo "恢复Premiere Pro..."
if [ -d "$BACKUP_DIR/Premiere/User_Preferences" ]; then
    cp -R "$BACKUP_DIR/Premiere/User_Preferences/" "/Users/<USER>/Library/Preferences/"
fi

echo "恢复Final Cut Pro..."
if [ -d "$BACKUP_DIR/FinalCut/User_Preferences" ]; then
    cp -R "$BACKUP_DIR/FinalCut/User_Preferences/" "/Users/<USER>/Library/Preferences/"
fi

echo "恢复系统设置..."
if [ -d "$BACKUP_DIR/System/SystemPreferences" ]; then
    cp -R "$BACKUP_DIR/System/SystemPreferences/"* "/Users/<USER>/Library/Preferences/"
fi

echo "✅ 快速恢复完成！请重启相关应用程序"
EOF
    
    chmod +x "$BACKUP_DIR/Scripts/quick_restore.sh"
    
    # 5. 完成
    local backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    success "🎉 完整备份流程完成！"
    success "📍 备份位置：$BACKUP_DIR"
    success "📊 备份大小：$backup_size"
    success "📋 备份报告：$BACKUP_DIR/backup_report.txt"
    success "🔄 恢复脚本：$BACKUP_DIR/Scripts/quick_restore.sh"
    
    # 打开备份目录
    open "$BACKUP_DIR"
    
    info "备份完成！建议将备份文件复制到外部存储设备以确保安全。"
}

# 执行主函数
main "$@"
