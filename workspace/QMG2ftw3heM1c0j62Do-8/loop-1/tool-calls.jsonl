[{"toolCallId": "call_3to2wwqi2wy6k1vepj0n7ge1", "name": "web_search", "args": {"count": 10, "query": "latest GUI Agent papers"}, "result": [{"title": "OSU-NLP-Group/GUI-Agents-Paper-List", "url": "https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List", "content": "GitHubGitHub · 翻译此页2024年12月27日 — This repo covers a variety of papers related to GUI Agents, such as: Datasets; Benchmarks; Models; Agent frameworks; Vision, language, ...<PERSON>'s PapersGrounding (8)Benchmark (90)Evaluation (9)"}, {"title": "OSU-NLP-Group/GUI-Agents-Paper-List", "url": "https://github.com/OSU-NLP-Group/GUI-Agents-Paper-List/blob/main/paper_by_key/paper_evaluation.md", "content": "GitHubGitHub · 翻译此页TLDR: This paper introduces WebVoyager, an innovative web agent powered by Large Multimodal Models (LMMs) that can complete user instructions end-to-end by ..."}, {"title": "Search for GUI", "url": "https://paperswithcode.com/search?&q=GUI", "content": "Papers With CodePapers With Code · 翻译此页UI-TARS: Pioneering Automated GUI Interaction with Native Agents · 2 code implementations • 21 Jan 2025. This paper introduces UI-TARS, a native GUI agent ..."}, {"title": "[2504.20464] A Survey on GUI Agents with Foundation ...", "url": "https://arxiv.org/abs/2504.20464", "content": "arXivarXiv · 翻译此页作者：J Li · 2025 — This paper provides a structured survey of recent advances in GUI agents, focusing on architectures enhanced by Reinforcement Learning (RL)."}, {"title": "ProgRM: Build Better GUI Agents with Progress Rewards", "url": "https://arxiv.org/abs/2505.18121", "content": "arXivarXiv · 翻译此页作者：D <PERSON> · 2025 — We propose Progress Reward Model (ProgRM) to provide dense informative intermediate rewards by predicting a task completion progress for each step in online ..."}, {"title": "Empowering Generalizable GUI Agents with One Exploration", "url": "https://openaccess.thecvf.com/content/CVPR2025/papers/Sun_GUI-Xplore_Empowering_Generalizable_GUI_Agents_with_One_Exploration_CVPR_2025_paper.pdf", "content": "CVF Open AccessCVF Open AccessPDF作者：Y Sun · 2025 · 被引用次数：5 — Comparison between the current GUI agent paradigm and our exploration-based paradigm. (a) The current paradigm only learns generalized GUI knowledge during ..."}, {"title": "GUI-Agent - a xlbqc Collection", "url": "https://huggingface.co/collections/xlbqc/gui-agent-67bc272a55edf2ef92ae1076", "content": "Hugging FaceHugging FaceMobile-Agent-V: Learning Mobile Device Operation Through Video-Guided Multi-Agent Collaboration. Paper • 2502.17110 • Published Feb 24 • 13."}, {"title": "Large Language Model-Brained GUI Agents: A Survey", "url": "https://openreview.net/forum?id=xChvYjvXTp", "content": "OpenReviewOpenReview · 翻译此页作者：C Zhang · 被引用次数：58 — This paper presents a comprehensive survey of LLM-brained GUI agents, exploring their historical evolution, core components, and advanced techniques."}, {"title": "CogAgent: A Visual Language Model for GUI Agents", "url": "https://paperswithcode.com/paper/cogagent-a-visual-language-model-for-gui", "content": "Papers With CodePapers With Code · 翻译此页In this paper, we introduce CogAgent, an 18-billion-parameter visual language model (VLM) specializing in GUI understanding and navigation."}, {"title": "LLM-Brained GUI Agents Survey", "url": "https://vyokky.github.io/LLM-Brained-GUI-Agents-Survey/", "content": "GitHubGitHub · 翻译此页A Collection of Research Papers and Projects in: Large Language Model-Brained GUI Agents: A Survey. ; ScienceBoard: Evaluating Multimodal Autonomous Agents in ..."}], "executionTime": 2751}]