[{"toolCallId": "call_xyb3zayv21dk1k4x73533827", "name": "web_search", "args": {"count": 10, "query": "MCP service add guide"}, "result": [{"title": "Build an MCP Server", "url": "https://modelcontextprotocol.io/quickstart/server", "content": "Model Context ProtocolModel Context Protocol · 翻译此页In this tutorial, we'll build a simple MCP weather server and connect it to a host, <PERSON> for Desktop. We'll start with a basic setup, and then progress to ..."}, {"title": "Building MCP servers for ChatGPT and API integrations", "url": "https://platform.openai.com/docs/mcp", "content": "OpenAIOpenAI · 翻译此页In this guide, we'll cover how to build a remote MCP server that reads data from a private data source (a vector store) and makes it available in ChatGPT via ..."}, {"title": "Use MCP servers in VS Code", "url": "https://code.visualstudio.com/docs/copilot/chat/mcp-servers", "content": "Visual Studio CodeVisual Studio Code · 翻译此页This article guides you through setting up MCP servers and using tools with agent mode in Visual Studio Code."}, {"title": "Configuring an MCP Service - 华为云", "url": "https://support.huaweicloud.com/intl/en-us/usermanual-pangulm/pangulm_04_0500.html", "content": "huaweicloud.comhuaweicloud.com · 翻译此页2天前 — ... MCP service. In the Add MCP Service dialog box, click the Pre installed Service or Personal Service tab, click Add, and click OK. Figure 2 Add."}, {"title": "Connect to Local MCP Servers", "url": "https://modelcontextprotocol.io/quickstart/user", "content": "Model Context ProtocolModel Context Protocol · 翻译此页This guide demonstrates how to connect to local MCP servers using Claude Desktop as an example, one of the many clients that support MCP. While we focus on ..."}, {"title": "MCP service management", "url": "https://www.alibabacloud.com/help/doc-detail/2883263.html", "content": "Alibaba CloudAlibaba Cloud · 翻译此页2025年6月17日 — Add tools to an MCP service. After successful creation, you can add tools to the service. The procedure is as follows: Click the target ...缺少字词： guide ‎| 必须包含： guide"}, {"title": "MCP server: A step-by-step guide to building from scratch", "url": "https://composio.dev/blog/mcp-server-step-by-step-guide-to-building-from-scrtch", "content": "ComposioComposio · 翻译此页2025年7月3日 — 1. Visit the Composio MCP Directory page · 2. Select Tools you need, keep an eye on the following: · 3. Go to the Installation Steps section on ..."}, {"title": "Model Context Protocol (MCP)", "url": "https://docs.anthropic.com/en/docs/claude-code/mcp", "content": "AnthropicAnthropic · 翻译此页​. Add MCP servers from JSON configuration · Make sure the JSON is properly escaped in your shell · The JSON must conform to the MCP server configuration schema ..."}, {"title": "Cursor – Model Context Protocol (MCP)", "url": "https://docs.cursor.com/context/model-context-protocol", "content": "CursorCursor · 翻译此页Enable or disable MCP tools directly from the chat interface. Click a tool name in the tools list to toggle it. Disabled tools won't be loaded into context or ..."}], "executionTime": 2702}, {"toolCallId": "call_iyw9kqa4veuuqvyqrbq2ta6b", "name": "browser_navigate", "args": {"url": "https://agent-tars.com/guide/basic/mcp.html"}, "result": {"status": "success", "url": "https://agent-tars.com/guide/basic/mcp.html", "message": "Navigated success"}, "executionTime": 1355}, {"toolCallId": "call_jzinhvqsqni5pyzyluv57rjm", "name": "browser_navigate", "args": {"url": "https://github.com/MiniMax-AI/MiniMax-MCP"}, "result": {"status": "success", "url": "https://github.com/MiniMax-AI/MiniMax-MCP", "message": "Navigated success"}, "executionTime": 282}, {"toolCallId": "call_q7vzaq4i10v2xliutv8fp4d0", "name": "browser_get_markdown", "args": {"page": 1}, "result": {"content": "# GitHub - MiniMax-AI/MiniMax-MCP: Official MiniMax Model Context Protocol (MCP) server that enables interaction with powerful Text to Speech, image generation and video generation APIs.\n\nOfficial MiniMax Model Context Protocol (MCP) server that enables interaction with powerful Text to Speech and video/image generation APIs. This server allows MCP clients like [<PERSON>](https://www.anthropic.com/claude), [<PERSON>urs<PERSON>](https://www.cursor.so/), [Windsurf](https://codeium.com/windsurf), [OpenAI Agents](https://github.com/openai/openai-agents-python) and others to generate speech, clone voices, generate video, generate image and more.\n\n## Documentation\n\n*   [中文文档](https://github.com/MiniMax-AI/MiniMax-MCP/blob/main/README-CN.md)\n*   [MiniMax-MCP-JS](https://github.com/MiniMax-AI/MiniMax-MCP-JS) - Official JavaScript implementation of MiniMax MCP\n\n## Quickstart with MCP Client\n\n1.  Get your API key from [MiniMax](https://www.minimax.io/platform/user-center/basic-information/interface-key).\n2.  Install `uv` (Python package manager), install with `curl -LsSf https://astral.sh/uv/install.sh | sh` or see the `uv` [repo](https://github.com/astral-sh/uv) for additional install methods.\n3.  **Important**: The API host and key vary by region and must match; otherwise, you'll encounter an `Invalid API key` error.\n\n| Region | Global | Mainland |\n| --- | --- | --- |\n| MINIMAX\\_API\\_KEY | go get from [MiniMax Global](https://www.minimax.io/platform/user-center/basic-information/interface-key) | go get from [MiniMax](https://platform.minimaxi.com/user-center/basic-information/interface-key) |\n| MINIMAX\\_API\\_HOST | [https://api.minimax.io](https://api.minimax.io/) | [https://api.minimaxi.com](https://api.minimaxi.com/) |\n\n### Claude Desktop\n\nGo to `Claude > Settings > Developer > Edit Config > claude_desktop_config.json` to include the following:\n\n```\n{\n  \"mcpServers\": {\n    \"MiniMax\": {\n      \"command\": \"uvx\",\n      \"args\": [\n        \"minimax-mcp\",\n        \"-y\"\n      ],\n      \"env\": {\n        \"MINIMAX_API_KEY\": \"insert-your-api-key-here\",\n        \"MINIMAX_MCP_BASE_PATH\": \"local-output-dir-path, such as /User/xxx/Desktop\",\n        \"MINIMAX_API_HOST\": \"api host, https://api.minimax.io | https://api.minimaxi.com\",\n        \"MINIMAX_API_RESOURCE_MODE\": \"optional, [url|local], url is default, audio/image/video are downloaded locally or provided in URL format\"\n      }\n    }\n  }\n}\n\n```\n\n⚠️ Warning: The API key needs to match the host. If an error \"API Error: invalid api key\" occurs, please check your api host:\n\n*   Global Host：`https://api.minimax.io`\n*   Mainland Host：`https://api.minimaxi.com`\n\nIf you're using Windows, you will have to enable \"Developer Mode\" in Claude Desktop to use the MCP server. Click \"Help\" in the hamburger menu in the top left and select \"Enable Developer Mode\".\n\n### Cursor\n\nGo to `Cursor -> Preferences -> Cursor Settings -> MCP -> Add new global MCP Server` to add above config.\n\nThat's it. Your MCP client can now interact with MiniMax through these tools:\n\n## Transport\n\nWe support two transport types: stdio and sse.\n\n| stdio | SSE |\n| --- | --- |\n| Run locally | Can be deployed locally or in the cloud |\n| Communication through `stdout` | Communication through `network` |\n| Input: Supports processing `local files` or valid `URL` resources | Input: When deployed in the cloud, it is recommended to use `URL` for input |\n\n## Available Tools\n\n| tool | description |\n| --- | --- |\n| `text_to_audio` | Convert text to audio with a given voice |\n| `list_voices` | List all voices available |\n| `voice_clone` | Clone a voice using provided audio files |\n| `generate_video` | Generate a video from a prompt |\n| `text_to_image` | Generate a image from a prompt |\n| `query_video_generation` | Query the result of video generation task |\n| `music_generation` | Generate a music track from a prompt and lyrics |\n| `voice_design` | Generate a voice from a prompt using preview text |\n\n## Release Notes\n\n### July 2, 2025\n\n#### 🆕 What's New\n\n*   **Voice Design**: New `voice_design` tool - create custom voices from descriptive prompts with preview audio\n*   **Video Enhancement**: Added `MiniMax-Hailuo-02` model with ultra-clear quality and duration/resolution controls\n*   **Music Generation**: Enhanced `music_generation` tool powered by `music-1.5` model\n\n#### 📈 Enhanced Tools\n\n*   `voice_design` - Generate personalized voices from text descriptions\n*   `generate_video` - Now supports MiniMax-Hailuo-02 with 6s/10s duration and 768P/1080P resolution options\n*   `music_generation` - High-quality music creation with music-1.5 model\n\n## FAQ\n\n### 1\\. invalid api key\n\nPlease ensure your API key and API host are regionally aligned\n\n| Region | Global | Mainland |\n| --- | --- | --- |\n| MINIMAX\\_API\\_KEY | go get from [MiniMax Global](https://www.minimax.io/platform/user-center/basic-information/interface-key) | go get from [MiniMax](https://platform.minimaxi.com/user-center/basic-information/interface-key) |\n| MINIMAX\\_API\\_HOST | [https://api.minimax.io](https://api.minimax.io/) | [https://api.minimaxi.com](https://api.minimaxi.com/) |\n\n### 2\\. spawn uvx ENOENT\n\nPlease confirm its absolute path by running this command in your terminal:\n\nOnce you obtain the absolute path (e.g., /usr/local/bin/uvx), update your configuration to use that path (e.g., \"command\": \"/usr/local/bin/uvx\").\n\n### 3\\. How to use `generate_video` in async-mode\n\nDefine completion rules before starting: [](https://camo.githubusercontent.com/64f42a66a81d074a7b1e0a28fe9b6549d26b2e072e2f12fb89fa147a80ba9e92/68747470733a2f2f7075626c69632d63646e2d766964656f2d646174612d616c67656e672e6f73732d636e2d77756c616e63686162752e616c6979756e63732e636f6d2f637572736f725f72756c65322e706e673f782d6f73732d70726f636573733d696d6167652f726573697a652c705f35302f666f726d61742c77656270)Alternatively, these rules can be configured in your IDE settings (e.g., Cursor):[](https://camo.githubusercontent.com/8e0e6f9412be0a8af801024e0832b0c41468062724abf6dd6e557caf381ab953/68747470733a2f2f7075626c69632d63646e2d766964656f2d646174612d616c67656e672e6f73732d636e2d77756c616e63686162752e616c6979756e63732e636f6d2f637572736f725f766964656f5f72756c652e706e673f782d6f73732d70726f636573733d696d6167652f726573697a652c705f35302f666f726d61742c77656270)\n\n## Example usage\n\n⚠️ Warning: Using these tools may incur costs.\n\n### 1\\. broadcast a segment of the evening news\n\n### 2\\. clone a voice\n\n### 3\\. generate a video\n\n### 4\\. generate images", "pagination": {"currentPage": 1, "totalPages": 1, "hasMorePages": false}, "title": "GitHub - MiniMax-AI/MiniMax-MCP: Official MiniMax Model Context Protocol (MCP) server that enables interaction with powerful Text to Speech, image generation and video generation APIs."}, "executionTime": 33}, {"toolCallId": "call_62jzz8qtmg4iab978h7ag69p", "name": "list_directory", "args": {"path": "/Users/<USER>/TARS-Agent/workspace"}, "result": [{"type": "text", "text": "[FILE] .DS_Store\n[DIR] BwCrwzk-ssRPvbv-JKMdL\n[DIR] QMG2ftw3heM1c0j62Do-8\n[DIR] _UUmXhMiyOhtcMSbBJ7i4\n[DIR] f24qCNnnXrmsPZJoupwIa"}], "executionTime": 2}, {"toolCallId": "call_w9esourm2ehala0rcvr93k7r", "name": "browser_navigate", "args": {"url": "https://www.blackmagicdesign.com/products/davinciresolve"}, "result": {"status": "success", "url": "https://www.blackmagicdesign.com/products/davinciresolve", "message": "Navigated success"}, "executionTime": 976}]