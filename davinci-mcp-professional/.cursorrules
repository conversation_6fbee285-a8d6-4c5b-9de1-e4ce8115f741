# DaVinci Resolve MCP Server development rules for Cursor

# Rules for the project
rules:
  # Quick navigation commands
  - name: View project structure
    match: /(project|structure|files)/i
    actions: ls -la
    
  - name: Show main server file
    match: /show (server|main|resolve_mcp_server)( file)?/i
    actions: cat resolve_mcp_server.py
    
  - name: Edit main server file
    match: /edit (server|main|resolve_mcp_server)( file)?/i
    actions: open resolve_mcp_server.py

  # Run commands
  - name: Run server in dev mode
    match: /run( server)?( in dev)?/i
    actions: ./run-now.sh
    
  - name: Setup server
    match: /setup( server)?/i
    actions: ./setup.sh
    
  # View logs and instructions
  - name: Show README
    match: /show readme/i
    actions: cat README.md
    
  - name: View changelog
    match: /show changelog/i
    actions: cat CHANGELOG.md

  # DaVinci Resolve specific
  - name: Check Resolve environment
    match: /check (resolve|environment|env|paths)/i
    actions: |
      echo "RESOLVE_SCRIPT_API = $RESOLVE_SCRIPT_API"
      echo "RESOLVE_SCRIPT_LIB = $RESOLVE_SCRIPT_LIB"
      echo "PYTHONPATH = $PYTHONPATH"
      
  - name: Check if Resolve is running
    match: /is resolve running/i
    actions: ps -ef | grep -i "[D]aVinci Resolve"

# Directory settings
directories:
  # Ignore virtual environment folder in searches
  - path: venv
    excludeFromSearch: true 