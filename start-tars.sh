#!/bin/bash
cd "$(dirname "$0")"
echo "🚀 启动TARS Agent Web界面..."
echo "📁 工作目录: $(pwd)"
echo "🌐 访问地址: http://localhost:8888"
echo ""

# 启动TARS并在后台运行
agent-tars web &
TARS_PID=$!

# 等待服务启动
sleep 3

# 尝试打开浏览器
if command -v open &> /dev/null; then
    # macOS
    open http://localhost:8888
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open http://localhost:8888
else
    echo "请手动打开浏览器访问: http://localhost:8888"
fi

echo "TARS Agent正在运行 (PID: $TARS_PID)"
echo "按 Ctrl+C 停止服务"

# 等待用户中断
wait $TARS_PID
