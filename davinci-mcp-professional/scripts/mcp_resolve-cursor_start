#!/bin/bash
# mcp_resolve-cursor_start
# Script to start DaVinci Resolve MCP server for Cursor integration
# This is a convenience wrapper with Cursor-specific functionality

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Get the script directory and project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." &> /dev/null && pwd )"
LOG_FILE="$SCRIPT_DIR/cursor_resolve_server.log"
VENV_DIR="$PROJECT_ROOT/venv"

# Display banner
echo -e "${BLUE}=============================================${NC}"
echo -e "${BLUE}  DaVinci Resolve - Cursor MCP Integration  ${NC}"
echo -e "${BLUE}=============================================${NC}"

# Check if DaVinci Resolve is running
check_resolve_running() {
    if ps -ef | grep -i "[D]aVinci Resolve" > /dev/null; then
        echo -e "${GREEN}✓ DaVinci Resolve is running${NC}"
        return 0
    else
        echo -e "${RED}✗ DaVinci Resolve is not running${NC}"
        echo -e "${YELLOW}Please start DaVinci Resolve before continuing${NC}"
        return 1
    fi
}

# Initialize log file
echo "Starting Cursor-Resolve MCP Server at $(date)" > "$LOG_FILE"

# Check environment variables
check_environment() {
    echo -e "${YELLOW}Checking environment variables...${NC}"
    
    # Import platform utilities if available
    if [ -f "$PROJECT_ROOT/src/utils/platform.py" ]; then
        echo -e "${GREEN}Using platform-specific config from project${NC}"
    else
        # Set default paths if not already set (macOS defaults)
        export RESOLVE_SCRIPT_API="${RESOLVE_SCRIPT_API:-/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting}"
        export RESOLVE_SCRIPT_LIB="${RESOLVE_SCRIPT_LIB:-/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so}"
        export PYTHONPATH="$PYTHONPATH:$RESOLVE_SCRIPT_API/Modules/"
    fi
    
    export PYTHONUNBUFFERED=1
    
    # Log environment
    echo "Environment variables:" >> "$LOG_FILE"
    echo "RESOLVE_SCRIPT_API=$RESOLVE_SCRIPT_API" >> "$LOG_FILE"
    echo "RESOLVE_SCRIPT_LIB=$RESOLVE_SCRIPT_LIB" >> "$LOG_FILE"
    echo "PYTHONPATH=$PYTHONPATH" >> "$LOG_FILE"
    
    # Check virtual environment
    if [ ! -d "$VENV_DIR" ]; then
        echo -e "${RED}✗ Virtual environment not found at: $VENV_DIR${NC}"
        echo -e "${YELLOW}Please run setup.sh to create the virtual environment${NC}"
        return 1
    fi
    
    # Check if files exist (if manually configured)
    if [ -n "$RESOLVE_SCRIPT_API" ] && [ ! -d "$RESOLVE_SCRIPT_API" ]; then
        echo -e "${RED}✗ DaVinci Resolve API path not found: $RESOLVE_SCRIPT_API${NC}"
        echo -e "${YELLOW}This path might be different on your system.${NC}"
        echo -e "${YELLOW}You can set it manually in your shell profile.${NC}"
        return 1
    fi
    
    if [ -n "$RESOLVE_SCRIPT_LIB" ] && [ ! -f "$RESOLVE_SCRIPT_LIB" ]; then
        echo -e "${RED}✗ DaVinci Resolve library not found: $RESOLVE_SCRIPT_LIB${NC}"
        echo -e "${YELLOW}This path might be different on your system.${NC}"
        echo -e "${YELLOW}You can set it manually in your shell profile.${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✓ Environment variables set correctly${NC}"
    return 0
}

# Setup Cursor MCP config
setup_cursor_config() {
    echo -e "${YELLOW}Setting up Cursor MCP configuration...${NC}"
    
    # Create system-level cursor config directory if it doesn't exist
    CURSOR_CONFIG_DIR="$HOME/.cursor/mcp"
    mkdir -p "$CURSOR_CONFIG_DIR"
    
    # Create or update system-level config file
    CURSOR_CONFIG_FILE="$CURSOR_CONFIG_DIR/config.json"
    
    # Generate system-level config based on template with absolute paths
    cat > "$CURSOR_CONFIG_FILE" << EOF
{
  "mcpServers": {
    "davinci-resolve": {
      "name": "DaVinci Resolve MCP",
      "command": "${VENV_DIR}/bin/python",
      "args": ["${PROJECT_ROOT}/resolve_mcp_server.py"]
    }
  }
}
EOF

    # Create project-level cursor config directory if it doesn't exist
    PROJECT_CURSOR_DIR="$PROJECT_ROOT/.cursor"
    mkdir -p "$PROJECT_CURSOR_DIR"
    
    # Create or update project-level config file
    PROJECT_CONFIG_FILE="$PROJECT_CURSOR_DIR/mcp.json"
    
    # Generate project-level config with absolute paths (same as system-level)
    cat > "$PROJECT_CONFIG_FILE" << EOF
{
  "mcpServers": {
    "davinci-resolve": {
      "name": "DaVinci Resolve MCP",
      "command": "${VENV_DIR}/bin/python",
      "args": ["${PROJECT_ROOT}/resolve_mcp_server.py"]
    }
  }
}
EOF
    
    echo -e "${GREEN}✓ Cursor MCP config created at: $CURSOR_CONFIG_FILE${NC}"
    echo -e "${GREEN}✓ Project-level MCP config created at: $PROJECT_CONFIG_FILE${NC}"
    echo -e "${GREEN}✓ MCP config points to: ${PROJECT_ROOT}/resolve_mcp_server.py${NC}"
    return 0
}

# Parse arguments
PROJECT_NAME=""
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --project|-p)
                PROJECT_NAME="$2"
                echo -e "${YELLOW}Will attempt to open project: $2${NC}"
                shift 2
                ;;
            *)
                echo -e "${YELLOW}Unknown argument: $1${NC}"
                shift
                ;;
        esac
    done
}

# Main function
main() {
    echo -e "${YELLOW}Starting DaVinci Resolve MCP Server for Cursor...${NC}"
    
    # Check if Resolve is running
    if ! check_resolve_running; then
        echo -e "${YELLOW}DaVinci Resolve not detected. Waiting for 10 seconds...${NC}"
        sleep 10
        if ! check_resolve_running; then
            echo -e "${RED}DaVinci Resolve must be running. Please start it and try again.${NC}"
            exit 1
        fi
    fi
    
    # Check environment
    if ! check_environment; then
        echo -e "${RED}Environment setup failed. Please check paths.${NC}"
        exit 1
    fi
    
    # Setup Cursor config
    if ! setup_cursor_config; then
        echo -e "${RED}Failed to setup Cursor configuration.${NC}"
        exit 1
    fi
    
    # Make scripts executable
    chmod +x "$PROJECT_ROOT/resolve_mcp_server.py"
    
    # Start the server
    echo -e "${GREEN}Starting MCP server...${NC}"
    echo -e "${BLUE}Connecting to DaVinci Resolve...${NC}"
    
    # Run the server with the virtual environment's Python
    if [ -n "$PROJECT_NAME" ]; then
        echo -e "${YELLOW}Opening project: $PROJECT_NAME${NC}"
        "$VENV_DIR/bin/python" "$PROJECT_ROOT/resolve_mcp_server.py" --project "$PROJECT_NAME"
    else
        "$VENV_DIR/bin/python" "$PROJECT_ROOT/resolve_mcp_server.py"
    fi
    
    # Server shouldn't reach this point unless it crashed
    echo -e "${RED}Server exited unexpectedly.${NC}"
    echo "Server exited at $(date)" >> "$LOG_FILE"
    exit 1
}

# Parse any command line arguments
parse_arguments "$@"

# Run main function
main

exit 0 