#!/bin/bash
# Master_Backup_Controller.sh
# 主控备份脚本 - 统一管理所有备份操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 脚本路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🎬 视频编辑备份中心 🎬                      ║"
    echo "║                                                              ║"
    echo "║  一键备份您的所有创作工具和项目数据                            ║"
    echo "║  支持：DaVinci Resolve, Adobe Creative Suite, 系统设置        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示菜单
show_menu() {
    echo -e "${BLUE}请选择备份类型：${NC}"
    echo
    echo -e "${GREEN}1.${NC} 🎬 DaVinci Resolve 快速备份"
    echo -e "${GREEN}2.${NC} 🎨 Adobe Creative Suite 完整备份"
    echo -e "${GREEN}3.${NC} ⚙️  macOS 系统设置备份"
    echo -e "${GREEN}4.${NC} 🎯 视频编辑软件完整备份 (全部)"
    echo -e "${GREEN}5.${NC} 📋 自定义选择备份"
    echo
    echo -e "${YELLOW}6.${NC} 📊 查看备份历史"
    echo -e "${YELLOW}7.${NC} 🔄 恢复向导"
    echo -e "${YELLOW}8.${NC} 🧹 清理旧备份"
    echo
    echo -e "${RED}0.${NC} 退出"
    echo
    echo -n -e "${CYAN}请输入选择 [0-8]: ${NC}"
}

# 检查脚本文件是否存在
check_scripts() {
    local missing_scripts=()
    
    if [ ! -f "$SCRIPT_DIR/Quick_DaVinci_Backup.sh" ]; then
        missing_scripts+=("Quick_DaVinci_Backup.sh")
    fi
    
    if [ ! -f "$SCRIPT_DIR/Adobe_Creative_Backup.sh" ]; then
        missing_scripts+=("Adobe_Creative_Backup.sh")
    fi
    
    if [ ! -f "$SCRIPT_DIR/System_Settings_Backup.sh" ]; then
        missing_scripts+=("System_Settings_Backup.sh")
    fi
    
    if [ ! -f "$SCRIPT_DIR/Video_Editing_Complete_Backup.sh" ]; then
        missing_scripts+=("Video_Editing_Complete_Backup.sh")
    fi
    
    if [ ${#missing_scripts[@]} -gt 0 ]; then
        echo -e "${RED}❌ 缺少以下备份脚本：${NC}"
        printf '%s\n' "${missing_scripts[@]}"
        echo -e "${YELLOW}请确保所有备份脚本都在同一目录下${NC}"
        exit 1
    fi
}

# 执行DaVinci备份
run_davinci_backup() {
    echo -e "${BLUE}🎬 启动 DaVinci Resolve 快速备份...${NC}"
    chmod +x "$SCRIPT_DIR/Quick_DaVinci_Backup.sh"
    "$SCRIPT_DIR/Quick_DaVinci_Backup.sh"
}

# 执行Adobe备份
run_adobe_backup() {
    echo -e "${BLUE}🎨 启动 Adobe Creative Suite 备份...${NC}"
    chmod +x "$SCRIPT_DIR/Adobe_Creative_Backup.sh"
    "$SCRIPT_DIR/Adobe_Creative_Backup.sh"
}

# 执行系统设置备份
run_system_backup() {
    echo -e "${BLUE}⚙️ 启动 macOS 系统设置备份...${NC}"
    chmod +x "$SCRIPT_DIR/System_Settings_Backup.sh"
    "$SCRIPT_DIR/System_Settings_Backup.sh"
}

# 执行完整备份
run_complete_backup() {
    echo -e "${BLUE}🎯 启动视频编辑软件完整备份...${NC}"
    chmod +x "$SCRIPT_DIR/Video_Editing_Complete_Backup.sh"
    "$SCRIPT_DIR/Video_Editing_Complete_Backup.sh"
}

# 自定义选择备份
custom_backup() {
    echo -e "${PURPLE}📋 自定义备份选择${NC}"
    echo
    echo "请选择要备份的项目（可多选，用空格分隔）："
    echo -e "${GREEN}1${NC} DaVinci Resolve"
    echo -e "${GREEN}2${NC} Adobe Creative Suite"
    echo -e "${GREEN}3${NC} macOS 系统设置"
    echo
    echo -n "请输入选择 (例如: 1 3): "
    read -r selections
    
    for selection in $selections; do
        case $selection in
            1)
                echo -e "${BLUE}执行 DaVinci Resolve 备份...${NC}"
                run_davinci_backup
                ;;
            2)
                echo -e "${BLUE}执行 Adobe Creative Suite 备份...${NC}"
                run_adobe_backup
                ;;
            3)
                echo -e "${BLUE}执行 macOS 系统设置备份...${NC}"
                run_system_backup
                ;;
            *)
                echo -e "${RED}无效选择: $selection${NC}"
                ;;
        esac
    done
}

# 查看备份历史
view_backup_history() {
    echo -e "${YELLOW}📊 备份历史记录${NC}"
    echo
    
    local backup_dirs=(
        "/Users/<USER>/Desktop/DaVinci_Quick_Backup_*"
        "/Users/<USER>/Desktop/Adobe_Creative_Backup_*"
        "/Users/<USER>/Desktop/System_Settings_Backup_*"
        "/Users/<USER>/Desktop/Video_Editing_Backups/Complete_Backup_*"
    )
    
    echo -e "${BLUE}最近的备份：${NC}"
    for pattern in "${backup_dirs[@]}"; do
        if ls $pattern 1> /dev/null 2>&1; then
            ls -dt $pattern | head -5 | while read dir; do
                if [ -d "$dir" ]; then
                    local size=$(du -sh "$dir" 2>/dev/null | cut -f1)
                    local date=$(basename "$dir" | grep -o '[0-9]\{8\}_[0-9]\{6\}' | sed 's/_/ /')
                    echo "  📁 $(basename "$dir") - $size - $date"
                fi
            done
        fi
    done
    
    echo
    echo -n "按Enter返回主菜单..."
    read
}

# 恢复向导
restore_wizard() {
    echo -e "${CYAN}🔄 恢复向导${NC}"
    echo
    echo "请选择要恢复的备份类型："
    echo -e "${GREEN}1${NC} DaVinci Resolve"
    echo -e "${GREEN}2${NC} Adobe Creative Suite"
    echo -e "${GREEN}3${NC} macOS 系统设置"
    echo
    echo -n "请输入选择 [1-3]: "
    read -r restore_choice
    
    case $restore_choice in
        1)
            echo -e "${BLUE}查找 DaVinci Resolve 备份...${NC}"
            ls -dt /Users/<USER>/Desktop/DaVinci_Quick_Backup_* 2>/dev/null | head -5 | nl
            echo -n "请选择要恢复的备份编号: "
            read -r backup_num
            backup_dir=$(ls -dt /Users/<USER>/Desktop/DaVinci_Quick_Backup_* 2>/dev/null | sed -n "${backup_num}p")
            if [ -n "$backup_dir" ] && [ -f "$backup_dir/restore.sh" ]; then
                echo -e "${GREEN}执行恢复: $backup_dir${NC}"
                "$backup_dir/restore.sh"
            else
                echo -e "${RED}恢复脚本不存在${NC}"
            fi
            ;;
        2)
            echo -e "${BLUE}查找 Adobe Creative Suite 备份...${NC}"
            ls -dt /Users/<USER>/Desktop/Adobe_Creative_Backup_* 2>/dev/null | head -5 | nl
            echo -n "请选择要恢复的备份编号: "
            read -r backup_num
            backup_dir=$(ls -dt /Users/<USER>/Desktop/Adobe_Creative_Backup_* 2>/dev/null | sed -n "${backup_num}p")
            if [ -n "$backup_dir" ] && [ -f "$backup_dir/restore_adobe.sh" ]; then
                echo -e "${GREEN}执行恢复: $backup_dir${NC}"
                "$backup_dir/restore_adobe.sh"
            else
                echo -e "${RED}恢复脚本不存在${NC}"
            fi
            ;;
        3)
            echo -e "${BLUE}查找 macOS 系统设置备份...${NC}"
            ls -dt /Users/<USER>/Desktop/System_Settings_Backup_* 2>/dev/null | head -5 | nl
            echo -n "请选择要恢复的备份编号: "
            read -r backup_num
            backup_dir=$(ls -dt /Users/<USER>/Desktop/System_Settings_Backup_* 2>/dev/null | sed -n "${backup_num}p")
            if [ -n "$backup_dir" ] && [ -f "$backup_dir/restore_system_settings.sh" ]; then
                echo -e "${GREEN}执行恢复: $backup_dir${NC}"
                "$backup_dir/restore_system_settings.sh"
            else
                echo -e "${RED}恢复脚本不存在${NC}"
            fi
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
    
    echo
    echo -n "按Enter返回主菜单..."
    read
}

# 清理旧备份
cleanup_old_backups() {
    echo -e "${YELLOW}🧹 清理旧备份${NC}"
    echo
    echo "此操作将删除7天前的备份文件"
    echo -e "${RED}⚠️ 此操作不可逆！${NC}"
    echo -n "确认继续？[y/N]: "
    read -r confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo "清理中..."
        
        # 清理各类备份（保留最近7天）
        find "/Users/<USER>/Desktop" -name "DaVinci_Quick_Backup_*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
        find "/Users/<USER>/Desktop" -name "Adobe_Creative_Backup_*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
        find "/Users/<USER>/Desktop" -name "System_Settings_Backup_*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
        find "/Users/<USER>/Desktop/Video_Editing_Backups" -name "Complete_Backup_*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
        
        echo -e "${GREEN}✅ 清理完成${NC}"
    else
        echo "取消清理"
    fi
    
    echo
    echo -n "按Enter返回主菜单..."
    read
}

# 主循环
main() {
    # 检查必要的脚本文件
    check_scripts
    
    while true; do
        show_header
        show_menu
        read -r choice
        
        case $choice in
            1)
                run_davinci_backup
                echo -n "按Enter返回主菜单..."
                read
                ;;
            2)
                run_adobe_backup
                echo -n "按Enter返回主菜单..."
                read
                ;;
            3)
                run_system_backup
                echo -n "按Enter返回主菜单..."
                read
                ;;
            4)
                run_complete_backup
                echo -n "按Enter返回主菜单..."
                read
                ;;
            5)
                custom_backup
                echo -n "按Enter返回主菜单..."
                read
                ;;
            6)
                view_backup_history
                ;;
            7)
                restore_wizard
                ;;
            8)
                cleanup_old_backups
                ;;
            0)
                echo -e "${GREEN}感谢使用视频编辑备份中心！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重新输入${NC}"
                sleep 2
                ;;
        esac
    done
}

# 执行主函数
main "$@"
