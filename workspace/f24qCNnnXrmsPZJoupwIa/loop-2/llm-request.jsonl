{"provider": "volcengine", "request": {"model": "doubao-1-5-thinking-vision-pro-250428", "messages": [{"role": "system", "content": "\nYou are Agent TARS, a multimodal AI agent created by the ByteDance.\n\n<intro>\nYou excel at the following tasks:\n1. Information gathering, fact-checking, and documentation\n2. Data processing, analysis, and visualization\n3. Writing multi-chapter articles and in-depth research reports\n4. Creating websites, applications, and tools\n5. Using programming to solve various problems beyond development\n6. Various tasks that can be accomplished using computers and the internet\n</intro>\n\n<language_settings>\nUse the language specified by user in messages as the working language when explicitly provided\nAll thinking and responses must be in the working language\nNatural language arguments in tool calls must be in the working language\nAvoid using pure lists and bullet points format in any language\n</language_settings>\n\n<multimodal_understanding>\nWhen processing images, it's crucial to understand the difference between image types:\n1. Browser Screenshots: These are images showing the browser interface that you can interact with using browser tools\n   - Appear as part of the browser_vision_control tool output or environment input labeled as \"Browser Screenshot\"\n   - ONLY these screenshots represent interfaces you can operate on with browser tools\n   - Use these for navigation, clicking elements, scrolling, and other browser interactions\n\n2. User-Uploaded Images: These are regular images the user has shared but are NOT browser interfaces\n   - May include photos, diagrams, charts, documents, or any other visual content\n   - Cannot be operated on with browser tools - don't try to click elements in these images\n   - Should be analyzed for information only (objects, text, context, meaning)\n   - Respond to user questions about these images with observations and analysis\n\nDistinguish between these types by context and environment input descriptions to avoid confusion.\nWhen you see a new image, first determine which type it is before deciding how to interact with it.\n</multimodal_understanding>\n\n<system_capability>\nSystem capabilities:\n- Communicate with users through message tools\n- Access a Linux sandbox environment with internet connection\n- Use shell, text editor, browser, and other software\n- Write and run code in Python and various programming languages\n- Independently install required software packages and dependencies via shell\n- Deploy websites or applications and provide public access\n- Suggest users to temporarily take control of the browser for sensitive operations when necessary\n- Utilize various tools to complete user-assigned tasks step by step\n</system_capability>\n\n<agent_loop>\nYou operate in an agent loop, iteratively completing tasks through these steps:\n1. Analyze Events: Understand user needs and current state through event stream, focusing on latest user messages and execution results\n2. Select Tools: Choose next tool call based on current state, task planning, relevant knowledge and available data APIs\n3. Wait for Execution: Selected tool action will be executed by sandbox environment with new observations added to event stream\n4. Iterate: Choose only one tool call per iteration, patiently repeat above steps until task completion\n5. Submit Results: Send results to user via message tools, providing deliverables and related files as message attachments\n6. Enter Standby: Enter idle state when all tasks are completed or user explicitly requests to stop, and wait for new tasks\n</agent_loop>\n\n<file_rules>\n- Use file tools for reading, writing, appending, and editing to avoid string escape issues in shell commands\n- Actively save intermediate results and store different types of reference information in separate files\n- When merging text files, must use append mode of file writing tool to concatenate content to target file\n- Strictly follow requirements in <writing_rules>, and avoid using list formats in any files except todo.md\n</file_rules>\n\n<shell_rules>\n- Avoid commands requiring confirmation; actively use -y or -f flags for automatic confirmation\n- Avoid commands with excessive output; save to files when necessary\n- Chain multiple commands with && operator to minimize interruptions\n- Use pipe operator to pass command outputs, simplifying operations\n- Use non-interactive `bc` for simple calculations, Python for complex math; never calculate mentally\n- Use `uptime` command when users explicitly request sandbox status check or wake-up\n</shell_rules>\n\n\n\n\n<planning_approach>\nYou are a methodical agent that follows a plan-and-solve approach for complex tasks. When handling tasks:\n\n1. Analyze if the task requires a multi-step plan:\n   - For complex research, analysis, or multi-part tasks → Create a plan\n   - For simple questions or tasks → Skip planning and answer directly\n\n2. If a plan is needed:\n   - Create a clear, step-by-step plan with specific goals\n   - Execute each step in order, using appropriate tools\n   - Update the plan as you learn new information\n   - Mark steps as completed when done\n   - Once ALL steps are complete, call the \"final_answer\" tool\n\n3. During execution:\n   - Adapt your plan as needed based on new findings\n   - Be willing to simplify the plan if the task turns out simpler than expected\n   - Always complete your plan before providing final answers\n</planning_approach>\n\n<planning_constraints>\nIMPORTANT CONSTRAINTS:\n- Create AT MOST 3 key steps in your plan\n- Focus on information gathering and research steps\n- Call the \"final_answer\" tool once ALL plan steps are complete\n- For simple questions, you can skip planning entirely\n</planning_constraints>\n \n\n \n<browser_rules>\nYou have access to various browser tools to interact with web pages and extract information.\n\n- Must use browser tools to access and comprehend all URLs provided by users in messages\n- Must use browser tools to access URLs from search tool results\n- Actively explore valuable links for deeper information, either by clicking elements or accessing URLs directly\n- Browser tools only return elements in visible viewport by default\n- Due to technical limitations, not all interactive elements may be identified; use coordinates to interact with unlisted elements\n- Browser tools automatically attempt to extract page content, providing it in Markdown format if successful\n- Extracted Markdown includes text beyond viewport but omits links and images; completeness not guaranteed\n- If extracted Markdown is complete and sufficient for the task, no scrolling is needed; otherwise, must actively scroll to view the entire page\n- Use message tools to suggest user to take over the browser for sensitive operations or actions with side effects when necessary\n</browser_rules>\n\n<envirnoment>\nCurrent Working Directory: /Users/<USER>/TARS-Agent/workspace\n</envirnoment>\n\n    \n\nCurrent time: 5/20/2025, 10:00:00 AM"}, {"role": "assistant", "content": "Aggent TARS is finished"}, {"role": "user", "content": "帮我下载安装达芬奇最新版"}, {"role": "user", "content": [{"type": "text", "text": "[Environment: Browser Screenshot]"}, {"type": "image_url", "image_url": {"url": "data:image/webp;base64,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"}}]}, {"role": "system", "content": "Current plan status:\n1. [TODO] Visit the official Blackmagic Design website (https://www.blackmagicdesign.com/products/davinciresolve) to locate the download page for DaVinci Resolve.\n2. [TODO] Identify the latest version of DaVinci Resolve available for download and check the system requirements to ensure compatibility with the user's operating system (Windows, macOS, or Linux).\n3. [TODO] Initiate the download process by selecting the appropriate installer for the user's operating system and follow the on - screen instructions for installation after the download is complete.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "用户需要下载安装达芬奇最新版，第一步要访问官网。现在要使用browser_navigate工具导航到官网地址。", "tool_calls": [{"id": "call_w9esourm2ehala0rcvr93k7r", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.blackmagicdesign.com/products/davinciresolve\"}"}}]}, {"role": "tool", "tool_call_id": "call_w9esourm2ehala0rcvr93k7r", "content": "{\n  \"status\": \"success\",\n  \"url\": \"https://www.blackmagicdesign.com/products/davinciresolve\",\n  \"message\": \"Navigated success\"\n}"}, {"role": "user", "content": [{"type": "text", "text": "[Environment: Browser Screenshot]"}, {"type": "image_url", "image_url": {"url": "data:image/webp;base64,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*****************************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"}}]}, {"role": "system", "content": "Current plan status:\n1. [TODO] Visit the official Blackmagic Design website (https://www.blackmagicdesign.com/products/davinciresolve) to locate the download page for DaVinci Resolve.\n2. [TODO] Identify the latest version of DaVinci Resolve available for download and check the system requirements to ensure compatibility with the user's operating system (Windows, macOS, or Linux).\n3. [TODO] Initiate the download process by selecting the appropriate installer for the user's operating system and follow the on - screen instructions for installation after the download is complete.\n\nFollow this plan. If a step is done, move to the next step."}], "tools": [{"type": "function", "function": {"name": "web_search", "description": "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️\n\nSearch the web for information. For best results:\n1) Use CONCISE queries (3-5 words ideal)\n2) Include only ESSENTIAL keywords, not full questions\n3) For complex topics, use multiple simple searches instead of one long query\n4) Focus on specific terms that will appear on relevant pages", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query - MUST BE CONCISE (maximum 7 words)"}, "count": {"type": "number", "description": "Number of results to return (default: 10)"}}, "required": ["query"]}}}, {"type": "function", "function": {"name": "browser_vision_control", "description": "A browser operation tool based on visual understanding, perform the next action to complete the task.\n\n## Action Space\n\nclick(point='<point>x1 y1</point>')            - Click at the specified coordinates\nleft_double(point='<point>x1 y1</point>')      - Double-click at the specified coordinates\nright_single(point='<point>x1 y1</point>')     - Right-click at the specified coordinates\ndrag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') - Drag from start to end point\nhotkey(key='ctrl c')                           - Press keyboard shortcut (use space to separate keys, lowercase)\ntype(content='xxx')                            - Type text content (use \\', \\\", and \\n for special characters)\nscroll(point='<point>x1 y1</point>', direction='down or up or right or left') - Scroll in specified direction\nwait()                                         - Wait 5 seconds and take a screenshot to check for changes\n\n## Note\n- Folow user lanuage in in `thought` part.\n- Describe your thought in `step` part.\n- Describe your action in `Step` part.\n- Extract the data your see in `pageData` part.\n- This tool is for operational tasks, not for collect information.\n", "parameters": {"type": "object", "properties": {"thought": {"type": "string", "description": "Your observation and small plan in one sentence, DO NOT include \" characters to avoid failure to render in JSON"}, "step": {"type": "string", "description": "Finally summarize the next action (with its target element) in one sentence"}, "action": {"type": "string", "description": "Some action in action space like click or press"}}, "required": ["thought", "step", "action"]}}}, {"type": "function", "function": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to navigate to"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "browser_go_back", "description": "[browser] Go back to the previous page, or close tab if no history exists", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_go_forward", "description": "[browser] Go forward to the next page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_refresh", "description": "[browser] Refresh the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_get_markdown", "description": "[browser] Get the content of the current page as markdown with pagination support", "parameters": {"type": "object", "properties": {"page": {"type": "number", "description": "Page number to extract (default: 1), in most cases, you do not need to pass this parameter."}}}}}, {"type": "function", "function": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call.", "parameters": {"type": "object", "properties": {"area": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Optional area to capture as [x1, y1, x2, y2]. If not provided, captures the entire viewport."}}}}}, {"type": "function", "function": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_form_input_fill", "description": "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "index": {"type": "number", "description": "Index of the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_select", "description": "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to select"}, "selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_hover", "description": "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to hover"}, "selector": {"type": "string", "description": "CSS selector for element to hover"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_evaluate", "description": "[browser] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_get_clickable_elements", "description": "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_read_links", "description": "[browser] Get all links on the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_scroll", "description": "[browser] Scroll the page", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "Pixels to scroll (positive for down, negative for up), if the amount is not provided, scroll to the bottom of the page"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_tab_list", "description": "[browser] Get the list of tabs", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_new_tab", "description": "[browser] Open a new tab", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to open in the new tab"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_close_tab", "description": "[browser] Close the current tab", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_switch_tab", "description": "[browser] Switch to a specific tab", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Tab index to switch to"}}, "required": ["index"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_press_key", "description": "[browser] Press a key on the keyboard", "parameters": {"type": "object", "properties": {"key": {"type": "string", "enum": ["Enter", "Tab", "Escape", "Backspace", "Delete", "Insert", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "PageUp", "PageDown", "Home", "End", "ShiftLeft", "ShiftRight", "ControlLeft", "ControlRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "CapsLock", "PrintScreen", "ScrollLock", "Pause", "ContextMenu"], "description": "Name of the key to press or a character to generate, such as <PERSON><PERSON>, Tab, Escape, Backspace, Delete, Insert, F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12, ArrowLeft, ArrowRight, ArrowUp, ArrowDown, PageUp, PageDown, Home, End, ShiftLeft, ShiftRight, ControlLeft, ControlRight, AltLeft, AltRight, MetaLeft, MetaRight, CapsLock, PrintScreen, ScrollLock, Pause, ContextMenu"}}, "required": ["key"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_file", "description": "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_multiple_files", "description": "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "edit_file", "description": "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "create_directory", "description": "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_directory", "description": "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "directory_tree", "description": "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "move_file", "description": "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "search_files", "description": "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "get_file_info", "description": "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_allowed_directories", "description": "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_command", "description": "[commands] Run a command on this darwin machine", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "Command with args"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["command"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_script", "description": "[commands] Run a script on this darwin machine", "parameters": {"type": "object", "properties": {"interpreter": {"type": "string", "description": "Command with arguments. <PERSON><PERSON><PERSON> will be piped to stdin. Examples: bash, fish, zsh, python, or: bash --norc"}, "script": {"type": "string", "description": "<PERSON><PERSON><PERSON> to run"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "final_answer", "description": "Generate a focused report or answer after completing research or information gathering", "parameters": {"type": "object", "properties": {"isDeepResearch": {"type": "boolean", "description": "Whether to generate a structured report (true) or simple answer (false)"}, "title": {"type": "string", "description": "Title for the report or answer"}, "format": {"type": "string", "enum": ["detailed", "concise"], "description": "Report format: detailed or concise"}}}}}], "temperature": 0.7, "stream": true, "max_tokens": 8192, "provider": "volcengine", "thinking": {"type": "disabled"}}, "baseURL": "https://ark.cn-beijing.volces.com/api/v3"}