#!/bin/bash
# setup_backup_scripts.sh
# 备份脚本安装和配置脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🔧 视频编辑备份脚本安装程序${NC}"
echo "=================================="

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 备份脚本列表
BACKUP_SCRIPTS=(
    "Video_Editing_Complete_Backup.sh"
    "Quick_DaVinci_Backup.sh"
    "System_Settings_Backup.sh"
    "Adobe_Creative_Backup.sh"
    "Master_Backup_Controller.sh"
)

echo -e "${BLUE}1. 检查脚本文件...${NC}"
missing_scripts=()
for script in "${BACKUP_SCRIPTS[@]}"; do
    if [ -f "$SCRIPT_DIR/$script" ]; then
        echo -e "${GREEN}✅ $script${NC}"
    else
        echo -e "${RED}❌ $script (缺失)${NC}"
        missing_scripts+=("$script")
    fi
done

if [ ${#missing_scripts[@]} -gt 0 ]; then
    echo -e "${RED}错误：缺少必要的脚本文件${NC}"
    exit 1
fi

echo -e "${BLUE}2. 设置脚本权限...${NC}"
for script in "${BACKUP_SCRIPTS[@]}"; do
    chmod +x "$SCRIPT_DIR/$script"
    echo -e "${GREEN}✅ $script 已设置为可执行${NC}"
done

echo -e "${BLUE}3. 创建桌面快捷方式...${NC}"
# 创建主控制脚本的桌面快捷方式
cat > "/Users/<USER>/Desktop/视频编辑备份中心.command" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
./Master_Backup_Controller.sh
EOF

chmod +x "/Users/<USER>/Desktop/视频编辑备份中心.command"
echo -e "${GREEN}✅ 桌面快捷方式已创建${NC}"

echo -e "${BLUE}4. 创建快速启动别名...${NC}"
# 添加到shell配置文件
SHELL_CONFIG=""
if [ -f "/Users/<USER>/.zshrc" ]; then
    SHELL_CONFIG="/Users/<USER>/.zshrc"
elif [ -f "/Users/<USER>/.bashrc" ]; then
    SHELL_CONFIG="/Users/<USER>/.bashrc"
elif [ -f "/Users/<USER>/.bash_profile" ]; then
    SHELL_CONFIG="/Users/<USER>/.bash_profile"
fi

if [ -n "$SHELL_CONFIG" ]; then
    # 检查是否已经添加过别名
    if ! grep -q "# Video Editing Backup Scripts" "$SHELL_CONFIG"; then
        cat >> "$SHELL_CONFIG" << EOF

# Video Editing Backup Scripts
alias backup-center='cd "$SCRIPT_DIR" && ./Master_Backup_Controller.sh'
alias backup-davinci='cd "$SCRIPT_DIR" && ./Quick_DaVinci_Backup.sh'
alias backup-adobe='cd "$SCRIPT_DIR" && ./Adobe_Creative_Backup.sh'
alias backup-system='cd "$SCRIPT_DIR" && ./System_Settings_Backup.sh'
alias backup-all='cd "$SCRIPT_DIR" && ./Video_Editing_Complete_Backup.sh'
EOF
        echo -e "${GREEN}✅ Shell别名已添加到 $SHELL_CONFIG${NC}"
        echo -e "${YELLOW}重启终端后可使用以下命令：${NC}"
        echo "  backup-center  - 打开备份中心"
        echo "  backup-davinci - DaVinci快速备份"
        echo "  backup-adobe   - Adobe套件备份"
        echo "  backup-system  - 系统设置备份"
        echo "  backup-all     - 完整备份"
    else
        echo -e "${YELLOW}⚠️ Shell别名已存在，跳过添加${NC}"
    fi
fi

echo -e "${BLUE}5. 创建定时备份配置...${NC}"
# 创建launchd plist文件用于定时备份
PLIST_DIR="/Users/<USER>/Library/LaunchAgents"
mkdir -p "$PLIST_DIR"

cat > "$PLIST_DIR/com.videoediting.backup.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.videoediting.backup</string>
    <key>ProgramArguments</key>
    <array>
        <string>$SCRIPT_DIR/Quick_DaVinci_Backup.sh</string>
    </array>
    <key>StartCalendarInterval</key>
    <dict>
        <key>Hour</key>
        <integer>18</integer>
        <key>Minute</key>
        <integer>0</integer>
    </dict>
    <key>RunAtLoad</key>
    <false/>
</dict>
</plist>
EOF

echo -e "${GREEN}✅ 定时备份配置已创建（每天18:00执行DaVinci备份）${NC}"
echo -e "${YELLOW}如需启用定时备份，请运行：${NC}"
echo "  launchctl load $PLIST_DIR/com.videoediting.backup.plist"

echo -e "${BLUE}6. 创建使用说明...${NC}"
cat > "$SCRIPT_DIR/README.md" << 'EOF'
# 视频编辑备份脚本使用说明

## 🚀 快速开始

### 方法1：使用桌面快捷方式
双击桌面上的 `视频编辑备份中心.command` 文件

### 方法2：使用终端命令
```bash
backup-center    # 打开备份中心主界面
backup-davinci   # 快速备份DaVinci Resolve
backup-adobe     # 备份Adobe Creative Suite
backup-system    # 备份macOS系统设置
backup-all       # 完整备份所有内容
```

## 📋 脚本功能

### 1. Master_Backup_Controller.sh
- 主控制界面，统一管理所有备份操作
- 支持选择性备份和恢复向导
- 备份历史查看和清理功能

### 2. Quick_DaVinci_Backup.sh
- DaVinci Resolve专用快速备份
- 备份项目文件、配置、LUT、模板
- 自动生成恢复脚本

### 3. Adobe_Creative_Backup.sh
- Adobe Creative Suite完整备份
- 支持Premiere Pro, After Effects, Photoshop, Illustrator, Audition
- 备份项目文件、预设、插件设置

### 4. System_Settings_Backup.sh
- macOS系统设置专用备份
- 备份系统偏好、键盘快捷键、网络设置
- 包含开发环境和Shell配置

### 5. Video_Editing_Complete_Backup.sh
- 所有视频编辑软件的完整备份
- 一次性备份DaVinci、Adobe、Final Cut Pro
- 包含系统设置和开发环境

## ⚙️ 定时备份

启用每日自动备份：
```bash
launchctl load ~/Library/LaunchAgents/com.videoediting.backup.plist
```

停用自动备份：
```bash
launchctl unload ~/Library/LaunchAgents/com.videoediting.backup.plist
```

## 🔄 恢复数据

每个备份都包含对应的恢复脚本：
- `restore.sh` - DaVinci Resolve恢复
- `restore_adobe.sh` - Adobe套件恢复
- `restore_system_settings.sh` - 系统设置恢复

## 📁 备份位置

默认备份到桌面：
- DaVinci: `~/Desktop/DaVinci_Quick_Backup_YYYYMMDD_HHMMSS/`
- Adobe: `~/Desktop/Adobe_Creative_Backup_YYYYMMDD_HHMMSS/`
- 系统: `~/Desktop/System_Settings_Backup_YYYYMMDD_HHMMSS/`
- 完整: `~/Desktop/Video_Editing_Backups/Complete_Backup_YYYYMMDD_HHMMSS/`

## ⚠️ 注意事项

1. 备份前建议关闭相关应用程序
2. 确保有足够的磁盘空间（建议20GB以上）
3. 重要数据建议额外备份到外部存储
4. 系统级操作可能需要管理员权限

## 🆘 故障排除

如果脚本无法执行：
```bash
chmod +x *.sh
```

如果权限不足：
```bash
sudo chown -R $USER:staff /path/to/backup/scripts
```
EOF

echo -e "${GREEN}✅ 使用说明已创建${NC}"

echo -e "${BLUE}7. 验证安装...${NC}"
echo "测试主控制脚本..."
if [ -x "$SCRIPT_DIR/Master_Backup_Controller.sh" ]; then
    echo -e "${GREEN}✅ 主控制脚本可执行${NC}"
else
    echo -e "${RED}❌ 主控制脚本不可执行${NC}"
fi

echo
echo -e "${GREEN}🎉 安装完成！${NC}"
echo "=================================="
echo -e "${YELLOW}使用方法：${NC}"
echo "1. 双击桌面上的 '视频编辑备份中心.command'"
echo "2. 或在终端中运行 'backup-center'"
echo "3. 查看 README.md 了解详细使用说明"
echo
echo -e "${BLUE}立即启动备份中心？[y/N]: ${NC}"
read -r launch_now

if [[ $launch_now =~ ^[Yy]$ ]]; then
    "$SCRIPT_DIR/Master_Backup_Controller.sh"
fi
