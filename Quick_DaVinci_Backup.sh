#!/bin/bash
# Quick_DaVinci_Backup.sh
# DaVinci Resolve 快速备份脚本 - 专注于项目和配置

set -e

# 配置
BACKUP_DIR="/Users/<USER>/Desktop/DaVinci_Quick_Backup_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$BACKUP_DIR/backup.log"

# 颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "[$(date '+%H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

echo -e "${BLUE}🎬 DaVinci Resolve 快速备份开始...${NC}"

# 创建备份目录
mkdir -p "$BACKUP_DIR"/{Projects,Config,Database}

# 1. 备份项目数据（最重要）
log "${BLUE}📁 备份项目数据...${NC}"
if [ -d "/Users/<USER>/Documents/2024_剪辑工程/Valley" ]; then
    rsync -av --progress "/Users/<USER>/Documents/2024_剪辑工程/Valley/" "$BACKUP_DIR/Projects/Valley/" 2>&1 | tee -a "$LOG_FILE"
    log "${GREEN}✅ Valley项目库备份完成${NC}"
fi

if [ -d "/Users/<USER>/Documents/2024_剪辑工程/2024_达芬奇工程" ]; then
    rsync -av --progress "/Users/<USER>/Documents/2024_剪辑工程/2024_达芬奇工程/" "$BACKUP_DIR/Projects/2024_Projects/" 2>&1 | tee -a "$LOG_FILE"
    log "${GREEN}✅ 2024项目备份完成${NC}"
fi

# 2. 备份关键配置文件
log "${BLUE}⚙️ 备份配置文件...${NC}"
PREFS_DIR="/Users/<USER>/Library/Preferences/Blackmagic Design/DaVinci Resolve"
if [ -d "$PREFS_DIR" ]; then
    cp -R "$PREFS_DIR" "$BACKUP_DIR/Config/User_Preferences/" 2>/dev/null
    log "${GREEN}✅ 用户偏好设置备份完成${NC}"
fi

APP_SUPPORT="/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve"
if [ -d "$APP_SUPPORT" ]; then
    # 只备份重要的配置，跳过缓存
    mkdir -p "$BACKUP_DIR/Config/App_Support"
    cp -R "$APP_SUPPORT/configs" "$BACKUP_DIR/Config/App_Support/" 2>/dev/null || true
    cp -R "$APP_SUPPORT/LUT" "$BACKUP_DIR/Config/App_Support/" 2>/dev/null || true
    cp -R "$APP_SUPPORT/Fusion" "$BACKUP_DIR/Config/App_Support/" 2>/dev/null || true
    cp -R "$APP_SUPPORT/Fairlight" "$BACKUP_DIR/Config/App_Support/" 2>/dev/null || true
    log "${GREEN}✅ 应用支持文件备份完成${NC}"
fi

# 3. 备份数据库配置
log "${BLUE}🗄️ 备份数据库配置...${NC}"
if [ -f "$PREFS_DIR/dblist.conf" ]; then
    cp "$PREFS_DIR/dblist.conf" "$BACKUP_DIR/Database/" 2>/dev/null
fi
if [ -f "$PREFS_DIR/activedb.conf" ]; then
    cp "$PREFS_DIR/activedb.conf" "$BACKUP_DIR/Database/" 2>/dev/null
fi
log "${GREEN}✅ 数据库配置备份完成${NC}"

# 4. 统计信息
PROJECT_COUNT=$(find "$BACKUP_DIR/Projects" -name "*.resolve" 2>/dev/null | wc -l)
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)

# 5. 创建恢复脚本
cat > "$BACKUP_DIR/restore.sh" << 'EOF'
#!/bin/bash
echo "🔄 DaVinci Resolve 快速恢复..."
BACKUP_DIR="$(dirname "$0")"

# 恢复项目数据
if [ -d "$BACKUP_DIR/Projects/Valley" ]; then
    rsync -av "$BACKUP_DIR/Projects/Valley/" "/Users/<USER>/Documents/2024_剪辑工程/Valley/"
    echo "✅ Valley项目恢复完成"
fi

if [ -d "$BACKUP_DIR/Projects/2024_Projects" ]; then
    rsync -av "$BACKUP_DIR/Projects/2024_Projects/" "/Users/<USER>/Documents/2024_剪辑工程/2024_达芬奇工程/"
    echo "✅ 2024项目恢复完成"
fi

# 恢复配置
if [ -d "$BACKUP_DIR/Config/User_Preferences" ]; then
    cp -R "$BACKUP_DIR/Config/User_Preferences/" "/Users/<USER>/Library/Preferences/Blackmagic Design/DaVinci Resolve"
    echo "✅ 用户配置恢复完成"
fi

if [ -d "$BACKUP_DIR/Config/App_Support" ]; then
    cp -R "$BACKUP_DIR/Config/App_Support/"* "/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/"
    echo "✅ 应用支持文件恢复完成"
fi

echo "🎉 DaVinci Resolve 恢复完成！请重启应用程序"
EOF

chmod +x "$BACKUP_DIR/restore.sh"

# 6. 生成备份信息
cat > "$BACKUP_DIR/backup_info.txt" << EOF
DaVinci Resolve 快速备份
======================
备份时间: $(date)
项目数量: $PROJECT_COUNT
备份大小: $BACKUP_SIZE
恢复方法: 运行 restore.sh

包含内容:
✅ Valley项目库 (主要项目)
✅ 2024达芬奇工程
✅ 用户偏好设置
✅ LUT文件
✅ Fusion模板
✅ Fairlight设置
✅ 数据库配置
EOF

echo -e "${GREEN}🎉 DaVinci Resolve 快速备份完成！${NC}"
echo -e "${YELLOW}📍 备份位置: $BACKUP_DIR${NC}"
echo -e "${YELLOW}📊 项目数量: $PROJECT_COUNT 个${NC}"
echo -e "${YELLOW}💾 备份大小: $BACKUP_SIZE${NC}"

# 打开备份目录
open "$BACKUP_DIR"
